# JobbLogg Staging Overrides
# Use: docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
# This file contains ONLY staging-specific overrides

version: '3.8'

services:
  # Convex service completely removed for staging
  # Staging uses frontend-only mode with direct Convex cloud connection
  # No convex service definition needed since frontend depends_on is removed

  # Frontend Staging Overrides
  frontend:
    build:
      target: production
    environment:
      - NODE_ENV=staging
      - COMPOSE_ENV=staging
      - BUILD_TARGET=production
      - VITE_ALLOW_INDEXING=false  # Prevent search engine indexing
    ports:
      - "5175:5173"  # Staging frontend port (matches Caddy config)
    env_file:
      - .env.staging
    volumes: []  # Remove development hot-reload volumes
    depends_on: []  # Explicitly remove all dependencies for staging (empty array)
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# NOTE: Nginx and Traefik services removed - Caddy is used as reverse proxy
# Caddy configuration is managed at system level (/etc/caddy/Caddyfile)
# Staging routing:
# - staging.jobblogg.no -> localhost:5175 (frontend)
# - Basic auth handled by <PERSON>addy (if configured)

# NOTE: Health check service removed - use built-in frontend health check instead
