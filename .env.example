# JobbLogg Environment Variables Template
# Copy this file to .env for development, .env.staging for staging, .env.production for production
# DO NOT commit actual values to version control

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=development
COMPOSE_ENV=dev
BUILD_TARGET=development

# =============================================================================
# PORT CONFIGURATION
# =============================================================================
FRONTEND_PORT=5173
CONVEX_PORT=3210

# =============================================================================
# CONVEX BACKEND CONFIGURATION
# =============================================================================
# Convex deployment URL (format: env:deployment-name)
CONVEX_DEPLOYMENT=dev:your-deployment-name

# Convex API URL
CONVEX_URL=https://your-deployment.convex.cloud
VITE_CONVEX_URL=https://your-deployment.convex.cloud

# =============================================================================
# CLERK AUTHENTICATION
# =============================================================================
# Clerk publishable key (starts with pk_test_ or pk_live_)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# =============================================================================
# GOOGLE MAPS INTEGRATION
# =============================================================================
# Google Maps API key for address lookup and maps
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# =============================================================================
# STRIPE PAYMENT PROCESSING
# =============================================================================
# Stripe publishable key (starts with pk_test_ or pk_live_)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Stripe secret key (starts with sk_test_ or sk_live_) - SERVER ONLY
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here

# Stripe webhook secret (starts with whsec_) - SERVER ONLY
# For local development, use Stripe CLI: stripe listen --forward-to localhost:5173/api/webhooks/stripe
# This will give you a temporary whsec_ secret for local testing
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# =============================================================================
# EMAIL SERVICE (RESEND)
# =============================================================================
# Resend API key for email notifications - SERVER ONLY
RESEND_API_KEY=re_your_resend_api_key_here

# =============================================================================
# SEO AND INDEXING
# =============================================================================
# Allow search engines to index this environment (true for production, false for staging)
VITE_ALLOW_INDEXING=true
