# JobbLogg Base Docker Compose Configuration
# This file contains the base service definitions shared across all environments
# Use docker-compose.override.yml for development overrides
# Use -f docker-compose.yml -f docker-compose.prod.yml for production
# Use -f docker-compose.yml -f docker-compose.staging.yml for staging

# version: '3.8'  # Removed to avoid Docker Compose v2 warnings

services:
  # Convex Backend Service - Base Configuration
  convex:
    build:
      context: .
      dockerfile: docker/convex/Dockerfile
    container_name: jobblogg-convex-${COMPOSE_ENV:-dev}
    restart: unless-stopped
    ports:
      - "${CONVEX_PORT:-3210}:3210"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - CONVEX_DEPLOYMENT=${CONVEX_DEPLOYMENT}
      - CONVEX_URL=${CONVEX_URL}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - RESEND_API_KEY=${RESEND_API_KEY}
    volumes:
      - ./convex:/app/convex:ro
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./tsconfig.node.json:/app/tsconfig.node.json:ro
      - convex_data_${COMPOSE_ENV:-dev}:/app/data
    networks:
      - jobblogg-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3210/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Service - Base Configuration
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
      target: ${BUILD_TARGET:-development}
    container_name: jobblogg-frontend-${COMPOSE_ENV:-dev}
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-5173}:5173"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_CONVEX_URL=${VITE_CONVEX_URL}
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
      - VITE_GOOGLE_MAPS_API_KEY=${VITE_GOOGLE_MAPS_API_KEY}
      - VITE_STRIPE_PUBLISHABLE_KEY=${VITE_STRIPE_PUBLISHABLE_KEY}
      - VITE_ALLOW_INDEXING=${VITE_ALLOW_INDEXING:-true}
    volumes:
      - ./src:/app/src:ro
      - ./public:/app/public:ro
      - ./index.html:/app/index.html:ro
      - ./vite.config.ts:/app/vite.config.ts:ro
      - ./tailwind.config.js:/app/tailwind.config.js:ro
      - ./postcss.config.js:/app/postcss.config.js:ro
    networks:
      - jobblogg-network
    depends_on:
      convex:
        condition: service_healthy

volumes:
  convex_data_dev:
    driver: local
  convex_data_staging:
    driver: local
  convex_data_prod:
    driver: local

networks:
  jobblogg-network:
    driver: bridge
    name: jobblogg-${COMPOSE_ENV:-dev}-network
