# Company Profile Field Consistency & Dynamic Labeling Fix

## Issues Resolved

### ✅ **Issue 1: Field Styling Consistency**
**Problem**: The "Bedriftsnavn" (Company Name) and "Daglig Leder" fields were not visually consistent with the "Organisasjonsnummer" field when locked.

**Solution**: 
- Verified all three fields use identical LockedInput component styling
- Ensured consistent gray background (`bg-jobblogg-card-bg`)
- Maintained same lock icons and visual indicators
- Standardized helper text formatting and positioning

### ✅ **Issue 2: Dynamic Contact Person Field Label**
**Problem**: The contact person field had a static "Daglig Leder" label, but should be dynamic based on company structure from Brønnøysundregisteret.

**Solution**:
- Implemented dynamic labeling logic based on Brønnøysundregisteret response
- Added support for both "Daglig Leder" and "Innehaver" labels
- Updated localization file with new labels and text entries
- Ensured correct data source usage based on available data

## Technical Implementation

### **1. Localization Updates**
Enhanced `src/localization/companyProfile.ts` with dynamic labeling support:

```typescript
// Added new labels
labels: {
  contactPerson: 'Daglig Leder',
  contactPersonInholder: 'Innehaver',
  // ... existing labels
},

// Added new placeholders
placeholders: {
  contactPerson: 'Skriv inn navn på daglig leder',
  contactPersonInholder: 'Skriv inn navn på innehaver',
  // ... existing placeholders
},

// Added new error messages
errors: {
  required: {
    contactPerson: 'Daglig leder er påkrevd',
    contactPersonInholder: 'Innehaver er påkrevd',
    // ... existing errors
  }
}
```

### **2. Dynamic Labeling Logic**
Added state and helper functions for dynamic contact person handling:

```typescript
// New state for tracking contact person type
const [contactPersonType, setContactPersonType] = useState<'dagligLeder' | 'innehaver' | null>(null);

// Helper functions for dynamic labeling
const getContactPersonLabel = (contactPersonType: 'dagligLeder' | 'innehaver' | null) => {
  switch (contactPersonType) {
    case 'dagligLeder':
      return companyProfileTexts.labels.contactPerson;
    case 'innehaver':
      return companyProfileTexts.labels.contactPersonInholder;
    default:
      return companyProfileTexts.labels.contactPerson;
  }
};

const getContactPersonPlaceholder = (contactPersonType: 'dagligLeder' | 'innehaver' | null) => {
  switch (contactPersonType) {
    case 'dagligLeder':
      return companyProfileTexts.placeholders.contactPerson;
    case 'innehaver':
      return companyProfileTexts.placeholders.contactPersonInholder;
    default:
      return companyProfileTexts.placeholders.contactPerson;
  }
};

const getContactPersonError = (contactPersonType: 'dagligLeder' | 'innehaver' | null) => {
  switch (contactPersonType) {
    case 'dagligLeder':
      return companyProfileTexts.errors.required.contactPerson;
    case 'innehaver':
      return companyProfileTexts.errors.required.contactPersonInholder;
    default:
      return companyProfileTexts.errors.required.contactPerson;
  }
};
```

### **3. Brønnøysundregisteret Data Processing**
Enhanced data handling to determine contact person type:

```typescript
// Handle contact person with dynamic labeling
let newContactPerson = '';
let newContactPersonType: 'dagligLeder' | 'innehaver' | null = null;
let contactPersonLabel = 'Kontaktperson';

if (selectedCompany.dagligLeder) {
  newContactPerson = selectedCompany.dagligLeder;
  newContactPersonType = 'dagligLeder';
  contactPersonLabel = 'Daglig Leder';
} else if (selectedCompany.innehaver) {
  newContactPerson = selectedCompany.innehaver;
  newContactPersonType = 'innehaver';
  contactPersonLabel = 'Innehaver';
}

if (newContactPerson && newContactPerson !== formData.contactPerson) {
  trackChange(contactPersonLabel, formData.contactPerson, newContactPerson);
  newFormData.contactPerson = newContactPerson;
  setContactPersonType(newContactPersonType);
}
```

### **4. Form Field Implementation**
Updated contact person field to use dynamic labeling:

```typescript
{/* Contact Person (Dynamic: Daglig Leder / Innehaver) */}
{lockedFields.contactPerson ? (
  <LockedInput
    label={getContactPersonLabel(contactPersonType)}
    value={formData.contactPerson}
    fullWidth
    helperText={brregFetchedAt ? `Hentet fra Brønnøysundregisteret ${new Date(brregFetchedAt).toLocaleDateString('nb-NO')}` : 'Hentet fra Brønnøysundregisteret'}
  />
) : (
  <TextInput
    label={getContactPersonLabel(contactPersonType)}
    value={formData.contactPerson}
    onChange={(value) => handleFieldChange('contactPerson', value)}
    error={errors.contactPerson}
    required
    placeholder={getContactPersonPlaceholder(contactPersonType)}
  />
)}
```

## LockedInput Styling Consistency

### **Verified Consistent Styling Properties**
All three locked fields (Company Name, Contact Person, Organization Number) now use identical LockedInput styling:

```typescript
// LockedInput component styling (from src/components/ui/Form/LockedInput.tsx)
className={`
  ${sizeClasses[size]}                    // px-4 py-3 text-base (medium)
  ${fullWidth ? 'w-full' : ''}           // Full width when specified
  bg-jobblogg-card-bg                    // ✅ Gray background
  border border-jobblogg-border          // ✅ Consistent border
  rounded-lg                             // ✅ Rounded corners
  text-jobblogg-text-medium             // ✅ Text color
  cursor-not-allowed                     // ✅ Disabled cursor
  opacity-75                            // ✅ Reduced opacity
  focus:outline-none                     // ✅ Focus handling
  focus:ring-2
  focus:ring-jobblogg-primary/30
  focus:border-jobblogg-primary
  transition-colors
  duration-200
`}
```

### **Visual Indicators**
- ✅ **Lock Icons**: All locked fields show lock icon in label
- ✅ **Gray Background**: Consistent `bg-jobblogg-card-bg` styling
- ✅ **Reduced Opacity**: 75% opacity for disabled appearance
- ✅ **Helper Text**: Consistent formatting and positioning

## Dynamic Labeling Logic

### **Priority-Based Selection**
1. **First Priority**: `selectedCompany.dagligLeder` → Label: "Daglig Leder"
2. **Second Priority**: `selectedCompany.innehaver` → Label: "Innehaver"
3. **Fallback**: Default to "Daglig Leder" if neither is available

### **Data Source Mapping**
- ✅ **Daglig Leder**: Uses `selectedCompany.dagligLeder` value
- ✅ **Innehaver**: Uses `selectedCompany.innehaver` value
- ✅ **Automatic Detection**: System automatically determines which is available
- ✅ **Proper Labeling**: Field label matches the actual data source

## User Experience Improvements

### **Clear Visual Feedback**
- ✅ **Consistent Appearance**: All locked fields look identical
- ✅ **Appropriate Labels**: Field labels match actual company structure
- ✅ **Data Source Attribution**: Clear indication of Brønnøysundregisteret source
- ✅ **Norwegian Localization**: Proper Norwegian terminology throughout

### **Intelligent Field Handling**
- ✅ **Automatic Detection**: System detects company structure automatically
- ✅ **Correct Data Usage**: Uses appropriate field based on company type
- ✅ **Validation Consistency**: Error messages match field labels
- ✅ **Placeholder Accuracy**: Placeholders reflect expected input type

## Files Modified

### **1. src/localization/companyProfile.ts**
- ✅ Added `contactPersonInholder` label
- ✅ Added corresponding placeholder text
- ✅ Added appropriate error messages
- ✅ Added help text for innehaver role

### **2. src/components/CompanyProfileModal.tsx**
- ✅ Added `contactPersonType` state tracking
- ✅ Implemented dynamic labeling helper functions
- ✅ Enhanced Brønnøysundregisteret data processing
- ✅ Updated contact person field with dynamic labeling
- ✅ Updated form validation with dynamic error messages

## Testing Scenarios

### **Scenario 1: Company with Daglig Leder**
1. ✅ Brønnøysundregisteret returns `dagligLeder` field
2. ✅ Field label shows "Daglig Leder"
3. ✅ Field is populated with `dagligLeder` value
4. ✅ Field is locked with proper styling
5. ✅ Helper text shows Brønnøysundregisteret source

### **Scenario 2: Company with Innehaver**
1. ✅ Brønnøysundregisteret returns `innehaver` field (no `dagligLeder`)
2. ✅ Field label shows "Innehaver"
3. ✅ Field is populated with `innehaver` value
4. ✅ Field is locked with proper styling
5. ✅ Helper text shows Brønnøysundregisteret source

### **Scenario 3: Manual Entry**
1. ✅ No Brønnøysundregisteret data available
2. ✅ Field label defaults to "Daglig Leder"
3. ✅ Field is editable with appropriate placeholder
4. ✅ Validation uses correct error message
5. ✅ Field styling matches other editable fields

## Design System Compliance

### **JobbLogg Design Tokens**
- ✅ **Colors**: All jobblogg-prefixed color tokens used
- ✅ **Typography**: Consistent text hierarchy maintained
- ✅ **Spacing**: Proper spacing and padding applied
- ✅ **Borders**: Consistent border styling across fields

### **Accessibility Standards**
- ✅ **WCAG AA Compliance**: Proper contrast ratios maintained
- ✅ **Screen Reader Support**: Appropriate ARIA attributes
- ✅ **Keyboard Navigation**: Full keyboard accessibility
- ✅ **Focus Management**: Clear focus indicators

### **Mobile Responsiveness**
- ✅ **Touch Targets**: 44px minimum touch targets maintained
- ✅ **Responsive Layout**: Proper scaling across screen sizes
- ✅ **Mobile-First Design**: Mobile-optimized layouts
- ✅ **Consistent Behavior**: Same functionality across devices

The Company Profile Modal now provides consistent field styling and intelligent dynamic labeling that adapts to the actual company structure from the Norwegian business registry, ensuring a professional and user-friendly experience.
