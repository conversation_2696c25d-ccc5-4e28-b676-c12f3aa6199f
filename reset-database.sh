#!/bin/bash

# JobbLogg Comprehensive Database Reset Script
# This script safely removes all user-generated data while preserving database schema structure
#
# Updated: 2024-12-19 - Added support for:
# - Team blocking/unblocking data (isBlocked, blockedAt, blockedBy, blockedReason)
# - User activity tracking (lastLoginAt, lastActivityAt)
# - Project assignments and collaboration features
# - Link preview cache (OpenGraph data)
# - Enhanced team management analysis

set -e  # Exit on any error

echo "🚨 JobbLogg Comprehensive Database Reset Utility"
echo "================================================"
echo ""
echo "⚠️  WARNING: This will permanently delete ALL user-generated data!"
echo "⚠️  Including: projects, customers, users, chat messages, contractor data"
echo "⚠️  NEW FEATURES: Team blocking data, user activity tracking, link previews"
echo "⚠️  BUSINESS DATA: Contractor company profiles (bedriftsprofiler) will be deleted!"
echo "⚠️  Only use in development/testing environments!"
echo ""
echo "✅ PRESERVES: Database schema, indexes, system configuration"
echo ""

# Check if we're in the right directory
if [ ! -f "convex/schema.ts" ]; then
    echo "❌ Error: Please run this script from the JobbLogg project root directory"
    exit 1
fi

# Check if Convex CLI is available
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx is not available. Please install Node.js and npm."
    exit 1
fi

# Function to get comprehensive data count
get_data_count() {
    echo "📊 Getting comprehensive database state..."
    npx convex run clearAllProjectData:getProjectDataCount '{}'
    echo ""
}

# Function to analyze contractor company profiles specifically
analyze_contractor_companies() {
    echo "🏢 Analyzing contractor company profiles (bedriftsprofiler)..."
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    npx convex run resetDatabase:analyzeContractorCompanies '{}'
    echo ""
}

# Function to get detailed contractor company information
get_contractor_company_details() {
    echo "🔍 Getting detailed contractor company information..."
    echo "This will show all contractor companies with their business details."
    echo ""
    read -p "Continue with detailed analysis? (y/n): " continue_analysis

    if [ "$continue_analysis" = "y" ] || [ "$continue_analysis" = "Y" ]; then
        npx convex run resetDatabase:getContractorCompanyDetails '{}'
    else
        echo "❌ Detailed analysis cancelled"
    fi
    echo ""
}

# Function to validate contractor company data integrity
validate_contractor_integrity() {
    echo "🔍 Validating contractor company data integrity..."
    echo "This will check for orphaned records, missing data, and relationship issues."
    echo ""
    npx convex run resetDatabase:validateContractorCompanyIntegrity '{}'
    echo ""
}

# Function to analyze team management data
analyze_team_data() {
    echo "👥 Analyzing team management data..."
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "This will show team blocking history, user activity, and collaboration data."
    echo ""

    echo "📊 Getting team member statistics..."
    npx convex run clearAllProjectData:getProjectDataCount '{}'
    echo ""

    echo "🔒 Checking for blocked users..."
    echo "Note: This requires authentication, so it may show limited data from CLI"
    echo ""
}

# Function to run dry-run analysis
dry_run_analysis() {
    echo "🔍 Running dry-run analysis (no data will be deleted)..."
    echo ""
    echo "Analysis options:"
    echo "1) Standard analysis (database records only)"
    echo "2) Include file storage analysis"
    echo "3) Focus on contractor companies only"
    echo ""
    read -p "Choose analysis type (1-3): " analysis_type

    case $analysis_type in
        1)
            npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'
            ;;
        2)
            npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": true}'
            ;;
        3)
            analyze_contractor_companies
            ;;
        *)
            echo "❌ Invalid choice. Running standard analysis..."
            npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'
            ;;
    esac
    echo ""
}

# Function to set administrator role
set_administrator_role() {
    echo "👑 Setting up administrator role..."
    echo "Please enter your email address (the one you use to log into JobbLogg):"
    read -r USER_EMAIL

    if [ -z "$USER_EMAIL" ]; then
        echo "❌ Error: Email address is required"
        return 1
    fi

    echo "🔧 Setting $USER_EMAIL as administrator..."
    npx convex run teamManagement:setUserAsAdministrator "{\"email\": \"$USER_EMAIL\"}"

    if [ $? -eq 0 ]; then
        echo "✅ Administrator role set successfully!"
        echo "🎉 You can now access 'Team administrasjon' in JobbLogg!"
    else
        echo "❌ Failed to set administrator role"
        echo "💡 You can manually set it later using: http://localhost:5173/set-administrator.html"
        return 1
    fi
    echo ""
}

# Function to create test data
create_test_data() {
    echo "🧪 Creating test data..."
    echo "Please enter your Clerk user ID (you can find this in the Convex dashboard):"
    read -r USER_ID

    if [ -z "$USER_ID" ]; then
        echo "❌ Error: User ID is required"
        exit 1
    fi

    npx convex run testDataUtilities:createTestData "{\"confirmationCode\": \"CREATE_TEST_DATA\", \"testUserId\": \"$USER_ID\"}"
    echo ""
}

# Function to clear all data (legacy)
clear_all_data() {
    echo "🗑️  Clearing all data (legacy function)..."
    npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA"}'
    echo ""
}

# Function to comprehensive reset with contractor company confirmation
comprehensive_reset() {
    echo ""
    echo "🔧 COMPREHENSIVE DATABASE RESET OPTIONS:"
    echo "1) Database records only (preserve file storage)"
    echo "2) Database records + file storage (complete wipe)"
    echo ""
    read -p "Choose option (1-2): " storage_choice

    local include_storage="false"
    if [ "$storage_choice" = "2" ]; then
        include_storage="true"
        echo ""
        echo "⚠️  WARNING: This will also delete all images and files from Convex storage!"
        echo "⚠️  This action cannot be undone!"
        echo ""
    fi

    # Show contractor company impact before proceeding
    echo "🏢 CONTRACTOR COMPANY IMPACT ANALYSIS:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    analyze_contractor_companies

    echo "⚠️  CRITICAL: All contractor company profiles (bedriftsprofiler) will be permanently deleted!"
    echo "⚠️  This includes business information, contact details, and Brønnøysundregisteret data!"
    echo "⚠️  Users will need to complete contractor onboarding again after reset!"
    echo ""
    read -p "Type 'DELETE_CONTRACTOR_COMPANIES' to confirm deletion of business data: " contractor_confirm

    if [ "$contractor_confirm" != "DELETE_CONTRACTOR_COMPANIES" ]; then
        echo "❌ Contractor company deletion not confirmed. Operation cancelled."
        return 1
    fi

    echo "🗑️  Executing comprehensive database reset..."
    npx convex run clearAllProjectData:comprehensiveReset "{\"confirmationCode\": \"DELETE_ALL_PROJECT_DATA\", \"dryRun\": false, \"includeFileStorage\": $include_storage}"

    if [ $? -eq 0 ]; then
        echo "✅ Database reset completed!"
        echo ""
        echo "ℹ️  NEXT STEPS:"
        echo "After database reset, you need to:"
        echo "1. Create a new user account in JobbLogg (sign up)"
        echo "2. Complete contractor onboarding to create your company"
        echo "3. Use option 11 in this script to set administrator role"
        echo ""
        echo "💡 TIP: Use option 10 'Full reset + test data' instead for a complete setup!"
    else
        echo "❌ Database reset failed!"
    fi
    echo ""
}

# Main menu
while true; do
    echo "What would you like to do?"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📊 ANALYSIS & INSPECTION:"
    echo "1) Check comprehensive database state"
    echo "2) Dry-run analysis (preview what would be deleted)"
    echo "3) Analyze contractor companies (bedriftsprofiler)"
    echo "4) Get detailed contractor company information"
    echo "5) Validate contractor company data integrity"
    echo "6) Analyze team management data (blocking, activity, assignments)"
    echo ""
    echo "🗑️  DATA MANAGEMENT:"
    echo "7) Comprehensive database reset (recommended)"
    echo "8) Legacy project data clear (backward compatibility)"
    echo ""
    echo "🧪 TESTING:"
    echo "9) Create test data"
    echo "10) Full reset + create test data"
    echo ""
    echo "👑 ADMINISTRATOR SETUP:"
    echo "11) Set administrator role (after database reset)"
    echo ""
    echo "12) Exit"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    read -p "Enter your choice (1-12): " choice

    case $choice in
        1)
            get_data_count
            ;;
        2)
            dry_run_analysis
            ;;
        3)
            analyze_contractor_companies
            ;;
        4)
            get_contractor_company_details
            ;;
        5)
            validate_contractor_integrity
            ;;
        6)
            analyze_team_data
            ;;
        7)
            echo ""
            echo "⚠️  COMPREHENSIVE DATABASE RESET"
            echo "⚠️  This will delete ALL user-generated data:"
            echo "   • All projects (including archived)"
            echo "   • All customers (including contractor companies/bedriftsprofiler)"
            echo "   • All user records (contractor onboarding data)"
            echo "   • All chat messages and reactions"
            echo "   • All log entries and image likes"
            echo "   • All typing indicators"
            echo "   • All project assignments (team collaboration)"
            echo "   • All link previews (OpenGraph cache)"
            echo "   • All user activity tracking data (login/activity timestamps)"
            echo "   • All team blocking data (blocked users, reasons, timestamps)"
            echo "   • Optionally: All file storage (images/attachments)"
            echo ""
            echo "🏢 BUSINESS DATA IMPACT:"
            echo "   • Contractor company profiles will be permanently deleted"
            echo "   • Business information, contact details, org numbers"
            echo "   • Brønnøysundregisteret integration data"
            echo "   • Users will need to re-complete contractor onboarding"
            echo ""
            echo "👥 TEAM MANAGEMENT IMPACT:"
            echo "   • All team member invitations and acceptances"
            echo "   • Team blocking history (who blocked whom, when, why)"
            echo "   • User activity tracking (last login, last activity)"
            echo "   • Project assignments and collaboration data"
            echo ""
            echo "✅ PRESERVES: Database schema, indexes, system configuration"
            echo ""
            echo "ℹ️  AFTER RESET: You'll need to create new user account and set administrator role"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'RESET' to confirm: " confirm

            if [ "$confirm" = "RESET" ]; then
                echo "📊 Current state before reset:"
                get_data_count

                if comprehensive_reset; then
                    echo "✅ Comprehensive database reset completed!"

                    echo "📊 Final state after reset:"
                    get_data_count

                    echo "🎉 Database is now completely clean for fresh testing!"
                else
                    echo "❌ Reset operation was cancelled or failed"
                fi
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        8)
            echo ""
            echo "⚠️  LEGACY PROJECT DATA CLEAR"
            echo "⚠️  This uses the legacy function (may not include all new data types)"
            echo "⚠️  For complete deletion including team blocking data, use option 7 instead"
            echo "This action cannot be undone!"
            read -p "Type 'yes' to confirm: " confirm

            if [ "$confirm" = "yes" ]; then
                clear_all_data
                echo "✅ Legacy project data cleared successfully!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        8)
            echo ""
            echo "⚠️  LEGACY PROJECT DATA CLEAR"
            echo "⚠️  This uses the legacy function (may not include all new data types)"
            echo "⚠️  For complete deletion including team blocking data, use option 7 instead"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'CLEAR' to confirm: " confirm

            if [ "$confirm" = "CLEAR" ]; then
                echo "📊 Current state before clear:"
                get_data_count

                echo ""
                echo "🗑️  Clearing legacy project data..."
                npx convex run clearAllProjectData:clearAllProjectData '{}'
                echo ""

                echo "📊 State after clear:"
                get_data_count
                echo "✅ Legacy project data cleared successfully!"
            else
                echo "❌ Operation cancelled."
            fi
            echo ""
            ;;
        9)
            create_test_data
            echo "✅ Test data created successfully!"
            echo ""
            ;;
        10)
            echo ""
            echo "⚠️  FULL RESET + TEST DATA CREATION"
            echo "⚠️  This will:"
            echo "   1. Delete all existing user data (including contractor companies)"
            echo "   2. Delete all team management data (blocking, activity tracking)"
            echo "   3. Delete all collaboration data (assignments, link previews)"
            echo "   4. Create fresh test data"
            echo ""
            echo "🏢 CONTRACTOR COMPANY IMPACT:"
            echo "   • All contractor company profiles will be deleted"
            echo "   • Test data may include sample contractor companies"
            echo ""
            echo "👥 TEAM MANAGEMENT IMPACT:"
            echo "   • All team blocking history will be cleared"
            echo "   • All user activity tracking will be reset"
            echo "   • All project assignments will be removed"
            echo ""
            echo "✅ INCLUDES: Test data creation + Administrator role setup"
            echo "💡 RECOMMENDED: This option provides a complete working environment"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'RESET' to confirm: " confirm

            if [ "$confirm" = "RESET" ]; then
                echo "📊 Current state before reset:"
                get_data_count

                if comprehensive_reset; then
                    echo "✅ All data cleared!"

                    create_test_data
                    echo "✅ Test data created!"

                    echo ""
                    echo "👑 Setting up administrator role..."
                    set_administrator_role

                    echo "📊 Final state after reset:"
                    get_data_count

                    echo "🎉 Full database reset with test data completed successfully!"
                    echo "🎉 Administrator role configured - you can now access team features!"
                else
                    echo "❌ Reset operation was cancelled or failed"
                fi
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        11)
            echo ""
            echo "👑 ADMINISTRATOR ROLE SETUP"
            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
            echo "This will set you as administrator so you can access 'Team administrasjon' in JobbLogg."
            echo "Use this after database reset or if you don't see team management features."
            echo ""
            set_administrator_role
            ;;
        12)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-12."
            echo ""
            ;;
    esac
done
