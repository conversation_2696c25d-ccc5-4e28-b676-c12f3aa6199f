# JobbLogg Deployment Troubleshooting Guide

## 🚨 Common Deployment Issues & Solutions

### 1. Convex Service Health Check Failures

**Problem**: `jobblogg-convex-prod` service becomes unhealthy, causing deployment rollback.

**Root Cause**: Convex is a cloud service, not a local Docker service. The health check was trying to access a non-existent local endpoint.

**Solution**: ✅ **FIXED**
- Disabled Convex Docker service in production (`replicas: 0`)
- Frontend connects directly to Convex cloud
- Removed Convex dependency from frontend service
- Updated health checks to exclude local Convex service

**Verification**:
```bash
# Check that no Convex container is running in production
docker ps | grep convex

# Should return no results for production deployment
```

### 2. Frontend Dependency Chain Issues

**Problem**: Frontend service fails to start due to `depends_on: service_healthy` waiting for unhealthy Convex service.

**Solution**: ✅ **FIXED**
- Removed `depends_on` for Convex service in production
- Frontend now starts independently
- Uses cloud Convex via environment variables

### 3. TypeScript Build Errors

**Problem**: Production builds fail due to TypeScript errors in test files.

**Solution**: ✅ **FIXED**
- Created `tsconfig.app.prod.json` excluding test files
- Updated build process to use production-specific TypeScript config
- Added fallback `build:no-typecheck` script

### 4. Health Check Endpoints

**Current Configuration**:
- **Production**: `https://jobblogg.no/api/health` (Caddy responds with "OK")
- **Staging**: `https://staging.jobblogg.no/api/health` (Caddy responds with "OK")
- **Development**: `http://localhost:3210/health` (Convex HTTP router)

## 🛠️ Troubleshooting Tools

### Health Check Script
```bash
# Check all services for specific environment
./scripts/health-check.sh prod
./scripts/health-check.sh staging
./scripts/health-check.sh dev
```

### Manual Health Checks
```bash
# Production
curl -f https://jobblogg.no/api/health
curl -f https://jobblogg.no/

# Staging
curl -f https://staging.jobblogg.no/api/health
curl -f https://staging.jobblogg.no/

# Development
curl -f http://localhost:5173/
curl -f http://localhost:3210/health
```

### Docker Service Status
```bash
# Check service health
docker compose -f docker-compose.yml -f docker-compose.prod.yml ps

# View logs for specific service
docker compose -f docker-compose.yml -f docker-compose.prod.yml logs frontend
docker compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=50 frontend

# Check resource usage
docker stats --no-stream | grep jobblogg
```

## 🔧 Deployment Commands

### Production Deployment
```bash
# Using deployment script
./deploy.sh production --build

# Manual deployment
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Check deployment status
./scripts/health-check.sh prod
```

### Staging Deployment
```bash
# Using deployment script
./deploy.sh staging --build

# Manual deployment
docker compose -f docker-compose.staging-only.yml up -d --build

# Check deployment status
./scripts/health-check.sh staging
```

## 📋 Pre-Deployment Checklist

### Environment Configuration
- [ ] `.env.production` file exists with correct values
- [ ] All required secrets are configured
- [ ] Convex deployment URL is correct
- [ ] Clerk keys are for production environment
- [ ] Stripe keys are for live mode

### Build Verification
- [ ] TypeScript production build passes: `npx tsc --project tsconfig.app.prod.json --noEmit`
- [ ] Frontend build succeeds: `npm run build`
- [ ] No critical linting errors: `npm run lint`

### Service Dependencies
- [ ] Convex cloud deployment is accessible
- [ ] Clerk authentication service is configured
- [ ] Stripe webhooks are configured for production domain
- [ ] DNS records point to correct server

## 🚨 Emergency Rollback

If deployment fails and needs immediate rollback:

```bash
# Stop current deployment
docker compose -f docker-compose.yml -f docker-compose.prod.yml down

# Start previous backup container (if available)
docker start jobblogg-frontend-production:backup-$(date +%Y%m%d)

# Or redeploy from last known good commit
git checkout <last-good-commit>
./deploy.sh production --build
```

## 📊 Monitoring & Alerts

### Key Metrics to Monitor
- Frontend container health status
- Response time for health endpoints
- Memory and CPU usage
- Error rates in application logs

### Log Locations
- **Docker logs**: `docker compose logs -f`
- **Caddy logs**: `/var/log/caddy/`
- **System logs**: `journalctl -u docker`

## 🔍 Common Error Messages

### "service frontend depends on undefined service convex"
**Solution**: Convex service is disabled in production. This error should not occur with the fixed configuration.

### "container jobblogg-convex-prod is unhealthy"
**Solution**: Convex service is disabled in production. No Convex container should be running.

### "curl: (7) Failed to connect to localhost port 3210"
**Solution**: Expected in production. Convex runs in the cloud, not locally.

### "Type instantiation is excessively deep"
**Solution**: Use production TypeScript config that excludes problematic files: `tsconfig.app.prod.json`
