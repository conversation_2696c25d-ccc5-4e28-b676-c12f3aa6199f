# JobbLogg - React + TypeScript + Vite

JobbLogg er en moderne prosjektloggingsapplikasjon bygget for norske entreprenører og håndverkere.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 20+ (for local development)
- Git

### Environment Setup
1. Copy environment template:
   ```bash
   cp .env.example .env
   ```
2. Configure your environment variables in `.env`

### Development
```bash
# Start development environment (with hot reload)
./deploy.sh dev

# Or manually:
docker-compose up -d
```

### Staging Deployment
```bash
# Deploy to staging
./deploy.sh staging --build

# Or manually:
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
```

### Production Deployment
```bash
# Deploy to production
./deploy.sh production --pull

# Or manually:
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 📁 Project Structure

### Docker Configuration
- `docker-compose.yml` - Base services configuration
- `docker-compose.override.yml` - Development overrides (auto-loaded)
- `docker-compose.staging.yml` - Staging environment overrides
- `docker-compose.prod.yml` - Production environment overrides

### Environment Files
- `.env.example` - Template with all variables
- `.env` - Development environment (local)
- `.env.staging` - Staging environment (committed)
- `.env.production` - Production environment (server-only)

## 🌐 Access URLs

### Development
- Frontend: http://localhost:5173
- Convex API: http://localhost:3210

### Staging
- Frontend: http://localhost:5175
- Public URL: https://staging.jobblogg.no

### Production
- Frontend: http://localhost:5174
- Public URL: https://jobblogg.no
- API: https://api.jobblogg.no

## 🔧 Infrastructure

### Reverse Proxy
- **Development**: Direct access
- **Staging/Production**: Caddy (system service)
  - Automatic SSL via Let's Encrypt
  - Configuration: `/etc/caddy/Caddyfile`

### Services
- **Frontend**: React + TypeScript + Vite
- **Backend**: Convex (serverless)
- **Database**: Convex (managed)
- **Authentication**: Clerk
- **Payments**: Stripe
- **Email**: Resend

### CI/CD Pipeline
- **GitHub Actions**: 3 workflows (deploy, test, security)
- **Automated Deployment**: Staging on push to main, Production on manual trigger
- **Docker Integration**: Uses standardized compose structure
- **Environment Management**: Automated .env file creation
- **Health Checks**: Automated verification of deployments

## CI/CD Status
- 🔒 Security Scan: ✅ PASSING
- 🧪 Test & Quality Check: ✅ MODERNIZED (Aligned with new infrastructure)
- 📦 Deployment: ✅ OPERATIONAL (Standardized Docker + GitHub Actions)

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...

      // Remove tseslint.configs.recommended and replace with this
      ...tseslint.configs.recommendedTypeChecked,
      // Alternatively, use this for stricter rules
      ...tseslint.configs.strictTypeChecked,
      // Optionally, add this for stylistic rules
      ...tseslint.configs.stylisticTypeChecked,

      // Other configs...
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...
      // Enable lint rules for React
      reactX.configs['recommended-typescript'],
      // Enable lint rules for React DOM
      reactDom.configs.recommended,
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```
