# JobbLogg Development Overrides
# This file is automatically loaded by docker-compose for development
# It provides hot reload, debug settings, and development-specific configurations

version: '3.8'

services:
  # Convex Development Overrides
  convex:
    environment:
      - NODE_ENV=development
      - COMPOSE_ENV=dev
    volumes:
      # Add hot reload volumes for development
      - ./convex:/app/convex
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.node.json:/app/tsconfig.node.json
    command: ["npx", "convex", "dev", "--url", "0.0.0.0:3210", "--typecheck=disable"]

  # Frontend Development Overrides
  frontend:
    build:
      target: development
    environment:
      - NODE_ENV=development
      - COMPOSE_ENV=dev
      - BUILD_TARGET=development
    volumes:
      # Hot reload volumes for development
      - ./src:/app/src
      - ./public:/app/public
      - ./index.html:/app/index.html
      - ./vite.config.ts:/app/vite.config.ts
      - ./tailwind.config.js:/app/tailwind.config.js
      - ./postcss.config.js:/app/postcss.config.js
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./tsconfig.app.json:/app/tsconfig.app.json
      - ./tsconfig.node.json:/app/tsconfig.node.json
