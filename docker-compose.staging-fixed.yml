version: '3.8'

services:
  # Frontend Service (Staging) - Simplified with direct env vars
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
      target: production
    container_name: jobblogg-frontend-staging
    restart: unless-stopped
    ports:
      - "5175:5173"
    environment:
      - NODE_ENV=staging
      # Convex Configuration
      - CONVEX_DEPLOYMENT=dev:enchanted-quail-174
      - VITE_CONVEX_URL=https://enchanted-quail-174.convex.cloud
      # Clerk Authentication
      - VITE_CLERK_PUBLISHABLE_KEY=pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk
      # Google Maps API
      - VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs
      # Resend Email Service
      - RESEND_API_KEY=re_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL
      # Stripe Payment Integration
      - VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51QuHVWRqXwHRnsDwFyefP4DJfEDG9Ti42UkWO7Y5nWmSDZbZtVLWfgDmmAP3YnYYb8905qIhtDDB8UUPLDjaUk9F00snevRBNh
      - STRIPE_SECRET_KEY=sk_test_51QuHVWRqXwHRnsDwtLPJ2Qd310QWPUvfvYKmxE4WPmC6ERPHCGfkdKgZp9xNZs3uPhUzGKQsmqytsgBdnXEClv3u00sKnCLi9T
      - STRIPE_WEBHOOK_SECRET=whsec_DIN_NYE_WEBHOOK_SECRET_HER
      # Staging-specific settings
      - VITE_ALLOW_INDEXING=false
    networks:
      - jobblogg-staging-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  jobblogg-staging-network:
    driver: bridge
    labels:
      - "environment=staging"
