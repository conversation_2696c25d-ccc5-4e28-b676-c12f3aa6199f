# JobbLogg Architecture Documentation

## 📋 Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Environment Configuration](#environment-configuration)
3. [Access Control & Security](#access-control--security)
4. [Deployment Workflows](#deployment-workflows)
5. [Troubleshooting Guide](#troubleshooting-guide)

---

## 🏗️ Architecture Overview

### Development Environment
**Local Development Stack:**
- **IDE**: VSCode with Augment Code AI assistant
- **Frontend**: React + TypeScript + Vite (Port 5173)
- **Backend**: Convex.dev cloud service
- **Authentication**: Clerk (test keys)
- **Styling**: Tailwind CSS v4 + Custom Design System
- **Package Manager**: npm with legacy peer deps

**Local Development URLs:**
- Frontend: `http://localhost:5173/`
- Vite Preview: `http://localhost:4173/`
- Convex Backend: Cloud-hosted (no local port)

### Production Infrastructure (Hetzner Server)
**Server Configuration:**
- **Provider**: Hetzner Cloud VPS
- **Domain**: jobblogg.no
- **SSL**: <PERSON>addy reverse proxy with automatic HTTPS
- **Container Runtime**: Docker + Docker Compose
- **Architecture**: Multi-container microservices

**Production Services:**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Caddy Proxy   │    │  Frontend       │    │  Convex API     │
│   (Port 80/443) │────│  (Port 5174)    │────│  (Cloud)        │
│   SSL + Routing │    │  React/Vite     │    │  Database+Logic │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Environment Separation
- **Development**: Local VSCode + Convex Cloud (dev deployment)
- **Staging**: Not currently implemented
- **Production**: Hetzner server + Convex Cloud (prod deployment)

---

## ⚙️ Environment Configuration

### Environment Files Structure
```
JobbLogg/
├── .env.local          # Local development (active)
├── .env.docker         # Docker development template
├── .env.example        # Template for new developers
└── [Production files created on server]
```

### Development Environment (.env.local)
**File**: `.env.local` (Local development - active)
```bash
# Convex Configuration
CONVEX_DEPLOYMENT=dev:enchanted-quail-174
VITE_CONVEX_URL=https://enchanted-quail-174.convex.cloud

# Clerk Authentication (Test Keys)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk

# Google Maps API
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs

# Resend Email Service
RESEND_API_KEY=re_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL

# Stripe Payment (Test Keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51QuHVWRqXwHRnsDwFyefP4DJfEDG9Ti42UkWO7Y5nWmSDZbZtVLWfgDmmAP3YnYYb8905qIhtDDB8UUPLDjaUk9F00snevRBNh
STRIPE_SECRET_KEY=sk_test_51QuHVWRqXwHRnsDwtLPJ2Qd310QWPUvfvYKmxE4WPmC6ERPHCGfkdKgZp9xNZs3uPhUzGKQsmqytsgBdnXEClv3u00sKnCLi9T
STRIPE_WEBHOOK_SECRET=whsec_05c94535d706fbf2ac6105f0bd4967e5acabdf1ef2ba6135e1c6ac6f346b93e4
```

### Docker Development Environment (.env.docker)
**File**: `.env.docker` (Template for Docker development)
```bash
# Environment
NODE_ENV=development

# Convex Configuration
CONVEX_DEPLOYMENT=dev:jobblogg-docker
CONVEX_URL=http://localhost:3210
VITE_CONVEX_URL=http://localhost:3210

# Same API keys as development
# Copy to .env.docker.local and customize
```

### Production Environment (Server-side)
**File**: Created on Hetzner server during deployment
```bash
# Environment
NODE_ENV=production

# Convex Configuration (Production)
CONVEX_DEPLOYMENT=prod:jobblogg
VITE_CONVEX_URL=https://api.jobblogg.no

# Clerk Authentication (Live Keys)
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k

# Google Maps API (Same as dev)
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs

# Stripe Payment (Live Keys)
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[PRODUCTION_KEY]
STRIPE_SECRET_KEY=sk_live_[PRODUCTION_KEY]
STRIPE_WEBHOOK_SECRET=whsec_[PRODUCTION_SECRET]

# Resend Email (Production)
RESEND_API_KEY=re_[PRODUCTION_KEY]
```

### Required Environment Variables by Service

**Frontend (Vite) Variables:**
- `VITE_CONVEX_URL` - Convex API endpoint
- `VITE_CLERK_PUBLISHABLE_KEY` - Clerk authentication
- `VITE_GOOGLE_MAPS_API_KEY` - Maps integration
- `VITE_STRIPE_PUBLISHABLE_KEY` - Payment processing

**Backend (Convex) Variables:**
- `CONVEX_DEPLOYMENT` - Deployment identifier
- `STRIPE_SECRET_KEY` - Server-side Stripe operations
- `STRIPE_WEBHOOK_SECRET` - Webhook verification
- `RESEND_API_KEY` - Email sending service

---

## 🔐 Access Control & Security

### GitHub Repository Access
**Repository**: `https://github.com/djrobbieh/JobbLogg.git`
- **Owner**: djrobbieh (<EMAIL>)
- **Access Level**: Private repository
- **SSH Key**: Required for git operations
- **Branch Protection**: Main branch protected

### Server Access (Hetzner)
**SSH Access:**
```bash
# Server connection (example)
ssh root@jobblogg-prod-server
```
**Security Measures:**
- SSH key-based authentication
- Firewall configured for ports 80, 443, 22
- Docker containers run as non-root users
- SSL certificates via Caddy automatic HTTPS

### Service Account Configurations
**Convex.dev:**
- Team: djrobbieh
- Project: jobblogg-8b070
- Dev deployment: enchanted-quail-174
- Prod deployment: [Production deployment ID]

**Clerk Authentication:**
- Development: loved-dory-86.clerk.accounts.dev
- Production: jobblogg.no domain

**API Key Management:**
- Development keys: Stored in `.env.local`
- Production keys: Stored securely on server
- Rotation: Manual process (no automated rotation)

---

## 🚀 Deployment Workflows

### Local Development Setup
```bash
# 1. Clone repository
<NAME_EMAIL>:djrobbieh/JobbLogg.git
cd JobbLogg

# 2. Install dependencies
npm install --legacy-peer-deps

# 3. Setup environment
cp .env.example .env.local
# Edit .env.local with your API keys

# 4. Start development server
npm run dev
# Access: http://localhost:5173
```

### Docker Development Environment
```bash
# 1. Setup Docker environment
cp .env.docker .env.docker.local
# Edit .env.docker.local with your configuration

# 2. Start Docker development
chmod +x scripts/docker-dev.sh
./scripts/docker-dev.sh start

# 3. View logs
./scripts/docker-dev.sh logs

# 4. Stop environment
./scripts/docker-dev.sh stop
```

### Production Deployment (Current Method)
**⚠️ Current production uses manual Docker commands due to recent fixes**

```bash
# 1. SSH to production server
ssh root@jobblogg-prod-server

# 2. Navigate to project
cd /root/JobbLogg

# 3. Pull latest changes
git pull origin main

# 4. Build with fixed Dockerfile (addresses env var issues)
docker build --no-cache \
  --target production \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k \
  --build-arg VITE_CONVEX_URL=https://api.jobblogg.no \
  --build-arg VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs \
  --build-arg VITE_STRIPE_PUBLISHABLE_KEY=pk_test_[KEY] \
  -f docker/frontend/Dockerfile.fixed \
  -t jobblogg-frontend-production:latest .

# 5. Stop current container
docker stop jobblogg-frontend-production
docker rm jobblogg-frontend-production

# 6. Start new container
docker run -d --name jobblogg-frontend-production \
  -p 5174:5174 \
  jobblogg-frontend-production:latest \
  npx serve -s dist -l 5174

# 7. Verify deployment
curl -I https://jobblogg.no
```

### Rollback Procedures
```bash
# 1. List available Docker images
docker images | grep jobblogg-frontend-production

# 2. Stop current container
docker stop jobblogg-frontend-production
docker rm jobblogg-frontend-production

# 3. Start previous version
docker run -d --name jobblogg-frontend-production \
  -p 5174:5174 \
  jobblogg-frontend-production:[PREVIOUS_TAG] \
  npx serve -s dist -l 5174
```

### Container Management
```bash
# View running containers
docker ps

# View logs
docker logs jobblogg-frontend-production

# Execute commands in container
docker exec -it jobblogg-frontend-production sh

# Monitor resource usage
docker stats jobblogg-frontend-production
```

---

## 🔧 Troubleshooting Guide

### Common Deployment Issues

#### 1. Environment Variables Not Loading
**Problem**: Placeholder keys appear in production bundle
**Symptoms**:
```
pk_live_PRODUCTION_KEY_PLACEHOLDER found in bundle
Authentication fails with 503 errors
```
**Solution**: Use explicit environment variables in Docker build
```bash
# Fixed Dockerfile pattern (docker/frontend/Dockerfile.fixed)
RUN VITE_CLERK_PUBLISHABLE_KEY=$VITE_CLERK_PUBLISHABLE_KEY \
    VITE_CONVEX_URL=$VITE_CONVEX_URL \
    npx vite build --mode production
```

#### 2. TypeScript Build Errors
**Problem**: Build fails due to TypeScript errors
**Symptoms**:
```
error TS6133: 'variable' is declared but its value is never read
error TS2339: Property does not exist on type
```
**Solution**: Temporarily skip TypeScript checking
```bash
# Modify package.json build script
"build": "vite build"  # Remove "tsc &&" prefix
```

#### 3. Clerk Subdomain 503 Errors
**Problem**: `https://clerk.jobblogg.no` returns 503 Service Unavailable
**Symptoms**:
```
Failed to load resource: the server responded with a status of 503
GET https://clerk.jobblogg.no/npm/@clerk/clerk-js@5/dist/clerk.browser.js
```
**Investigation Steps**:
```bash
# Check DNS configuration
nslookup clerk.jobblogg.no
dig clerk.jobblogg.no

# Check reverse proxy configuration
curl -I https://clerk.jobblogg.no

# Verify Caddy/nginx configuration for subdomain routing
```

#### 4. Docker Build Cache Issues
**Problem**: Old cached layers prevent new environment variables from taking effect
**Solution**: Use `--no-cache` flag
```bash
docker build --no-cache [other-args]
```

#### 5. Port Conflicts
**Problem**: Port 5174 already in use
**Solution**:
```bash
# Find process using port
lsof -i :5174

# Kill process if needed
kill -9 [PID]

# Or use different port
docker run -p 5175:5174 [image]
```

### Environment Variable Debugging
```bash
# Check what variables are available during build
docker run --rm [image] env | grep VITE

# Verify variables in built bundle
docker exec [container] grep -o "pk_[a-zA-Z0-9_]*" /app/dist/assets/index-*.js

# Test environment variable substitution
echo $VITE_CLERK_PUBLISHABLE_KEY
```

### Service Connectivity Problems
```bash
# Test frontend accessibility
curl -I https://jobblogg.no

# Check container health
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Verify internal container networking
docker exec [container] curl -f http://localhost:5174/

# Check reverse proxy logs
docker logs caddy-container
```

### Performance Monitoring
```bash
# Monitor container resources
docker stats --no-stream

# Check disk usage
docker system df

# View container logs with timestamps
docker logs -t jobblogg-frontend-production

# Monitor network traffic
docker exec [container] netstat -tuln
```

### Emergency Procedures
```bash
# Quick rollback to previous working version
docker stop jobblogg-frontend-production
docker rm jobblogg-frontend-production
docker run -d --name jobblogg-frontend-production \
  -p 5174:5174 \
  jobblogg-frontend-production:[PREVIOUS_TAG]

# Emergency container restart
docker restart jobblogg-frontend-production

# Clear all Docker resources (CAUTION)
docker system prune -a --volumes
```

### Log Analysis
```bash
# View recent logs
docker logs --tail 100 jobblogg-frontend-production

# Follow logs in real-time
docker logs -f jobblogg-frontend-production

# Search for specific errors
docker logs jobblogg-frontend-production 2>&1 | grep -i error

# Export logs for analysis
docker logs jobblogg-frontend-production > deployment.log 2>&1
```

---

## 📚 Additional Resources

### Key Files and Directories
```
JobbLogg/
├── docker/frontend/Dockerfile.fixed    # Working production Dockerfile
├── .dockerignore                       # Excludes .env* files
├── scripts/docker-prod.sh              # Production deployment script
├── DOCKER_SETUP.md                     # Docker setup guide
└── DEVELOPMENT_PORTS.md                # Port configuration
```

### Important URLs
- **Production**: https://jobblogg.no
- **GitHub**: https://github.com/djrobbieh/JobbLogg
- **Convex Dashboard**: https://dashboard.convex.dev
- **Clerk Dashboard**: https://dashboard.clerk.com

### Contact Information
- **Developer**: djrobbieh (<EMAIL>)
- **Repository**: Private GitHub repository
- **Server**: Hetzner Cloud VPS

---

*Last Updated: August 2025*
*Version: 1.0*
