# JobbLogg Stripe Payment Integration - Implementeringsplan

## 🎯 **Oversikt**

Komplett implementeringsplan for Stripe payment integration i JobbLogg med 7-dagers gratis prøveperiode, Customer Portal for selvbetjening, og robust webhook-håndtering.

### **Hovedfunksjoner:**
- 7-dagers gratis prøveperiode uten kredittkort
- Stripe Customer Portal for all selvbetjening
- Norsk lokalisering og MVA-håndtering
- Robust webhook-system med idempotency
- Comprehensive access control

---

## **Phase 1: Customer Journey & Business Logic**

### **🔄 Customer Journey med Customer Portal:**

```mermaid
graph TD
    A[User Signs Up] --> B[7-Day Trial Starts]
    B --> C[Day 3: First Reminder]
    C --> D[Day 5: Second Reminder] 
    D --> E[24h: Final Warning]
    E --> F[Trial Expires]
    F --> G{Payment Required}
    G -->|Pays via Portal| H[Active Subscription]
    G -->|No Payment| I[Grace Period - Read Only]
    I --> J[3 Days Grace]
    J -->|Pays| H
    J -->|No Payment| K[Account Suspended]
    H --> L[Customer Portal for all changes]
```

### **🎯 Business Logic Decisions:**

1. **Stripe Customer Portal som standard:**
   - Alle oppgraderinger, kortbytte, fakturaer og kanselleringer
   - Eliminerer behov for egne "Upgrade Flow Components" i v1
   - Reduserer kompleksitet og vedlikehold betydelig

2. **Enterprise håndtering:**
   - "Kontakt oss" leder til tilbud/Hosted Invoice i Stripe
   - Ingen selvbetjening for Enterprise-kunder

3. **Årlig vs månedlig UI:**
   - Tydelig "Spar X kr/år" som i eksisterende prisbilder
   - Konsistent med nåværende design

4. **Trial Management:**
   - Varsler: dag 3, dag 5, 24t før slutt
   - Grace period: 3 dager med read-only tilgang
   - Én prøve per organisasjon og per kort

---

## **Phase 2: Database Schema**

### **🗄️ Convex Schema Utvidelser:**

```typescript
// convex/schema.ts - Nye tabeller

subscriptions: defineTable({
  userId: v.string(),
  
  // Stripe identifiers
  stripeCustomerId: v.string(),
  stripeSubscriptionId: v.optional(v.string()),
  stripePriceId: v.optional(v.string()),
  stripeProductId: v.optional(v.string()),
  
  // Subscription details
  status: v.union(
    v.literal("trialing"),
    v.literal("active"), 
    v.literal("past_due"),
    v.literal("canceled"),
    v.literal("incomplete"),
    v.literal("incomplete_expired"),
    v.literal("unpaid"),
    v.literal("paused")
  ),
  
  // Plan information
  planLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
  billingInterval: v.union(v.literal("month"), v.literal("year")),
  seats: v.optional(v.number()),
  
  // Timing
  trialStart: v.optional(v.number()),
  trialEnd: v.optional(v.number()),
  currentPeriodStart: v.number(),
  currentPeriodEnd: v.number(),
  
  // Cancellation
  cancelAt: v.optional(v.number()),
  cancelAtPeriodEnd: v.optional(v.boolean()),
  canceledAt: v.optional(v.number()),
  
  // Payment status
  latestInvoiceStatus: v.optional(v.union(
    v.literal("draft"),
    v.literal("open"),
    v.literal("paid"),
    v.literal("uncollectible"),
    v.literal("void")
  )),
  collectionMethod: v.optional(v.union(v.literal("charge_automatically"), v.literal("send_invoice"))),
  
  // Portal and webhook tracking
  portalUrl: v.optional(v.string()),
  lastWebhookEventId: v.optional(v.string()),
  
  createdAt: v.number(),
  updatedAt: v.number(),
})
  .index("by_user", ["userId"])
  .index("by_stripe_customer", ["stripeCustomerId"])
  .index("by_stripe_subscription", ["stripeSubscriptionId"])
  .index("by_status", ["status"])
  .index("by_trial_end", ["trialEnd"]),

// Trial notifications tracking
trialNotifications: defineTable({
  userId: v.string(),
  type: v.union(
    v.literal("trial_reminder_day_3"),
    v.literal("trial_reminder_day_5"), 
    v.literal("trial_reminder_24h"),
    v.literal("trial_expired"),
    v.literal("payment_failed_0d"),
    v.literal("payment_failed_3d"),
    v.literal("payment_failed_5d"),
    v.literal("grace_period_reminder")
  ),
  sentAt: v.number(),
  emailSent: v.boolean(),
  inAppNotificationRead: v.optional(v.boolean()),
})
  .index("by_user", ["userId"])
  .index("by_type_and_user", ["type", "userId"]),

// Webhook event tracking for idempotency
webhookEvents: defineTable({
  eventId: v.string(),
  eventType: v.string(),
  processedAt: v.number(),
})
  .index("by_event_id", ["eventId"]),
```

### **🔧 Enhanced User Table:**

```typescript
// Tillegg til eksisterende users table
users: defineTable({
  // ... existing fields
  
  // Subscription status (derived from Stripe events only)
  subscriptionStatus: v.optional(v.union(
    v.literal("trialing"),
    v.literal("active"),
    v.literal("past_due"),
    v.literal("canceled"),
    v.literal("grace_period")
  )),
  trialEndsAt: v.optional(v.number()),
  hasCompletedTrial: v.optional(v.boolean()),
  
  // Prevent multiple trials
  trialUsedAt: v.optional(v.number()),
  paymentMethodFingerprint: v.optional(v.string()),
})
```

---

## **Phase 3: Stripe Integration & Backend**

### **🔑 Stripe Configuration:**

```typescript
// convex/stripe/config.ts
export const STRIPE_CONFIG = {
  // Products and prices (create in Stripe Dashboard)
  products: {
    basic: {
      monthly: "price_basic_monthly_nok",
      yearly: "price_basic_yearly_nok"
    },
    professional: {
      monthly: "price_professional_monthly_nok", 
      yearly: "price_professional_yearly_nok"
    },
    enterprise: {
      monthly: "price_enterprise_monthly_nok",
      yearly: "price_enterprise_yearly_nok"
    }
  },
  
  // Customer Portal configuration
  portalConfiguration: {
    business_profile: {
      headline: "Administrer ditt JobbLogg-abonnement",
    },
    features: {
      payment_method_update: { enabled: true },
      invoice_history: { enabled: true },
      subscription_update: {
        enabled: true,
        default_allowed_updates: ["price", "promotion_code"],
        proration_behavior: "create_prorations"
      },
      subscription_cancel: {
        enabled: true,
        mode: "at_period_end",
        proration_behavior: "none"
      }
    },
    default_return_url: process.env.CONVEX_SITE_URL + "/dashboard"
  },
  
  // Checkout configuration
  checkoutDefaults: {
    mode: "subscription",
    payment_method_types: ["card"],
    allow_promotion_codes: true,
    billing_address_collection: "required",
    tax_id_collection: { enabled: true },
    locale: "no",
    subscription_data: {
      trial_period_days: 7,
    }
  }
} as const;
```

### **💳 Subscription Management Functions:**

```typescript
// convex/subscriptions.ts - Key functions

// Create trial subscription with Stripe customer
export const createTrialSubscription = mutation({
  args: {
    userId: v.string(),
    email: v.string(),
    name: v.string(),
    companyName: v.optional(v.string()),
    orgNumber: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user already had a trial
    const existingUser = await ctx.db.get(args.userId as any);
    if (existingUser?.hasCompletedTrial) {
      throw new Error("Trial already used");
    }

    // Create Stripe customer with Norwegian settings
    const customer = await stripe.customers.create({
      email: args.email,
      name: args.name,
      metadata: { userId: args.userId },
      tax: { validate_location: "immediately" }
    });

    // Create subscription record
    const subscriptionId = await ctx.db.insert("subscriptions", {
      userId: args.userId,
      stripeCustomerId: customer.id,
      status: "trialing",
      planLevel: "basic",
      billingInterval: "month",
      currentPeriodStart: Date.now(),
      currentPeriodEnd: Date.now() + (7 * 24 * 60 * 60 * 1000),
      trialStart: Date.now(),
      trialEnd: Date.now() + (7 * 24 * 60 * 60 * 1000),
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return { subscriptionId, customerId: customer.id };
  },
});

// Create Customer Portal session
export const createPortalSession = mutation({
  args: {
    userId: v.string(),
    returnUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    const session = await stripe.billingPortal.sessions.create({
      customer: subscription.stripeCustomerId,
      return_url: args.returnUrl || `${process.env.CONVEX_SITE_URL}/dashboard`,
    });

    return { url: session.url };
  },
});
```

### **🎣 Webhook Handler:**

```typescript
// convex/stripe/webhooks.ts
export const handleStripeWebhook = httpAction(async (ctx, request) => {
  const body = await request.text();
  const signature = request.headers.get("stripe-signature");

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature!,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    return new Response("Webhook signature verification failed", { status: 400 });
  }

  // Idempotency check
  const existingEvent = await ctx.runQuery(internal.webhooks.checkEventProcessed, {
    eventId: event.id
  });

  if (existingEvent) {
    return new Response("Event already processed", { status: 200 });
  }

  // Handle different event types
  switch (event.type) {
    case "customer.created":
    case "checkout.session.completed":
    case "customer.subscription.created":
    case "customer.subscription.updated":
    case "customer.subscription.deleted":
    case "customer.subscription.trial_will_end":
    case "invoice.paid":
    case "invoice.payment_failed":
    case "payment_intent.succeeded":
    case "payment_intent.payment_failed":
      // Process each event type with appropriate handler
      break;
  }

  return new Response("Webhook handled successfully", { status: 200 });
});
```

---

## **Phase 4: Frontend Implementation**

### **🎨 Trial Status Component:**

```typescript
// src/components/subscription/TrialStatus.tsx
export const TrialStatus: React.FC = () => {
  const { user } = useUser();
  const subscription = useQuery(api.subscriptions.getUserSubscription, {
    userId: user?.id || ""
  });
  const createPortalSession = useMutation(api.subscriptions.createPortalSession);

  if (!subscription || subscription.status !== "trialing") return null;

  const daysLeft = Math.ceil((subscription.trialEnd - Date.now()) / (24 * 60 * 60 * 1000));
  const isExpiringSoon = daysLeft <= 2;

  const handleManageSubscription = async () => {
    const { url } = await createPortalSession({
      userId: user!.id,
      returnUrl: window.location.href
    });
    window.location.href = url;
  };

  return (
    <div className={`p-6 rounded-xl border ${
      isExpiringSoon
        ? 'bg-jobblogg-warning-soft border-jobblogg-warning'
        : 'bg-jobblogg-primary-soft border-jobblogg-primary'
    }`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
            {daysLeft > 0
              ? `${daysLeft} dager igjen av gratis prøveperioden`
              : 'Prøveperioden er utløpt'
            }
          </h3>
          <p className="text-jobblogg-text-medium">
            Oppgrader for å fortsette å bruke alle funksjoner
          </p>
        </div>
        <PrimaryButton onClick={handleManageSubscription}>
          Velg abonnement
        </PrimaryButton>
      </div>
    </div>
  );
};
```

### **🔐 Access Control Hook:**

```typescript
// src/hooks/useSubscriptionAccess.ts
export const useSubscriptionAccess = () => {
  const { user } = useUser();
  const subscription = useQuery(api.subscriptions.getUserSubscription, {
    userId: user?.id || ""
  });

  // Derive access permissions from Stripe status only
  const hasActiveSubscription = subscription?.status === 'active';
  const isInTrial = subscription?.status === 'trialing';
  const isTrialExpired = isInTrial && subscription.trialEnd < Date.now();
  const isInGracePeriod = ['past_due', 'incomplete', 'unpaid'].includes(subscription?.status || '');

  const canCreateProjects = hasActiveSubscription || (isInTrial && !isTrialExpired);
  const canAccessProjects = hasActiveSubscription || isInTrial || isInGracePeriod;
  const hasFullAccess = hasActiveSubscription || (isInTrial && !isTrialExpired);
  const isReadOnly = isInGracePeriod || isTrialExpired;

  return {
    subscription,
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade: isTrialExpired || subscription?.status === 'past_due',
  };
};
```

### **🚫 Subscription Gate Component:**

```typescript
// src/components/subscription/SubscriptionGate.tsx
interface SubscriptionGateProps {
  children: React.ReactNode;
  feature: 'create_project' | 'full_access' | 'view_projects';
  fallback?: React.ReactNode;
}

export const SubscriptionGate: React.FC<SubscriptionGateProps> = ({
  children,
  feature,
  fallback
}) => {
  const { canCreateProjects, canAccessProjects, hasFullAccess } = useSubscriptionAccess();

  const hasAccess = {
    create_project: canCreateProjects,
    full_access: hasFullAccess,
    view_projects: canAccessProjects,
  }[feature];

  if (!hasAccess) {
    return fallback || <UpgradePrompt feature={feature} />;
  }

  return <>{children}</>;
};
```

---

## **Phase 5: Trial Management & Notifications**

### **📧 Notification System:**

```typescript
// convex/trialManagement.ts
const crons = cronJobs();

// Daily check at 9 AM Norwegian time
crons.daily(
  "trial and payment management",
  { hourUTC: 7 },
  internal.trialManagement.dailyTrialCheck
);

export const dailyTrialCheck = internalMutation({
  handler: async (ctx) => {
    const now = Date.now();

    // Day 3 reminders (4 days left)
    await sendTrialReminders(ctx, now + (4 * 24 * 60 * 60 * 1000), "trial_reminder_day_3");

    // Day 5 reminders (2 days left)
    await sendTrialReminders(ctx, now + (2 * 24 * 60 * 60 * 1000), "trial_reminder_day_5");

    // 24h reminders (1 day left)
    await sendTrialReminders(ctx, now + (1 * 24 * 60 * 60 * 1000), "trial_reminder_24h");

    // Handle expired trials
    await handleExpiredTrials(ctx, now);

    // Handle grace period expiration
    await handleGracePeriodExpiration(ctx, now);
  },
});
```

---

## **Phase 6: Sikkerhet og Robusthet**

### **🔒 Webhook Security:**

```typescript
// Idempotency checking
export const checkEventProcessed = internalQuery({
  args: { eventId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("webhookEvents")
      .withIndex("by_event_id", (q) => q.eq("eventId", args.eventId))
      .first();
  },
});

// Server-side entitlement validation
export const validateUserAccess = query({
  args: {
    userId: v.string(),
    feature: v.union(v.literal("create_project"), v.literal("full_access"), v.literal("view_projects"))
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // Validate access based on current Stripe status
    // Return { hasAccess: boolean, reason: string }
  },
});
```

---

## **🚀 Implementeringstidslinje**

### **Uke 1: Stripe Setup & Database**
- ✅ Opprett Stripe-konto og produkter med norsk MVA
- ✅ Konfigurer Customer Portal med norsk språk
- ✅ Implementer utvidet database schema
- ✅ Aktiver Stripe Tax for Norge

### **Uke 2: Backend & Webhooks**
- ✅ Implementer subscription management functions
- ✅ Bygge robust webhook handler med idempotency
- ✅ Sette opp trial management system
- ✅ Implementere Smart Retries og dunning

### **Uke 3: Frontend Integration**
- ✅ Bygge forenklet trial status komponenter
- ✅ Implementere Customer Portal integrasjon
- ✅ Lage access control system
- ✅ Norsk lokalisering av alle tekster

### **Uke 4: Testing & Lansering**
- ✅ Omfattende testplan
- ✅ Produksjonsdeploy med webhook endpoints
- ✅ Overvåking og KPI-oppsett
- ✅ Support-dokumentasjon

---

## **🧪 Testplan**

### **Kjøpsflyt Testing:**
- ✅ Alle planer månedlig/årlig
- ✅ Med og uten kupongkoder
- ✅ 3DS-autentisering
- ✅ Mislykket betaling scenarios

### **Trial Management Testing:**
- ✅ Trial start og countdown
- ✅ Notification timing (dag 3, 5, 24h)
- ✅ Trial expiration handling
- ✅ Grace period functionality
- ✅ Duplicate trial prevention

### **Customer Portal Testing:**
- ✅ Plan oppgradering/nedgradering
- ✅ Betalingsmetode endring
- ✅ Faktura nedlasting
- ✅ Abonnement kansellering
- ✅ Norsk språk og MVA

### **Webhook Robusthet:**
- ✅ Webhook retry scenarios
- ✅ Idempotency testing
- ✅ Network failure handling
- ✅ Signature verification

---

## **📊 KPI og Rapportering**

### **Viktige Metrics:**
- **Trial Start Rate:** % av registreringer som starter trial
- **Trial-to-Paid Conversion:** % av trials som konverterer
- **Payment Success Rate:** % vellykkede betalinger
- **Monthly Churn Rate:** % kunder som kansellerer per måned
- **MRR/ARR Growth:** Månedlig/årlig recurring revenue
- **Customer Lifetime Value (CLV)**

---

## **🔧 Stripe Dashboard Konfiguration**

### **Produkter som må opprettes:**
```
Basic Plan:
- price_basic_monthly_nok: 29900 øre (299 NOK)
- price_basic_yearly_nok: 287000 øre (2870 NOK)

Professional Plan:
- price_professional_monthly_nok: 99900 øre (999 NOK)
- price_professional_yearly_nok: 959000 øre (9590 NOK)

Enterprise Plan:
- price_enterprise_monthly_nok: 299900 øre (2999 NOK)
- price_enterprise_yearly_nok: 2879000 øre (28790 NOK)
```

### **Webhook Endpoints:**
```
Production: https://your-domain.com/api/stripe/webhook
Development: https://your-convex-site.convex.site/stripe/webhook
```

### **Required Webhook Events:**
- customer.created
- checkout.session.completed
- customer.subscription.created
- customer.subscription.updated
- customer.subscription.deleted
- customer.subscription.trial_will_end
- invoice.paid
- invoice.payment_failed
- payment_intent.succeeded
- payment_intent.payment_failed

---

## **🌍 Norsk Lokalisering**

### **Stripe Settings:**
- Locale: "no" (Norwegian)
- Currency: "nok" (Norwegian Kroner)
- Tax: Aktivert for Norge (25% MVA)
- Invoice language: Norwegian
- Customer Portal language: Norwegian

### **Email Templates:**
- Trial reminders på norsk
- Payment failure notifications på norsk
- Invoice emails på norsk
- Cancellation confirmations på norsk

---

## **🔐 Sikkerhetskrav**

### **Webhook Security:**
- Signaturverifisering på alle webhooks
- Idempotency keys på alle Stripe API-kall
- Rate limiting på webhook endpoints

### **Data Protection:**
- Ingen lagring av kortinformasjon
- GDPR-kompatibel databehandling
- Kryptering av sensitive subscription data

### **Access Control:**
- Server-side validering av subscription status
- Entitlements styres KUN av Stripe events
- Grace period håndteres server-side

---

## **📞 Support og Vedlikehold**

### **Monitoring:**
- Webhook delivery success rate
- Payment failure alerts
- Trial conversion tracking
- Subscription churn alerts

### **Logging:**
- Alle webhook events
- Payment attempts og failures
- Trial status changes
- Access control decisions

### **Backup Plans:**
- Manual subscription management via Stripe Dashboard
- Emergency access override for critical customers
- Rollback procedures for failed deployments

---

## **🎯 Seathåndtering (Ansattegrenser) - Hard Limit Approach**

### **📊 Plangrenser og Håndhevelse:**

JobbLogg bruker **Hard Limit Approach** for seat management - dette betyr at nye team invitasjoner blokkeres når plangrensen nås, og kunder må oppgradere før de kan legge til flere medlemmer.

```typescript
// Plangrenser
export const PLAN_LIMITS = {
  basic: {
    name: "Liten bedrift",
    maxSeats: 9,
    employeeRange: "1–9 ansatte",
    monthlyPrice: 299,
    yearlyPrice: 2870
  },
  professional: {
    name: "Mellomstor bedrift",
    maxSeats: 49,
    employeeRange: "10–49 ansatte",
    monthlyPrice: 999,
    yearlyPrice: 9590
  },
  enterprise: {
    name: "Stor bedrift",
    maxSeats: 249,
    employeeRange: "50–249 ansatte",
    monthlyPrice: 2999,
    yearlyPrice: 28790
  }
} as const;

// Hard Limit Enforcement
export const SEAT_ENFORCEMENT = {
  ENFORCEMENT_MODE: "hard_limit", // Block at limit
  WARNING_THRESHOLD: 0.8, // Warn at 80% capacity (7/9 seats)
  CRITICAL_THRESHOLD: 0.9, // Critical warning at 90% capacity (8/9 seats)
} as const;
```

### **🔄 Hard Limit Business Logic:**

```mermaid
graph TD
    A[User Attempts Team Invitation] --> B{Check Current Seat Count}
    B --> C{Within Plan Limit?}
    C -->|Yes| D[Allow Invitation]
    C -->|No| E[Block Invitation]
    D --> F[Send Invitation]
    D --> G{Check Warning Thresholds}
    G -->|80% Capacity| H[Show Approaching Limit Warning]
    G -->|90% Capacity| I[Show Critical Limit Warning]
    G -->|Under 80%| J[No Warning]
    E --> K[Show Upgrade Required Modal]
    K --> L[Redirect to Customer Portal]
    F --> M[Update Seat Count]
    M --> N[Log Seat Usage]
```

### **💡 Hard Limit Advantages:**
- **Klarere grenser:** Ingen forvirring om når oppgradering kreves
- **Enklere administrasjon:** Ingen grace periods å håndtere
- **Transparent pricing:** Kunder vet nøyaktig hva de betaler for
- **Mindre kompleksitet:** Færre edge cases og notification flows

### **⚠️ Warning System:**
- **80% kapasitet:** "Du nærmer deg plangrensen (7/9 plasser)"
- **90% kapasitet:** "Kun 1 plass igjen på din plan"
- **100% kapasitet:** "Plangrense nådd - oppgrader for å legge til flere"

---

## **🔧 Database Schema for Hard Limit Seat Management**

```typescript
// convex/schema.ts - Tillegg til subscriptions table
subscriptions: defineTable({
  // ... existing fields

  // Seat management (Hard Limit)
  currentSeats: v.number(), // Current active team members
  maxSeats: v.number(), // Plan limit (enforced strictly)
  lastSeatWarningAt: v.optional(v.number()), // Last warning sent
  seatEnforcementMode: v.literal("hard_limit"), // Always hard limit
})

// Seat usage tracking
seatUsageHistory: defineTable({
  subscriptionId: v.id("subscriptions"),
  userId: v.string(),
  action: v.union(
    v.literal("seat_added"),
    v.literal("seat_removed"),
    v.literal("invitation_blocked"),
    v.literal("plan_upgraded")
  ),
  seatCount: v.number(),
  planLevel: v.string(),
  timestamp: v.number(),
  metadata: v.optional(v.object({
    invitedUserEmail: v.optional(v.string()),
    blockedReason: v.optional(v.string()),
    previousPlan: v.optional(v.string()),
    newPlan: v.optional(v.string()),
  })),
})
  .index("by_subscription", ["subscriptionId"])
  .index("by_timestamp", ["timestamp"]),

// Seat warnings (simplified for hard limit)
seatNotifications: defineTable({
  subscriptionId: v.id("subscriptions"),
  userId: v.string(),
  type: v.union(
    v.literal("approaching_limit"), // 80% capacity
    v.literal("critical_limit"), // 90% capacity
    v.literal("limit_reached"), // 100% capacity - blocked
    v.literal("plan_upgraded") // Successful upgrade
  ),
  currentSeats: v.number(),
  maxSeats: v.number(),
  planLevel: v.string(),
  sentAt: v.number(),
  acknowledged: v.optional(v.boolean()),
})
  .index("by_subscription", ["subscriptionId"])
  .index("by_type", ["type"]),
```

---

## **🎯 Hard Limit Seat Management Functions**

```typescript
// convex/seatManagement.ts - Hard Limit Implementation

// Check if user can invite new team member (Hard Limit)
export const canInviteTeamMember = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      return { canInvite: false, reason: "no_subscription" };
    }

    const planLimit = PLAN_LIMITS[subscription.planLevel];
    const currentSeats = subscription.currentSeats || 0;

    // Hard Limit Check - Block if at or over limit
    if (currentSeats >= planLimit.maxSeats) {
      return {
        canInvite: false,
        reason: "hard_limit_reached",
        currentSeats,
        maxSeats: planLimit.maxSeats,
        suggestedPlan: getSuggestedUpgrade(subscription.planLevel, currentSeats + 1),
        message: `Du har nådd grensen for din ${planLimit.name} plan (${currentSeats}/${planLimit.maxSeats}). Oppgrader for å legge til flere team medlemmer.`
      };
    }

    // Check warning thresholds
    const warningThreshold = Math.floor(planLimit.maxSeats * SEAT_ENFORCEMENT.WARNING_THRESHOLD);
    const criticalThreshold = Math.floor(planLimit.maxSeats * SEAT_ENFORCEMENT.CRITICAL_THRESHOLD);

    let warning = null;
    let warningMessage = null;

    if (currentSeats >= criticalThreshold) {
      warning = "critical";
      const remaining = planLimit.maxSeats - currentSeats;
      warningMessage = `Kun ${remaining} ${remaining === 1 ? 'plass' : 'plasser'} igjen på din plan.`;
    } else if (currentSeats >= warningThreshold) {
      warning = "approaching";
      warningMessage = `Du nærmer deg plangrensen (${currentSeats}/${planLimit.maxSeats}).`;
    }

    return {
      canInvite: true,
      reason: "within_limit",
      currentSeats,
      maxSeats: planLimit.maxSeats,
      warning,
      warningMessage,
      remainingSeats: planLimit.maxSeats - currentSeats,
      suggestedPlan: warning ? getSuggestedUpgrade(subscription.planLevel, currentSeats + 1) : null
    };
  },
});

// Invite team member with hard limit enforcement
export const inviteTeamMemberWithSeatCheck = mutation({
  args: {
    userId: v.string(),
    invitedEmail: v.string(),
    role: v.union(v.literal("administrator"), v.literal("utførende")),
  },
  handler: async (ctx, args) => {
    // Check if invitation is allowed (Hard Limit)
    const seatCheck = await ctx.runQuery("seatManagement:canInviteTeamMember", {
      userId: args.userId
    });

    if (!seatCheck.canInvite) {
      // Log blocked invitation attempt
      const subscription = await ctx.db
        .query("subscriptions")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .first();

      if (subscription) {
        await ctx.db.insert("seatUsageHistory", {
          subscriptionId: subscription._id,
          userId: args.userId,
          action: "invitation_blocked",
          seatCount: subscription.currentSeats || 0,
          planLevel: subscription.planLevel,
          timestamp: Date.now(),
          metadata: {
            invitedUserEmail: args.invitedEmail,
            blockedReason: seatCheck.reason,
          },
        });

        // Send limit reached notification
        await ctx.runMutation("seatManagement:sendSeatNotification", {
          subscriptionId: subscription._id,
          type: "limit_reached",
          currentSeats: subscription.currentSeats || 0,
          maxSeats: subscription.maxSeats || 0,
        });
      }

      throw new Error(`Cannot invite team member: ${seatCheck.message || seatCheck.reason}`);
    }

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    // Proceed with invitation
    const invitationResult = await ctx.runMutation("teamManagement:inviteTeamMember", {
      userId: args.userId,
      email: args.invitedEmail,
      role: args.role,
    });

    // Update seat count
    const newSeatCount = (subscription.currentSeats || 0) + 1;

    await ctx.db.patch(subscription._id, {
      currentSeats: newSeatCount,
      updatedAt: Date.now(),
    });

    // Log seat usage
    await ctx.db.insert("seatUsageHistory", {
      subscriptionId: subscription._id,
      userId: args.userId,
      action: "seat_added",
      seatCount: newSeatCount,
      planLevel: subscription.planLevel,
      timestamp: Date.now(),
      metadata: {
        invitedUserEmail: args.invitedEmail,
      },
    });

    // Send warning notifications if approaching limits
    const planLimit = PLAN_LIMITS[subscription.planLevel];
    const warningThreshold = Math.floor(planLimit.maxSeats * SEAT_ENFORCEMENT.WARNING_THRESHOLD);
    const criticalThreshold = Math.floor(planLimit.maxSeats * SEAT_ENFORCEMENT.CRITICAL_THRESHOLD);

    if (newSeatCount >= criticalThreshold) {
      await ctx.runMutation("seatManagement:sendSeatNotification", {
        subscriptionId: subscription._id,
        type: "critical_limit",
        currentSeats: newSeatCount,
        maxSeats: planLimit.maxSeats,
      });
    } else if (newSeatCount >= warningThreshold) {
      await ctx.runMutation("seatManagement:sendSeatNotification", {
        subscriptionId: subscription._id,
        type: "approaching_limit",
        currentSeats: newSeatCount,
        maxSeats: planLimit.maxSeats,
      });
    }

    return {
      ...invitationResult,
      seatInfo: {
        currentSeats: newSeatCount,
        maxSeats: planLimit.maxSeats,
        remainingSeats: planLimit.maxSeats - newSeatCount,
        isNearLimit: newSeatCount >= warningThreshold,
        isCritical: newSeatCount >= criticalThreshold,
      }
    };
  },
});

// Remove team member and update seat count
export const removeTeamMemberWithSeatUpdate = mutation({
  args: {
    userId: v.string(),
    removedUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Remove team member
    const removalResult = await ctx.runMutation("teamManagement:removeTeamMember", {
      userId: args.userId,
      removedUserId: args.removedUserId,
    });

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      return removalResult;
    }

    // Update seat count
    const newSeatCount = Math.max(0, (subscription.currentSeats || 0) - 1);

    await ctx.db.patch(subscription._id, {
      currentSeats: newSeatCount,
      updatedAt: Date.now(),
    });

    // Log seat usage
    await ctx.db.insert("seatUsageHistory", {
      subscriptionId: subscription._id,
      userId: args.userId,
      action: "seat_removed",
      seatCount: newSeatCount,
      planLevel: subscription.planLevel,
      timestamp: Date.now(),
    });

    return {
      ...removalResult,
      seatInfo: {
        currentSeats: newSeatCount,
        maxSeats: subscription.maxSeats || 0,
        remainingSeats: (subscription.maxSeats || 0) - newSeatCount,
      }
    };
  },
});

// Get suggested plan upgrade based on seat count
function getSuggestedUpgrade(currentPlan: string, requiredSeats: number) {
  if (requiredSeats <= PLAN_LIMITS.basic.maxSeats) {
    return "basic";
  } else if (requiredSeats <= PLAN_LIMITS.professional.maxSeats) {
    return "professional";
  } else if (requiredSeats <= PLAN_LIMITS.enterprise.maxSeats) {
    return "enterprise";
  } else {
    return "enterprise"; // Contact us for larger organizations
  }
}
```

---

## **🎨 Frontend Components for Hard Limit Seat Management**

```typescript
// src/components/subscription/SeatUsageIndicator.tsx - Hard Limit Version
import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { useUser } from '@clerk/clerk-react';

export const SeatUsageIndicator: React.FC = () => {
  const { user } = useUser();
  const subscription = useQuery(api.subscriptions.getUserSubscription, {
    userId: user?.id || ""
  });

  if (!subscription) return null;

  const currentSeats = subscription.currentSeats || 0;
  const maxSeats = subscription.maxSeats || 0;
  const usagePercentage = (currentSeats / maxSeats) * 100;
  const remainingSeats = maxSeats - currentSeats;

  // Hard Limit Status
  const isAtLimit = currentSeats >= maxSeats;
  const isCritical = usagePercentage >= 90; // 90%+ capacity
  const isNearLimit = usagePercentage >= 80; // 80%+ capacity

  const getStatusColor = () => {
    if (isAtLimit) return 'text-jobblogg-error';
    if (isCritical) return 'text-jobblogg-warning';
    if (isNearLimit) return 'text-jobblogg-accent';
    return 'text-jobblogg-text-medium';
  };

  const getBackgroundColor = () => {
    if (isAtLimit) return 'bg-jobblogg-error-soft border-jobblogg-error';
    if (isCritical) return 'bg-jobblogg-warning-soft border-jobblogg-warning';
    if (isNearLimit) return 'bg-jobblogg-accent-soft border-jobblogg-accent';
    return 'bg-jobblogg-surface border-jobblogg-border';
  };

  const getStatusMessage = () => {
    if (isAtLimit) return 'Plangrense nådd - oppgrader for å legge til flere';
    if (isCritical) return `Kun ${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} igjen`;
    if (isNearLimit) return 'Du nærmer deg plangrensen';
    return `${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} tilgjengelig`;
  };

  return (
    <div className={`p-4 rounded-lg border ${getBackgroundColor()}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-jobblogg-text-strong">
          Team medlemmer
        </span>
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {currentSeats} / {maxSeats}
        </span>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isAtLimit ? 'bg-jobblogg-error' :
            isCritical ? 'bg-jobblogg-warning' :
            isNearLimit ? 'bg-jobblogg-accent' : 'bg-jobblogg-primary'
          }`}
          style={{ width: `${Math.min(100, usagePercentage)}%` }}
        />
      </div>

      {/* Status message */}
      <p className={`text-xs ${getStatusColor()}`}>
        {getStatusMessage()}
      </p>

      {/* Upgrade suggestion for critical/at limit */}
      {(isCritical || isAtLimit) && (
        <div className="mt-2 pt-2 border-t border-current border-opacity-20">
          <p className="text-xs text-jobblogg-text-medium">
            Vurder å oppgradere til en større plan for flere team medlemmer.
          </p>
        </div>
      )}
    </div>
  );
};

// src/components/team/InviteTeamMemberModal.tsx - Hard Limit Version
export const InviteTeamMemberModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
}> = ({ isOpen, onClose }) => {
  const { user } = useUser();
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<'administrator' | 'utførende'>('utførende');
  const [isLoading, setIsLoading] = useState(false);

  const seatCheck = useQuery(api.seatManagement.canInviteTeamMember, {
    userId: user?.id || ""
  });

  const inviteTeamMember = useMutation(api.seatManagement.inviteTeamMemberWithSeatCheck);

  const handleInvite = async () => {
    if (!seatCheck?.canInvite) {
      return; // Should not reach here due to UI blocking
    }

    setIsLoading(true);
    try {
      await inviteTeamMember({
        userId: user!.id,
        invitedEmail: email,
        role,
      });
      onClose();
      // Show success message
    } catch (error) {
      console.error('Failed to invite team member:', error);
      // Show error message
    } finally {
      setIsLoading(false);
    }
  };

  if (!seatCheck) return null;

  // Show upgrade modal if cannot invite (Hard Limit reached)
  if (!seatCheck.canInvite) {
    return (
      <SeatLimitUpgradeModal
        isOpen={isOpen}
        onClose={onClose}
        reason={seatCheck.reason}
        currentSeats={seatCheck.currentSeats}
        maxSeats={seatCheck.maxSeats}
        suggestedPlan={seatCheck.suggestedPlan}
        message={seatCheck.message}
      />
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-4">Inviter team medlem</h2>

        {/* Seat warning if approaching/critical limit */}
        {seatCheck.warning && (
          <div className={`p-3 rounded-lg mb-4 ${
            seatCheck.warning === 'critical'
              ? 'bg-jobblogg-warning-soft border border-jobblogg-warning'
              : 'bg-jobblogg-accent-soft border border-jobblogg-accent'
          }`}>
            <div className="flex items-start gap-2">
              <svg className="w-5 h-5 mt-0.5 text-current flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="text-sm font-medium">
                  {seatCheck.warningMessage}
                </p>
                {seatCheck.suggestedPlan && (
                  <p className="text-xs mt-1 opacity-80">
                    Vurder å oppgradere til {seatCheck.suggestedPlan} plan for flere plasser.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Current seat usage */}
        <div className="bg-jobblogg-surface rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-jobblogg-text-medium">Team medlemmer:</span>
            <span className="font-medium text-jobblogg-text-strong">
              {seatCheck.currentSeats} / {seatCheck.maxSeats}
            </span>
          </div>
          <div className="text-xs text-jobblogg-text-muted mt-1">
            {seatCheck.remainingSeats} {seatCheck.remainingSeats === 1 ? 'plass' : 'plasser'} igjen etter denne invitasjonen
          </div>
        </div>

        {/* Invitation form */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-jobblogg-text-strong mb-1">
              E-postadresse
            </label>
            <input
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-3 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-jobblogg-text-strong mb-1">
              Rolle
            </label>
            <select
              value={role}
              onChange={(e) => setRole(e.target.value as any)}
              className="w-full p-3 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary"
              disabled={isLoading}
            >
              <option value="utførende">Utførende</option>
              <option value="administrator">Administrator</option>
            </select>
          </div>

          <div className="flex gap-3 pt-2">
            <SecondaryButton onClick={onClose} disabled={isLoading}>
              Avbryt
            </SecondaryButton>
            <PrimaryButton
              onClick={handleInvite}
              disabled={isLoading || !email.trim()}
            >
              {isLoading ? 'Sender...' : 'Send invitasjon'}
            </PrimaryButton>
          </div>
        </div>
      </div>
    </Modal>
  );
};

// src/components/subscription/SeatLimitUpgradeModal.tsx - Hard Limit Version
export const SeatLimitUpgradeModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  reason: string;
  currentSeats: number;
  maxSeats: number;
  suggestedPlan?: string;
  message?: string;
}> = ({ isOpen, onClose, reason, currentSeats, maxSeats, suggestedPlan, message }) => {
  const { user } = useUser();
  const createPortalSession = useMutation(api.subscriptions.createPortalSession);
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgrade = async () => {
    setIsLoading(true);
    try {
      const { url } = await createPortalSession({
        userId: user!.id,
        returnUrl: window.location.href
      });
      window.location.href = url;
    } catch (error) {
      console.error('Failed to create portal session:', error);
      setIsLoading(false);
    }
  };

  const getPlanInfo = (planLevel: string) => {
    const plans = {
      basic: { name: 'Liten bedrift', seats: '1-9', price: '299 kr/mnd' },
      professional: { name: 'Mellomstor bedrift', seats: '10-49', price: '999 kr/mnd' },
      enterprise: { name: 'Stor bedrift', seats: '50-249', price: '2999 kr/mnd' }
    };
    return plans[planLevel as keyof typeof plans] || plans.professional;
  };

  const suggestedPlanInfo = suggestedPlan ? getPlanInfo(suggestedPlan) : null;

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="p-6 text-center">
        <div className="w-16 h-16 bg-jobblogg-error-soft rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-jobblogg-error" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clipRule="evenodd" />
          </svg>
        </div>

        <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
          Plangrense nådd
        </h2>

        <p className="text-jobblogg-text-medium mb-6">
          {message || `Du har nådd grensen for din plan (${currentSeats}/${maxSeats} team medlemmer). Oppgrader for å legge til flere.`}
        </p>

        {/* Current usage */}
        <div className="bg-jobblogg-surface rounded-lg p-4 mb-6">
          <div className="text-sm text-jobblogg-text-medium mb-2">
            Nåværende bruk:
          </div>
          <div className="text-2xl font-bold text-jobblogg-text-strong">
            {currentSeats} / {maxSeats}
          </div>
          <div className="text-sm text-jobblogg-text-muted">
            team medlemmer
          </div>
        </div>

        {/* Suggested plan */}
        {suggestedPlanInfo && (
          <div className="bg-jobblogg-primary-soft rounded-lg p-4 mb-6 border border-jobblogg-primary">
            <div className="text-sm text-jobblogg-primary font-medium mb-1">
              Anbefalt oppgradering:
            </div>
            <div className="text-lg font-semibold text-jobblogg-text-strong">
              {suggestedPlanInfo.name}
            </div>
            <div className="text-sm text-jobblogg-text-medium">
              {suggestedPlanInfo.seats} ansatte • {suggestedPlanInfo.price}
            </div>
          </div>
        )}

        <div className="flex gap-3">
          <SecondaryButton onClick={onClose} disabled={isLoading}>
            Avbryt
          </SecondaryButton>
          <PrimaryButton onClick={handleUpgrade} disabled={isLoading}>
            {isLoading ? 'Åpner...' : 'Oppgrader plan'}
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  );
};
```

---

## **📧 Email Notifications for Hard Limit Seat Management**

```typescript
// convex/emails/seatNotifications.ts - Hard Limit Version
export const sendSeatNotification = mutation({
  args: {
    userId: v.string(),
    type: v.string(),
    currentSeats: v.number(),
    maxSeats: v.number(),
    planLevel: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId as any);
    if (!user) return;

    const planNames = {
      basic: 'Liten bedrift',
      professional: 'Mellomstor bedrift',
      enterprise: 'Stor bedrift'
    };

    const planName = planNames[args.planLevel as keyof typeof planNames] || args.planLevel;
    const remainingSeats = args.maxSeats - args.currentSeats;

    const emailTemplates = {
      approaching_limit: {
        subject: "⚠️ Du nærmer deg plangrensen på JobbLogg",
        message: `Hei ${user.name},

Du har nå ${args.currentSeats} av ${args.maxSeats} team medlemmer på din ${planName} plan.

Du har ${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} igjen før du når plangrensen.

Vurder å oppgradere snart for å unngå at nye invitasjoner blokkeres.

Administrer abonnement: [Customer Portal Link]

Mvh,
JobbLogg teamet`
      },

      critical_limit: {
        subject: "🚨 Kun én plass igjen på din JobbLogg plan",
        message: `Hei ${user.name},

Du har nå ${args.currentSeats} av ${args.maxSeats} team medlemmer på din ${planName} plan.

Du har kun ${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} igjen!

Oppgrader nå for å unngå at nye team invitasjoner blokkeres.

Administrer abonnement: [Customer Portal Link]

Mvh,
JobbLogg teamet`
      },

      limit_reached: {
        subject: "🚫 Plangrense nådd - Nye invitasjoner blokkert",
        message: `Hei ${user.name},

Du har nådd grensen for din ${planName} plan (${args.currentSeats}/${args.maxSeats} team medlemmer).

Nye team invitasjoner er nå blokkert til du oppgraderer din plan.

Oppgrader umiddelbart: [Customer Portal Link]

Mvh,
JobbLogg teamet`
      },

      plan_upgraded: {
        subject: "✅ Plan oppgradert - Flere team plasser tilgjengelig",
        message: `Hei ${user.name},

Takk for at du oppgraderte din JobbLogg plan!

Du kan nå legge til flere team medlemmer og fortsette å vokse.

Administrer team: [Team Management Link]

Mvh,
JobbLogg teamet`
      }
    };

    const template = emailTemplates[args.type as keyof typeof emailTemplates];
    if (!template) return;

    // Send email via your email service
    await ctx.runMutation("emails:sendEmail", {
      to: user.email,
      subject: template.subject,
      message: template.message,
    });
  },
});
```

---

## **🎯 Hard Limit Seat Management - Oppsummering**

### **✅ Implementerte Funksjoner:**

#### **1. Streng Håndhevelse:**
- **Blokkering ved grense:** Nye invitasjoner stoppes når plangrensen nås
- **Ingen grace period:** Klarere grenser uten forvirring
- **Umiddelbar feedback:** Tydelige meldinger om hvorfor invitasjon blokkeres

#### **2. Proaktive Varsler:**
- **80% kapasitet:** "Du nærmer deg plangrensen"
- **90% kapasitet:** "Kun X plasser igjen"
- **100% kapasitet:** "Plangrense nådd - oppgrader nå"

#### **3. Sømløs Oppgradering:**
- **Customer Portal integrasjon:** Ett klikk til oppgradering
- **Automatisk plan forslag:** Anbefaler riktig plan basert på behov
- **Umiddelbar aktivering:** Nye plasser tilgjengelig etter oppgradering

#### **4. Komplett Logging:**
- **Seat usage history:** Full sporbarhet av alle endringer
- **Blocked invitation tracking:** Oversikt over blokkerte forsøk
- **Plan upgrade events:** Dokumentasjon av alle oppgraderinger

### **📊 Business Benefits:**

#### **1. Transparent Pricing:**
- Kunder vet nøyaktig hva de betaler for
- Ingen overraskelser eller skjulte kostnader
- Klar sammenheng mellom pris og verdi

#### **2. Enklere Support:**
- Færre edge cases å håndtere
- Klarere regler for kundeservice
- Mindre kompleksitet i billing logic

#### **3. Bedre Konvertering:**
- Tydelig oppgraderingspress ved grense
- Umiddelbar verdi av oppgradering
- Mindre friksjon i oppgraderingsprosessen

### **🔧 Tekniske Fordeler:**

#### **1. Enklere Kodebase:**
- Ingen grace period logic å vedlikeholde
- Færre notification flows
- Mindre komplekse state transitions

#### **2. Robust Enforcement:**
- Server-side validering på alle invitasjoner
- Atomiske operasjoner for seat updates
- Konsistent state management

#### **3. Skalerbar Arkitektur:**
- Lett å legge til nye plan tiers
- Fleksibel seat limit konfiguration
- Modulær notification system

### **📈 Metrics å Følge:**

#### **1. Seat Utilization:**
- Gjennomsnittlig seat usage per plan
- Tid fra 80% til 100% kapasitet
- Conversion rate ved plangrense

#### **2. Upgrade Behavior:**
- Hvor raskt kunder oppgraderer ved grense
- Hvilke planer de velger
- Churn rate ved blocked invitations

#### **3. Support Impact:**
- Reduksjon i seat-relaterte support tickets
- Klarhet i customer communication
- Satisfaction scores for billing transparency

---

### **🚀 Implementeringsrekkefølge:**

1. **Database Schema:** Legg til seat tracking fields
2. **Backend Functions:** Implementer hard limit logic
3. **Frontend Components:** Bygg seat indicators og upgrade modals
4. **Email Notifications:** Sett opp warning og blocking emails
5. **Testing:** Omfattende testing av alle seat scenarios
6. **Deployment:** Gradvis utrulling med monitoring

**Hard Limit Approach gir JobbLogg en enkel, transparent og effektiv måte å håndtere seat management på, med klar verdiproposisjon for oppgraderinger og minimal kompleksitet i både kode og kundeopplevelse.**

---

*Denne implementeringsplanen gir en komplett, robust og skalerbar Stripe-integrasjon som følger beste praksis og norske forretningskrav.*
```
