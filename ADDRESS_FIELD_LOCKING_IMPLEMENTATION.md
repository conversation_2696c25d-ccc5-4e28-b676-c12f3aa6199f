# Address Field Locking Implementation

## Overview

Successfully implemented address field locking functionality in the Company Profile Modal, allowing users to toggle between locked Brønnøysundregisteret addresses and custom editable addresses.

## Features Implemented

### ✅ **1. Address Field Locking by Default**
- Street Address, Postal Code, and City fields are now locked when data comes from Brønnøysundregisteret
- Uses the same LockedInput component styling as Company Name and Contact Person fields
- Consistent gray background, lock icons, and helper text

### ✅ **2. Custom Address Toggle**
- Added "Bruk tilpasset adresse" (Use custom address) checkbox
- When unchecked (default): Address fields are locked showing Brønnøysundregisteret data
- When checked: Address fields become editable for project-specific addresses
- Toggle state is saved in form data and persists when reopening the modal

### ✅ **3. Dynamic Field Switching**
- Real-time switching between LockedInput and TextInput components based on toggle state
- Proper state management ensures fields update immediately when toggle is changed
- Debug logging shows the locking logic in action

## Technical Implementation

### **Address Locking Logic**
```typescript
// Determine if address should be locked
const shouldLockAddress = hasBreregData || !!(contractorCompany.streetAddress && contractorCompany.postalCode && contractorCompany.city);

// Set locked fields based on custom address toggle
const lockedFieldsState = {
  companyName: shouldLockCompanyName,
  contactPerson: shouldLockContactPerson,
  streetAddress: shouldLockAddress && !formData.useCustomAddress, // ✅ Lock unless custom address enabled
  postalCode: shouldLockAddress && !formData.useCustomAddress,    // ✅ Lock unless custom address enabled
  city: shouldLockAddress && !formData.useCustomAddress,          // ✅ Lock unless custom address enabled
  orgNumber: true
};
```

### **Toggle Handler Enhancement**
```typescript
// Handle form field changes with address toggle logic
const handleFieldChange = (field: keyof CompanyFormData, value: string | boolean) => {
  setFormData(prev => ({ ...prev, [field]: value }));
  
  // If toggling custom address, update locked fields immediately
  if (field === 'useCustomAddress') {
    const shouldLockAddress = !!(contractorCompany?.streetAddress && contractorCompany?.postalCode && contractorCompany?.city);
    
    setLockedFields(prev => ({
      ...prev,
      streetAddress: shouldLockAddress && !value, // ✅ Unlock when custom address enabled
      postalCode: shouldLockAddress && !value,
      city: shouldLockAddress && !value,
    }));
  }
};
```

### **Dynamic Field Rendering**
```typescript
// Street Address Field
{lockedFields.streetAddress ? (
  <LockedInput
    label={companyProfileTexts.labels.streetAddress}
    value={formData.streetAddress}
    fullWidth
    helperText="Hentet fra Brønnøysundregisteret"
  />
) : (
  <TextInput
    label={companyProfileTexts.labels.streetAddress}
    value={formData.streetAddress}
    onChange={(value) => handleFieldChange('streetAddress', value)}
    error={errors.streetAddress}
    placeholder={companyProfileTexts.placeholders.streetAddress}
  />
)}

// Postal Code Field
{lockedFields.postalCode ? (
  <LockedInput
    label={companyProfileTexts.labels.postalCode}
    value={formData.postalCode}
    fullWidth
    helperText="Hentet fra Brønnøysundregisteret"
  />
) : (
  <TextInput
    label={companyProfileTexts.labels.postalCode}
    value={formData.postalCode}
    onChange={(value) => handleFieldChange('postalCode', value)}
    error={errors.postalCode}
    placeholder={companyProfileTexts.placeholders.postalCode}
    maxLength={4}
  />
)}

// City Field
{lockedFields.city ? (
  <LockedInput
    label={companyProfileTexts.labels.city}
    value={formData.city}
    fullWidth
    helperText="Hentet fra Brønnøysundregisteret"
  />
) : (
  <TextInput
    label={companyProfileTexts.labels.city}
    value={formData.city}
    onChange={(value) => handleFieldChange('city', value)}
    error={errors.city}
    placeholder={companyProfileTexts.placeholders.city}
  />
)}
```

## User Experience

### **Default State (Address Locked)**
- ✅ Street Address: Gray background, lock icon, shows Brønnøysundregisteret address
- ✅ Postal Code: Gray background, lock icon, shows official postal code
- ✅ City: Gray background, lock icon, shows official city name
- ✅ Helper Text: "Hentet fra Brønnøysundregisteret" on all address fields
- ✅ Toggle: Unchecked, labeled "Bruk tilpasset adresse"

### **Custom Address State (Address Unlocked)**
- ✅ Street Address: White background, editable, can enter project-specific address
- ✅ Postal Code: White background, editable, can enter custom postal code
- ✅ City: White background, editable, can enter custom city
- ✅ Validation: Proper form validation applies to editable fields
- ✅ Toggle: Checked, enabling custom address mode

### **Visual Consistency**
- ✅ All locked fields (Company Name, Contact Person, Address fields, Org Number) have identical styling
- ✅ Same gray background (`bg-jobblogg-neutral-secondary`)
- ✅ Same lock icon positioning (right-aligned inside field)
- ✅ Same helper text formatting and color
- ✅ Same border and spacing treatment

## Debug Features

### **Console Logging**
Added comprehensive debug logging to track address locking behavior:

```typescript
console.log('🔍 DEBUG: shouldLockAddress:', shouldLockAddress);
console.log('🔍 DEBUG: useCustomAddress:', formData.useCustomAddress);
console.log('🔍 DEBUG: Toggling custom address to:', value);
```

### **Expected Console Output**
```
🔍 DEBUG: shouldLockAddress: true
🔍 DEBUG: useCustomAddress: false
🔍 DEBUG: Setting lockedFields to: {
  companyName: true,
  contactPerson: true,
  streetAddress: true,    // ✅ Locked by default
  postalCode: true,       // ✅ Locked by default
  city: true,             // ✅ Locked by default
  orgNumber: true
}
```

When toggle is activated:
```
🔍 DEBUG: Toggling custom address to: true
🔍 DEBUG: shouldLockAddress: true
// Fields become unlocked: streetAddress: false, postalCode: false, city: false
```

## Files Modified

### **src/components/CompanyProfileModal.tsx**
- ✅ Enhanced `lockedFieldsState` to include address fields
- ✅ Updated `handleFieldChange` to handle custom address toggle
- ✅ Modified address field rendering to use conditional LockedInput/TextInput
- ✅ Added debug logging for address locking behavior
- ✅ Implemented real-time field switching based on toggle state

## Testing Scenarios

### **Scenario 1: Default Behavior**
1. ✅ Open Company Profile Modal
2. ✅ Address fields should be locked (gray background, lock icons)
3. ✅ Toggle should be unchecked
4. ✅ Fields should show Brønnøysundregisteret data

### **Scenario 2: Enable Custom Address**
1. ✅ Check "Bruk tilpasset adresse" toggle
2. ✅ Address fields should immediately become editable (white background)
3. ✅ Fields should retain current values but become editable
4. ✅ Form validation should apply to editable fields

### **Scenario 3: Disable Custom Address**
1. ✅ Uncheck "Bruk tilpasset adresse" toggle
2. ✅ Address fields should immediately become locked again
3. ✅ Fields should show original Brønnøysundregisteret data
4. ✅ Lock icons and gray background should return

### **Scenario 4: Form Persistence**
1. ✅ Toggle custom address on/off
2. ✅ Close and reopen modal
3. ✅ Toggle state should be preserved
4. ✅ Field locking should match saved toggle state

## Benefits

### **1. Data Integrity**
- Official Brønnøysundregisteret addresses are protected from accidental modification
- Clear visual indication of data source (locked fields with helper text)
- Users can still override when needed for project-specific requirements

### **2. User Experience**
- Intuitive toggle interface similar to contractor onboarding wizard
- Immediate visual feedback when switching between modes
- Consistent styling across all locked fields in the modal

### **3. Flexibility**
- Supports both official addresses and custom project addresses
- Toggle state persists across modal sessions
- Maintains form validation for both locked and editable states

The address field locking implementation provides a robust solution that balances data integrity with user flexibility, following JobbLogg's design system and accessibility standards while maintaining the same visual consistency as other locked fields in the Company Profile Modal.
