#!/bin/bash

# JobbLogg Deployment Script
# Supports the new standardized Docker Compose structure

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to force cleanup containers by environment
cleanup_environment() {
    local env=$1
    print_status "Force cleaning up $env environment containers..."

    # Stop and remove containers by environment with multiple naming patterns
    case $env in
        staging)
            # Multiple naming patterns for staging
            for pattern in "jobblogg-frontend-staging" "jobblogg-convex-staging" "jobblogg-healthcheck-staging"; do
                docker stop "$pattern" 2>/dev/null || true
                docker rm "$pattern" 2>/dev/null || true
            done
            docker network rm jobblogg-staging_jobblogg-staging-network 2>/dev/null || true
            ;;
        production)
            # Multiple naming patterns for production (old and new)
            for pattern in "jobblogg-frontend-production" "jobblogg-frontend-prod" "jobblogg-convex-production" "jobblogg-convex-prod" "jobblogg-convex-prod-placeholder" "jobblogg-healthcheck-production" "mock-convex-production-new"; do
                docker stop "$pattern" 2>/dev/null || true
                docker rm "$pattern" 2>/dev/null || true
            done
            # Remove various network naming patterns
            for network in "jobblogg-production_jobblogg-production-network" "jobblogg-prod-network" "jobblogg_jobblogg-network"; do
                docker network rm "$network" 2>/dev/null || true
            done
            ;;
        dev)
            # Multiple naming patterns for dev
            for pattern in "jobblogg-frontend-dev" "jobblogg-convex-dev" "jobblogg-healthcheck-dev"; do
                docker stop "$pattern" 2>/dev/null || true
                docker rm "$pattern" 2>/dev/null || true
            done
            docker network rm jobblogg_jobblogg-network 2>/dev/null || true
            ;;
    esac
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [ENVIRONMENT] [OPTIONS]"
    echo ""
    echo "ENVIRONMENTS:"
    echo "  dev         - Development environment (uses docker-compose.override.yml)"
    echo "  staging     - Staging environment (uses docker-compose.staging-only.yml)"
    echo "  production  - Production environment (uses docker-compose.prod.yml)"
    echo ""
    echo "OPTIONS:"
    echo "  --build     - Force rebuild of Docker images"
    echo "  --pull      - Pull latest images before deployment"
    echo "  --logs      - Show logs after deployment"
    echo "  --force-cleanup - Aggressively clean up all containers and networks"
    echo "  --help      - Show this help message"
    echo ""
    echo "EXAMPLES:"
    echo "  $0 dev                    # Start development environment"
    echo "  $0 staging --build        # Deploy staging with rebuild"
    echo "  $0 production --pull      # Deploy production with latest images"
}

# Parse command line arguments
ENVIRONMENT=""
BUILD_FLAG=""
PULL_FLAG=""
SHOW_LOGS=false
FORCE_CLEANUP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        dev|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        --pull)
            PULL_FLAG="--pull"
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --force-cleanup)
            FORCE_CLEANUP=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment is required"
    show_usage
    exit 1
fi

# Set compose files based on environment
case $ENVIRONMENT in
    dev)
        COMPOSE_FILES="-f docker-compose.yml"
        ENV_FILE=".env"
        print_status "Deploying DEVELOPMENT environment"
        ;;
    staging)
        COMPOSE_FILES="-f docker-compose.staging-only.yml"
        ENV_FILE=".env.staging"
        print_status "Deploying STAGING environment"
        print_status "Using staging-only compose (no convex dependency)"
        ;;
    production)
        COMPOSE_FILES="-f docker-compose.yml -f docker-compose.prod.yml"
        ENV_FILE=".env.production"
        print_status "Deploying PRODUCTION environment"
        ;;
esac

# Check if environment file exists
if [[ ! -f "$ENV_FILE" ]]; then
    print_error "Environment file $ENV_FILE not found"
    print_warning "Copy .env.example to $ENV_FILE and configure your variables"
    exit 1
fi

print_status "Using environment file: $ENV_FILE"
print_status "Using compose files: $COMPOSE_FILES"

# Export environment file
export $(grep -v '^#' "$ENV_FILE" | xargs)

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose $COMPOSE_FILES down

# Force cleanup of any remaining containers (always for staging and production to avoid port conflicts)
if [[ "$ENVIRONMENT" == "staging" ]] || [[ "$ENVIRONMENT" == "production" ]] || [[ "$FORCE_CLEANUP" == true ]]; then
    cleanup_environment $ENVIRONMENT
fi

# Pull images if requested
if [[ -n "$PULL_FLAG" ]]; then
    print_status "Pulling latest images..."
    docker-compose $COMPOSE_FILES pull
fi

# Start services
print_status "Starting services..."
docker-compose $COMPOSE_FILES up -d $BUILD_FLAG

# Wait for services to be healthy
print_status "Waiting for services to be ready..."
sleep 10

# Check service status
print_status "Checking service status..."
docker-compose $COMPOSE_FILES ps

# Show logs if requested
if [[ "$SHOW_LOGS" == true ]]; then
    print_status "Showing service logs..."
    docker-compose $COMPOSE_FILES logs --tail=50
fi

print_success "Deployment completed successfully!"
print_status "Environment: $ENVIRONMENT"

# Show access URLs based on environment
case $ENVIRONMENT in
    dev)
        print_status "Frontend: http://localhost:5173"
        print_status "Convex: http://localhost:3210"
        ;;
    staging)
        print_status "Frontend: http://localhost:5175"
        print_status "Convex: http://localhost:3211 (if enabled)"
        print_status "Public URL: https://staging.jobblogg.no"
        ;;
    production)
        print_status "Frontend: http://localhost:5174"
        print_status "Convex: http://localhost:3210"
        print_status "Public URL: https://jobblogg.no"
        ;;
esac
