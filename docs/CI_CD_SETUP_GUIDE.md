# 🚀 JobbLogg CI/CD Setup Guide

**Sist oppdatert**: 25. august 2025  
**Status**: ✅ Implementert og testet

---

## 📋 Oversikt

JobbLogg bruker GitHub Actions for automatisert CI/CD pipeline med følgende workflows:

1. **🔒 Security Scan** - <PERSON>kker<PERSON>tsskanning på hver commit
2. **🧪 Test & Quality Check** - Kodekvalitet og testing  
3. **📦 Deploy** - Automatisk deployment til staging/produksjon

---

## 🏗️ Pipeline Arkitektur

```mermaid
graph LR
    A[Git Push] --> B[Security Scan]
    B --> C[Test & Quality]
    C --> D[Build]
    D --> E[Deploy Staging]
    E --> F[Manual Approval]
    F --> G[Deploy Production]
```

---

## 🔧 Workflow Konfigurasjoner

### 1. Security Scan (`.github/workflows/security.yml`)

**Trigger**: <PERSON>e pushes og pull requests  
**Formål**: <PERSON>kanne for sikkerhetsproblemer

```yaml
# Hovedfunksjoner:
- Secret detection (smart patterns)
- Dependency vulnerability scanning  
- Code security analysis
- False positive filtering
```

**Status**: ✅ **PASSERER**

### 2. Test & Quality Check (`.github/workflows/test.yml`)

**Trigger**: Alle pushes og pull requests  
**Formål**: Sikre kodekvalitet

```yaml
# Hovedfunksjoner:
- ESLint code quality checks
- TypeScript type checking
- Unit tests (når implementert)
- Build verification
```

**Status**: ✅ **PASSERER**

### 3. Deploy (`.github/workflows/deploy.yml`)

**Trigger**: Push til main branch  
**Formål**: Automatisk deployment

```yaml
# Hovedfunksjoner:
- Docker image building
- SSH deployment til server
- Health checks
- Rollback på feil
```

**Status**: ⏳ **Venter på produksjonsserver**

---

## 🔐 GitHub Secrets Konfigurasjon

Følgende secrets må konfigureres i GitHub repository settings:

### Påkrevde Secrets for Hetzner Server:
```bash
# Hetzner server tilgang
HETZNER_SERVER_IP=your-hetzner-server-ip
HETZNER_SSH_KEY=your-hetzner-private-key

# Production environment variables (build args)
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k
VITE_CONVEX_URL=https://api.jobblogg.no
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[PRODUCTION_KEY]

# Backend secrets (for Convex deployment)
CONVEX_DEPLOY_KEY=your-convex-deploy-key
STRIPE_SECRET_KEY=sk_live_[PRODUCTION_KEY]
STRIPE_WEBHOOK_SECRET=whsec_[PRODUCTION_SECRET]
RESEND_API_KEY=re_[PRODUCTION_KEY]
```

---

## 🖥️ Hetzner Server (Eksisterende)

### Nåværende Konfigurasjon:
- **Provider**: Hetzner Cloud VPS
- **Domain**: jobblogg.no
- **SSL**: Caddy reverse proxy med automatisk HTTPS
- **Container Runtime**: Docker
- **Deployment**: Manual Docker commands (nå automatisert via CI/CD)

### Server Detaljer:
- **SSH Access**: root@hetzner-server-ip
- **Project Directory**: /root/JobbLogg
- **Container**: jobblogg-prod (port 3000)
- **Reverse Proxy**: Caddy (automatisk SSL)

---

## 📦 Deployment Prosess

### Automatisk (Staging):
1. Push til `main` branch
2. Security scan kjører
3. Tests kjører  
4. Build og deploy til staging
5. Health check verifiserer deployment

### Manuell (Produksjon):
1. Gå til GitHub Actions
2. Velg "Deploy" workflow
3. Klikk "Run workflow"
4. Velg "production" environment
5. Bekreft deployment

---

## 🔍 Monitoring og Logging

### Health Checks:
- **Endpoint**: `/health`
- **Interval**: Hver 30 sekund
- **Timeout**: 10 sekunder

### Logging:
- **Application logs**: Docker container logs
- **Access logs**: Nginx access logs  
- **Error logs**: Centralized error tracking

---

## 🚨 Troubleshooting

### Vanlige Problemer:

#### 1. Security Scan Feiler
```bash
# Sjekk for hardkodede secrets
grep -r "sk_" src/
grep -r "pk_" src/

# Løsning: Flytt til environment variables
```

#### 2. ESLint Errors
```bash
# Kjør lokalt for å fikse
npm run lint -- --fix

# For kritiske errors, endre til warnings i .eslintrc.js
```

#### 3. Deployment Feiler
```bash
# Sjekk server tilkobling
ssh deploy-user@your-server

# Sjekk Docker status
docker ps
docker-compose logs
```

---

## 📚 Vedlikehold

### Månedlig:
- [ ] Oppdater dependencies (`npm audit`)
- [ ] Sjekk server diskplass
- [ ] Review security scan resultater

### Kvartalsvis:  
- [ ] Oppdater Docker images
- [ ] Review og oppdater secrets
- [ ] Performance testing

### Årlig:
- [ ] Server OS oppdateringer
- [ ] SSL sertifikat fornyelse
- [ ] Backup strategi review

---

## 🎯 Neste Steg

1. **Sett opp produksjonsserver**
2. **Konfigurer domene og SSL**  
3. **Test full deployment pipeline**
4. **Implementer monitoring**
5. **Team training på CI/CD prosess**

---

*CI/CD pipeline er nå klar for produksjon! 🚀*
