# 🔐 SSH-nøkkel Setup for Staging Server

**Formål**: Konfigurere SSH-nøkler for automatisk deployment til staging server  
**Forutsetninger**: Tilgang til staging server og GitHub repository

---

## 🎯 Oversikt

For å aktivere automatisk deployment til staging server (staging.jobblogg.no) via GitHub Actions, må vi sette opp SSH-nøkler for sikker tilkobling.

---

## 🔑 Steg 1: Generer SSH-nøkler

### På din lokale maskin:

```bash
# Generer SSH nøkkelpar for staging
ssh-keygen -t ed25519 -C "<EMAIL>" -f ~/.ssh/jobblogg-staging

# Generer SSH nøkkelpar for produksjon (hvis ikke allerede gjort)
ssh-keygen -t ed25519 -C "<EMAIL>" -f ~/.ssh/jobblogg-production

# Vis public keys (for kopiering til server)
echo "=== STAGING PUBLIC KEY ==="
cat ~/.ssh/jobblogg-staging.pub
echo ""
echo "=== PRODUCTION PUBLIC KEY ==="
cat ~/.ssh/jobblogg-production.pub
```

### Alternativ med RSA (hvis ed25519 ikke støttes):

```bash
# RSA nøkler (større, men mer kompatible)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/jobblogg-staging-rsa
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/jobblogg-production-rsa
```

---

## 🖥️ Steg 2: Konfigurer Staging Server

### Hvis staging server er på samme Hetzner server:

```bash
# SSH til Hetzner server
ssh root@your-hetzner-server-ip

# Opprett staging directory (hvis ikke eksisterer)
mkdir -p /opt/jobblogg-staging
cd /opt/jobblogg-staging

# Klon repository for staging
git clone https://github.com/djrobbieh/JobbLogg.git .
git checkout main

# Opprett deploy bruker for staging (hvis ikke eksisterer)
adduser --system --group --home /opt/jobblogg-staging staging-deploy
usermod -aG docker staging-deploy

# Sett opp SSH for staging-deploy bruker
mkdir -p /opt/jobblogg-staging/.ssh
chown staging-deploy:staging-deploy /opt/jobblogg-staging/.ssh
chmod 700 /opt/jobblogg-staging/.ssh
```

### Legg til public keys på serveren:

```bash
# Legg til staging public key
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGitHub-Actions-Staging-Key <EMAIL>" >> /opt/jobblogg-staging/.ssh/authorized_keys

# Legg til production public key (for root bruker)
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGitHub-Actions-Production-Key <EMAIL>" >> /root/.ssh/authorized_keys

# Sett riktige tillatelser
chmod 600 /opt/jobblogg-staging/.ssh/authorized_keys
chmod 600 /root/.ssh/authorized_keys
chown staging-deploy:staging-deploy /opt/jobblogg-staging/.ssh/authorized_keys
```

---

## 🔐 Steg 3: GitHub Secrets Konfigurasjon

### Gå til GitHub Repository Settings:

1. **GitHub.com** → **djrobbieh/JobbLogg** → **Settings** → **Secrets and variables** → **Actions**

### Legg til følgende secrets:

#### A. Staging Server Secrets:
```bash
# Staging server tilgang
STAGING_HOST=your-hetzner-server-ip
STAGING_SSH_KEY=<innhold av ~/.ssh/jobblogg-staging private key>
STAGING_USERNAME=staging-deploy
STAGING_PATH=/opt/jobblogg-staging

# Staging environment variables
VITE_CONVEX_URL_STAGING=https://your-staging-deployment.convex.cloud
VITE_CLERK_PUBLISHABLE_KEY_STAGING=pk_test_staging_key_here
VITE_STRIPE_PUBLISHABLE_KEY_STAGING=pk_test_staging_stripe_key
STRIPE_SECRET_KEY_STAGING=sk_test_staging_stripe_key
RESEND_API_KEY_STAGING=your_staging_resend_key
```

#### B. Production Server Secrets (oppdatert):
```bash
# Production server tilgang (Hetzner)
HETZNER_SERVER_IP=your-hetzner-server-ip
HETZNER_SSH_KEY=<innhold av ~/.ssh/jobblogg-production private key>

# Production environment variables
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k
VITE_CONVEX_URL=https://api.jobblogg.no
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[PRODUCTION_KEY]
```

### Hvordan kopiere private key:

```bash
# Kopier staging private key
cat ~/.ssh/jobblogg-staging
# Kopier hele innholdet (inkludert -----BEGIN og -----END linjer)

# Kopier production private key  
cat ~/.ssh/jobblogg-production
# Kopier hele innholdet
```

---

## 🧪 Steg 4: Test SSH Tilkobling

### Test staging tilkobling:

```bash
# Test fra lokal maskin
ssh -i ~/.ssh/jobblogg-staging staging-deploy@your-hetzner-server-ip

# Hvis vellykket, test deployment kommandoer
cd /opt/jobblogg-staging
git pull origin main
docker --version
```

### Test production tilkobling:

```bash
# Test fra lokal maskin
ssh -i ~/.ssh/jobblogg-production root@your-hetzner-server-ip

# Test deployment kommandoer
cd /root/JobbLogg
git pull origin main
docker ps
```

---

## 🚀 Steg 5: Oppdater GitHub Actions Workflow

Staging deployment er allerede konfigurert i `.github/workflows/deploy.yml`, men la meg verifisere at den bruker riktige secrets:

### Staging deployment bruker:
- `STAGING_HOST` - Server IP
- `STAGING_SSH_KEY` - Private SSH key
- `STAGING_USERNAME` - staging-deploy
- Environment variables for staging

### Production deployment bruker:
- `HETZNER_SERVER_IP` - Server IP  
- `HETZNER_SSH_KEY` - Private SSH key
- Root bruker tilgang
- Environment variables for production

---

## 🔍 Steg 6: Test Deployment

### Test staging deployment:

1. **Gå til GitHub Actions**
2. **Velg "Deploy" workflow**
3. **Klikk "Run workflow"**
4. **Velg "staging" environment**
5. **Klikk "Run workflow"**

### Forventet resultat:
- ✅ SSH tilkobling til staging server
- ✅ Git pull av siste kode
- ✅ Docker build og deployment
- ✅ Health check på staging.jobblogg.no

### Test production deployment:

1. **Samme prosess som staging**
2. **Velg "production" environment**
3. **Krever manuell godkjenning**

---

## 🚨 Troubleshooting

### Problem: SSH Connection Failed

**Løsning:**
```bash
# Sjekk SSH key format
head -1 ~/.ssh/jobblogg-staging
# Skal starte med: -----BEGIN OPENSSH PRIVATE KEY-----

# Test tilkobling manuelt
ssh -i ~/.ssh/jobblogg-staging -v staging-deploy@your-server-ip
```

### Problem: Permission Denied

**Løsning:**
```bash
# På serveren, sjekk tillatelser
ls -la /opt/jobblogg-staging/.ssh/
chmod 700 /opt/jobblogg-staging/.ssh
chmod 600 /opt/jobblogg-staging/.ssh/authorized_keys
chown -R staging-deploy:staging-deploy /opt/jobblogg-staging/.ssh
```

### Problem: Git Pull Failed

**Løsning:**
```bash
# På serveren, sett git config
cd /opt/jobblogg-staging
git config --global --add safe.directory /opt/jobblogg-staging
chown -R staging-deploy:staging-deploy /opt/jobblogg-staging
```

---

## 📋 Sjekkliste

- [ ] SSH nøkler generert lokalt
- [ ] Public keys lagt til på server
- [ ] GitHub Secrets konfigurert (6 staging + 6 production)
- [ ] SSH tilkobling testet manuelt
- [ ] Staging deployment testet via GitHub Actions
- [ ] Production deployment testet via GitHub Actions
- [ ] Health checks passerer for begge miljøer

---

## 🎉 Ferdig!

Når alt er konfigurert vil du ha:

- ✅ **Automatisk staging deployment** på hver push til main
- ✅ **Manuell production deployment** med godkjenning
- ✅ **Sikker SSH tilkobling** uten passord
- ✅ **Isolerte miljøer** (staging og production)
- ✅ **Health checks** og rollback muligheter

**Neste steg**: Test første automatiske deployment! 🚀

---

*SSH-nøkkel setup for staging server fullført!*
