# JobbLogg Team Member Invitation System

## System Overview

JobbLogg's team member invitation system provides a streamlined magic link-based onboarding flow that transforms the traditional 8-step registration process into just 3 steps. The system integrates Convex backend, Clerk authentication, and Resend email service to deliver a seamless user experience.

### High-Level Architecture

```mermaid
graph TD
    A[Admin sends invitation] --> B[Convex creates invitation record]
    B --> C[Resend sends magic link email]
    C --> D[Recipient clicks magic link]
    D --> E[Pre-filled registration form]
    E --> F[Clerk creates account]
    F --> G[Convex accepts invitation]
    G --> H[User activated in team]
```

### Integration Points

- **Convex Backend**: Handles invitation creation, token management, and user activation
- **Clerk Authentication**: Manages user account creation and authentication
- **Resend Email Service**: Delivers professional magic link emails
- **React Frontend**: Provides admin interface and recipient registration flow

## Technical Implementation Details

### Complete Flow Breakdown

#### 1. Invitation Creation (Admin Side)
1. Admin opens `InviteTeamMemberModal` from team management page
2. Admin fills out recipient details (email, name, phone, role)
3. Frontend calls `inviteTeamMemberMagicLink` Convex action
4. System creates pending user record with invitation token
5. Magic link email sent via Resend API

#### 2. Email Delivery
1. Resend API sends professionally designed HTML email
2. Email contains magic link with invitation token
3. Norwegian localized content with company branding
4. 7-day expiration period clearly communicated

#### 3. Recipient Registration
1. Recipient clicks magic link → redirects to `/accept-invite?token=xxx`
2. `AcceptInvite` component loads invitation data
3. Form pre-populated with invitation details
4. Recipient adds password and can edit pre-filled information
5. Clerk handles account creation with proper validation

#### 4. Account Activation
1. After successful Clerk registration, `acceptMagicLinkInvitation` called
2. Convex updates user record with Clerk ID
3. User activated in team with assigned role
4. Automatic redirect to dashboard

### Phone Number Formatting

The system handles Norwegian phone number formatting throughout the flow:

```javascript
// Frontend formatting (InviteTeamMemberModal)
const formattedPhone = `+47 ${phone.slice(0, 3)} ${phone.slice(3, 5)} ${phone.slice(5)}`;

// AcceptInvite extraction (fixes double prefix issue)
const rawPhone = (invitationInfo.phone || '').replace(/\D/g, '').replace(/^47/, '');
```

## API Reference

### Convex Functions

#### `teamManagement.createMagicLinkInvitation`
**Type**: Mutation
**Purpose**: Creates invitation record in database

```typescript
args: {
  email: string,
  firstName: string,
  lastName: string,
  phone: string,
  invitedBy: string, // Clerk user ID
  role: 'administrator' | 'utfoerende'
}

returns: {
  invitationId: string,
  invitationToken: string,
  invitationLink: string,
  expiresAt: number,
  // ... other fields
}
```

#### `teamManagement.inviteTeamMemberMagicLink`
**Type**: Action
**Purpose**: Complete invitation flow with email sending

```typescript
args: {
  email: string,
  firstName: string,
  lastName: string,
  phone: string,
  invitedBy: string,
  role: 'administrator' | 'utfoerende'
}

returns: {
  invitationId: string,
  invitationToken: string,
  invitationLink: string,
  emailSent: boolean,
  emailId: string,
  message: string
}
```

#### `teamManagement.acceptMagicLinkInvitation`
**Type**: Mutation
**Purpose**: Activate user after Clerk account creation

```typescript
args: {
  invitationToken: string,
  clerkUserId: string,
  finalEmail: string,
  finalFirstName: string,
  finalLastName: string,
  finalPhone: string
}

returns: {
  userId: string,
  message: string,
  userProfile: UserProfile
}
```

#### `teamManagement.getMagicLinkInvitationData`
**Type**: Query
**Purpose**: Retrieve invitation details for pre-filling form

```typescript
args: {
  invitationToken: string
}

returns: {
  email: string,
  firstName: string,
  lastName: string,
  phone: string,
  role: string,
  companyName: string,
  invitedByName: string,
  expiresAt: number
}
```

### Email Functions

#### `emails.sendMagicLinkInvitationEmail`
**Type**: Action
**Purpose**: Send magic link email via Resend

```typescript
args: {
  to: string,
  firstName: string,
  lastName: string,
  invitedByName: string,
  inviterEmail: string,
  companyName: string,
  magicLink: string,
  expiresAt: number,
  role: 'administrator' | 'utfoerende'
}

returns: {
  success: boolean,
  emailId: string,
  message: string
}
```

### Email Template Data Structures

#### `MagicLinkInvitationEmailData`
```typescript
interface MagicLinkInvitationEmailData {
  firstName: string;
  lastName: string;
  invitedByName: string;
  inviterEmail: string;
  companyName: string;
  magicLink: string;
  expiresAt: number;
  role: 'administrator' | 'utfoerende';
}
```

## User Experience Flow

### Admin Perspective: Sending Invitations

1. **Access Team Management**
   - Navigate to `/team` page
   - Click "Inviter teammedlem" button

2. **Fill Invitation Form**
   - Enter recipient's email address
   - Provide first name and last name
   - Add Norwegian mobile number (8 digits)
   - Select role (Administrator or Utførende)

3. **Send Invitation**
   - Click "Send invitasjon" button
   - System validates input and creates invitation
   - Success message confirms email sent
   - Invitation link provided for manual sharing if needed

4. **Track Invitations**
   - View pending invitations in team management
   - Monitor invitation status (pending/accepted/expired)
   - Resend invitations if needed

### Recipient Perspective: Accepting Invitations

1. **Receive Magic Link Email**
   - Professional HTML email with Norwegian localization
   - Clear call-to-action button: "✨ Bli med i teamet nå"
   - Company and role information displayed
   - Expiration date clearly communicated

2. **Click Magic Link**
   - Redirects to `/accept-invite?token=xxx`
   - Page loads with invitation details
   - Form pre-populated with provided information

3. **Complete Registration**
   - Review and edit pre-filled information if needed
   - Create secure password (minimum 8 characters)
   - Click "Registrer og logg inn" button
   - Clerk handles account creation and validation

4. **Automatic Activation**
   - Account created and team membership activated
   - Automatic login and redirect to dashboard
   - Welcome message and onboarding guidance

### Error Handling and Edge Cases

#### Expired Tokens
- **Detection**: Token expiration checked on page load
- **User Experience**: Clear error message with contact information
- **Admin Action**: Resend invitation through team management

#### Duplicate Users
- **Detection**: Email already exists in Clerk
- **User Experience**: Redirect to login page with explanation
- **Resolution**: Use existing account or contact administrator

#### Invalid Tokens
- **Detection**: Token not found in database
- **User Experience**: Error page with support contact
- **Prevention**: Secure token generation and validation

#### Network Failures
- **Email Delivery**: Retry mechanism in Resend integration
- **Database Operations**: Convex automatic retry and error handling
- **User Feedback**: Clear error messages with retry options

## Configuration and Setup

### Environment Variables

```bash
# Convex Configuration
CONVEX_DEPLOYMENT=your-deployment-name
VITE_CONVEX_URL=https://your-deployment.convex.cloud

# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=pk_test_xxx
CLERK_SECRET_KEY=sk_test_xxx

# Resend Configuration (in Convex)
RESEND_API_KEY=re_xxx
```

### Clerk Configuration

#### Norwegian Localization
```typescript
// In ClerkProvider setup
import { nbNO } from '@clerk/localizations';

<ClerkProvider 
  publishableKey={clerkPublishableKey}
  localization={nbNO}
>
```

#### Required Fields Configuration
```typescript
// Clerk Dashboard Settings
- Email address: Required
- Password: Required  
- First name: Required
- Last name: Required
- Phone number: Optional (handled via metadata)
```

### Resend Email Configuration

#### Domain Setup
- **Development**: Use `<EMAIL>`
- **Production**: Configure custom domain in Resend dashboard
- **DNS**: Add required SPF, DKIM, and DMARC records

#### Email Templates
- **HTML Template**: Professional design with JobbLogg branding
- **Text Template**: Plain text fallback for accessibility
- **Localization**: Norwegian content with English technical terms

## Security Considerations

### Token Security
- **Generation**: Cryptographically secure random tokens
- **Expiration**: 7-day automatic expiration
- **Single Use**: Tokens invalidated after successful acceptance
- **Storage**: Secure database storage with proper indexing

### User Permission Levels

#### Administrator Role
- **Permissions**: Full team management access
- **Capabilities**: Invite users, manage projects, access all data
- **Restrictions**: Cannot delete company or change ownership

#### Utførende Role  
- **Permissions**: Project execution and customer communication
- **Capabilities**: Create projects, chat with customers, update project status
- **Restrictions**: Cannot invite users or access admin settings

### Data Privacy and GDPR Compliance

#### Data Collection
- **Minimal Data**: Only necessary information collected
- **Consent**: Clear communication about data usage
- **Retention**: Automatic cleanup of expired invitations

#### Data Processing
- **Encryption**: All data encrypted in transit and at rest
- **Access Control**: Role-based access to sensitive information
- **Audit Trail**: Complete logging of invitation and acceptance events

#### User Rights
- **Access**: Users can view their invitation data
- **Correction**: Pre-filled data can be edited during registration
- **Deletion**: Unused invitations automatically expire and are cleaned up

## Troubleshooting Guide

### Common Issues and Solutions

#### Phone Number Double Prefix Issue
**Problem**: Phone numbers display as "+47 47XXXXXXXX" instead of "+47 XXXXXXXX"

**Root Cause**: Phone number already contains "47" prefix when "+47" is added in UI

**Solution**: Extract raw digits in AcceptInvite component:
```javascript
const rawPhone = (invitationInfo.phone || '').replace(/\D/g, '').replace(/^47/, '');
```

#### Clerk Account Creation Failures
**Problem**: `first_name is not a valid parameter` error

**Root Cause**: Clerk's low-level API only accepts `emailAddress` and `password`

**Solution**: Use Clerk's `<SignUp>` component or update user profile after account creation:
```javascript
// After account creation
await user?.update({
  firstName: firstName.trim(),
  lastName: lastName.trim(),
});
```

#### Email Delivery Issues
**Problem**: Magic link emails not received

**Debugging Steps**:
1. Check Resend dashboard for delivery status
2. Verify email address format and domain
3. Check spam/junk folders
4. Validate Resend API key and domain configuration

**Solution**: Implement retry mechanism and fallback notification methods

#### Token Validation Errors
**Problem**: "Invalid invitation token" errors

**Debugging Steps**:
1. Verify token format and length
2. Check token expiration in database
3. Validate URL parameter extraction
4. Confirm database connectivity

**Solution**: Implement proper error handling and user feedback

### Debug Logging and Monitoring

#### Frontend Logging
```javascript
console.log('🚀 Form submitted with data:', { email, firstName, lastName, phone, role });
console.log('📱 Phone value details:', {
  phone,
  phoneLength: phone.length,
  phoneType: typeof phone,
  phoneRegexTest: /^\d{8}$/.test(phone)
});
```

#### Backend Logging
```javascript
console.log('🔧 Creating magic link invitation for:', email);
console.log('📧 Sending magic link invitation email to:', args.to);
console.log('✅ Magic link email sent successfully:', result);
```

#### Monitoring Metrics
- **Invitation Creation Rate**: Track successful invitation creation
- **Email Delivery Rate**: Monitor Resend delivery success
- **Acceptance Rate**: Measure invitation-to-registration conversion
- **Error Rates**: Track and alert on system failures

### Performance Optimization

#### Database Queries
- **Indexing**: Proper indexes on invitation tokens and user IDs
- **Cleanup**: Automatic removal of expired invitations
- **Caching**: Cache frequently accessed company and user data

#### Email Delivery
- **Batch Processing**: Group multiple invitations when possible
- **Rate Limiting**: Respect Resend API limits
- **Template Caching**: Cache compiled email templates

#### Frontend Performance
- **Code Splitting**: Lazy load invitation components
- **Form Validation**: Client-side validation before API calls
- **Loading States**: Proper loading indicators for better UX

---

*This documentation covers the complete team member invitation system in JobbLogg. For additional technical details or troubleshooting assistance, consult the development team or check the latest code in the repository.*
