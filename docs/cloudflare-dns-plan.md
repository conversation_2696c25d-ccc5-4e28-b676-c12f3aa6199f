# Cloudflare DNS Cutover Plan

## 📋 DNS Migrering til Cloudflare

**Formål**: Flytte DNS fra nåværende leverandør til Cloudflare med minimal nedetid.  
**Dato**: Klar for implementering  
**Estimert nedetid**: 5-15 minutter

---

## 🎯 Fase 1: Forberedelser (Før DNS-endring)

### 1.1 Verifiser Nåværende Oppsett
```bash
# Sjekk nåværende DNS
dig jobblogg.no
dig staging.jobblogg.no

# Verifiser server IP
curl -I https://jobblogg.no
```

### 1.2 Forbered Cloudflare Zone
- [ ] Opprett jobblogg.no zone i Cloudflare
- [ ] Notér Cloudflare nameservere
- [ ] **IKKE** endre nameservere ennå

---

## 🌐 Fase 2: DNS Records (Proxy OFF først)

### 2.1 A Records - **PROXY: OFF**
```
Type: A
Name: jobblogg.no (eller @)
Content: ************
TTL: 300 (5 minutter)
Proxy: 🔴 OFF (grå sky)
```

```
Type: A  
Name: staging
Content: ************
TTL: 300 (5 minutter)
Proxy: 🔴 OFF (grå sky)
```

### 2.2 CNAME Records - Placeholders
```
Type: CNAME
Name: clerk
Content: placeholder.example.com
TTL: 300
Proxy: 🔴 OFF
Kommentar: Oppdateres når Clerk domain konfigureres
```

```
Type: CNAME
Name: api
Content: jobblogg.no
TTL: 300
Proxy: 🔴 OFF
```

### 2.3 Email Records - Placeholders
```
Type: TXT
Name: jobblogg.no (eller @)
Content: "v=spf1 include:_spf.google.com ~all"
TTL: 300
Kommentar: Oppdater med faktisk email provider
```

```
Type: TXT
Name: _dmarc
Content: "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
TTL: 300
Kommentar: Oppdater med faktisk email
```

```
Type: TXT
Name: default._domainkey
Content: "v=DKIM1; k=rsa; p=PLACEHOLDER_DKIM_KEY"
TTL: 300
Kommentar: Oppdater med faktisk DKIM key
```

---

## ⚡ Fase 3: DNS Cutover (Kritisk fase)

### 3.1 Endre Nameservere
**Hos domain registrar** (f.eks. Domeneshop):
```
Nameserver 1: xxx.ns.cloudflare.com
Nameserver 2: yyy.ns.cloudflare.com
```
*(Faktiske nameservere fra Cloudflare zone)*

### 3.2 Verifiser Propagering
```bash
# Test DNS propagering (kan ta 5-60 minutter)
dig @8.8.8.8 jobblogg.no
dig @1.1.1.1 jobblogg.no

# Test fra forskjellige lokasjoner
curl -I https://jobblogg.no
curl -I https://staging.jobblogg.no
```

---

## 🔒 Fase 4: SSL og Proxy Aktivering

### 4.1 Vent på SSL Provisioning
- Cloudflare genererer automatisk SSL sertifikater
- Kan ta 5-15 minutter
- Verifiser: `curl -I https://jobblogg.no`

### 4.2 Aktiver Proxy (Orange Sky)
**NÅR SSL fungerer**, aktiver proxy:

```
jobblogg.no: Proxy 🟠 ON (orange sky)
staging.jobblogg.no: Proxy 🟠 ON (orange sky)
api.jobblogg.no: Proxy 🟠 ON (orange sky)
```

### 4.3 Cloudflare SSL Settings
```
SSL/TLS Mode: Full (Strict)
Edge Certificates: Universal SSL (aktivert)
Always Use HTTPS: ON
```

---

## 🧪 Fase 5: Verifisering og Testing

### 5.1 Smoke Test
```bash
# Kjør infrastruktur smoke test
curl -s https://jobblogg.no/api/health          # Skal returnere: OK
curl -s https://staging.jobblogg.no/api/health  # Skal returnere: OK
curl -I https://api.jobblogg.no                 # Skal returnere: HTTP/2 200
```

### 5.2 SSL Test
```bash
# Verifiser SSL sertifikater
openssl s_client -connect jobblogg.no:443 -servername jobblogg.no < /dev/null 2>/dev/null | openssl x509 -noout -issuer -subject

# Test HTTPS redirect
curl -I http://jobblogg.no  # Skal redirecte til HTTPS
```

### 5.3 Performance Test
```bash
# Test responstider
time curl -I https://jobblogg.no
time curl -I https://staging.jobblogg.no
```

---

## 🔄 Rollback Plan (Hvis noe går galt)

### Umiddelbar Rollback
1. **Endre nameservere tilbake** til original DNS provider
2. **Vent 5-15 minutter** på propagering
3. **Verifiser** at original oppsett fungerer

### Delvis Rollback
- Deaktiver Cloudflare proxy (grå sky) men behold DNS
- Endre TTL til 60 sekunder for rask endring

---

## 📊 Cloudflare Optimalisering (Etter cutover)

### Caching Rules
```
Rule 1: Cache HTML
- URL: jobblogg.no/*
- Cache Level: Standard
- Browser TTL: 4 hours
- Edge TTL: 1 hour

Rule 2: Cache Assets
- URL: *.js, *.css, *.png, *.jpg
- Cache Level: Cache Everything  
- Browser TTL: 1 year
- Edge TTL: 1 month
```

### Security Settings
```
Security Level: Medium
Bot Fight Mode: ON
Browser Integrity Check: ON
Challenge Passage: 30 minutes
```

---

## ✅ Post-Cutover Checklist

- [ ] Alle domener responderer med HTTPS
- [ ] Health checks returnerer "OK"
- [ ] SSL sertifikater er gyldige (Cloudflare utstedt)
- [ ] Responstider er akseptable (<2 sekunder)
- [ ] Ingen 502/503 feil
- [ ] Email records oppdatert (hvis relevant)
- [ ] Clerk subdomain konfigurert (hvis relevant)
- [ ] Monitoring og alerting fungerer

---

## 📞 Kontaktinformasjon

**Cloudflare Support**: https://support.cloudflare.com  
**DNS Propagering Check**: https://dnschecker.org  
**SSL Test**: https://www.ssllabs.com/ssltest/

---

*Opprettet: 24. august 2025*  
*Klar for implementering når DNS skal flyttes*
