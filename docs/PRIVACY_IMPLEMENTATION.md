# Privacy Implementation Guide for JobbLogg

## Oversikt

Dette dokumentet beskriver hvordan personvernfunksjonaliteten er implementert i JobbLogg-applikasjonen.

## Implementerte komponenter

### 1. Juridiske sider
- **Personvernerklæring** (`/privacy-policy`) - Detaljert beskrivelse av databehandling
- **Cookie-erklæring** (`/cookie-policy`) - Informasjon om bruk av cookies
- **Bruk<PERSON>vilkår** (`/terms-of-service`) - <PERSON><PERSON><PERSON><PERSON> betingelser for bruk

### 2. Cookie-samtykke system
- **CookieBanner** - Modal for cookie-samtykke med granulær kontroll
- **CookieSettings** - Administrasjon av cookie-preferanser
- **useCookieConsent** - Hook for cookie-samtykke logikk

### 3. Brukerrettigheter
- **PrivacySettings** (`/privacy-settings`) - Personverninnstillinger og datarettigheter
- Dataeksport i JSON-format
- Kontosletting (simulert i demo)
- <PERSON><PERSON> til retting og begrensning av data

### 4. Layout og navigasjon
- **Footer** - <PERSON><PERSON> til alle personvernsider
- **PageLayout** - Oppdatert med footer-støtte
- Routing for alle nye sider

## Teknisk implementering

### Cookie-samtykke
```typescript
// Bruk av cookie-samtykke hook
const { consent, showBanner, acceptAll, isAllowed } = useCookieConsent();

// Sjekk om spesifikk cookie-type er tillatt
if (isAllowed('analytics')) {
  // Kjør analytics-kode
}
```

### Dataeksport
```typescript
// Eksempel på dataeksport (implementert i PrivacySettings)
const handleExportData = async () => {
  const userData = {
    user: userInfo,
    projects: userProjects,
    messages: userMessages,
    exportDate: new Date().toISOString()
  };
  
  // Last ned som JSON-fil
  downloadAsJSON(userData, 'jobblogg-data-export.json');
};
```

### Cookie-lagring
```typescript
// Cookie-samtykke lagres i localStorage
const COOKIE_CONSENT_KEY = 'jobblogg-cookie-consent';
const consent = {
  necessary: true,    // Alltid true
  functional: false,  // Brukervalg
  analytics: false,   // Brukervalg
  marketing: false,   // Brukervalg
  timestamp: Date.now(),
  version: '1.0'
};
```

## Filstruktur

```
src/
├── components/
│   ├── CookieConsent/
│   │   ├── CookieBanner.tsx
│   │   ├── CookieSettings.tsx
│   │   └── index.ts
│   └── ui/Layout/
│       └── Footer.tsx
├── hooks/
│   └── useCookieConsent.ts
├── pages/
│   └── Privacy/
│       ├── PrivacyPolicy.tsx
│       ├── CookiePolicy.tsx
│       ├── TermsOfService.tsx
│       ├── PrivacySettings.tsx
│       └── index.ts
└── docs/
    ├── GDPR_COMPLIANCE.md
    └── PRIVACY_IMPLEMENTATION.md
```

## Ruter

### Offentlige ruter (ingen autentisering)
- `/privacy-policy` - Personvernerklæring
- `/cookie-policy` - Cookie-erklæring  
- `/terms-of-service` - Brukervilkår

### Beskyttede ruter (krever innlogging)
- `/privacy-settings` - Personverninnstillinger og datarettigheter

## Styling og design

Alle komponenter følger JobbLogg designsystemet:
- Bruker `jobblogg-*` tokens for farger
- Mobile-first responsive design
- WCAG AA-kompatible kontraster
- Konsistent spacing og typografi

## Testing

### Manuell testing
1. **Cookie-banner**: Sjekk at banner vises ved første besøk
2. **Cookie-innstillinger**: Test granulær kontroll av cookies
3. **Dataeksport**: Verifiser at JSON-fil lastes ned
4. **Responsive design**: Test på mobile og desktop
5. **Navigasjon**: Sjekk alle lenker i footer

### Automatiserte tester (anbefalt)
```typescript
// Eksempel på test for cookie-samtykke
describe('Cookie Consent', () => {
  it('should show banner on first visit', () => {
    // Test implementering
  });
  
  it('should save preferences to localStorage', () => {
    // Test implementering  
  });
});
```

## Compliance sjekkliste

### GDPR-krav ✅
- [x] Personvernerklæring
- [x] Cookie-erklæring  
- [x] Brukervilkår
- [x] Cookie-samtykke banner
- [x] Brukerrettigheter (innsyn, retting, sletting, portabilitet)
- [x] Kontaktinformasjon for personvern
- [x] Lenke til Datatilsynet

### Tekniske krav ✅
- [x] Granulær cookie-kontroll
- [x] Persistent lagring av samtykke
- [x] Dataeksport-funksjonalitet
- [x] Kontosletting (simulert)
- [x] Responsive design
- [x] Tilgjengelig navigasjon

### Mangler (må implementeres i produksjon)
- [ ] Faktisk dataeksport fra Convex database
- [ ] Faktisk kontosletting med Convex mutations
- [ ] E-post varsling ved databrudd
- [ ] Logging av personvernhendelser
- [ ] Integrasjon med faktiske analytics-tjenester

## Vedlikehold

### Regelmessige oppgaver
- **Månedlig**: Sjekk at alle lenker fungerer
- **Kvartalsvis**: Oppdater juridiske dokumenter ved behov
- **Årlig**: Gjennomgå og oppdater personvernerklæring

### Ved endringer
- Oppdater personvernerklæring ved nye databehandlinger
- Oppdater cookie-erklæring ved nye cookies
- Øk versjonsnummer på cookie-samtykke ved endringer

## Support og kontakt

For spørsmål om personvernimplementeringen:
- **E-post**: <EMAIL>
- **Dokumentasjon**: Se `docs/GDPR_COMPLIANCE.md`
- **Kode**: Se `src/components/CookieConsent/` og `src/pages/Privacy/`
