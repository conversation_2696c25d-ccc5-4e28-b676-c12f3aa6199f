# 🔧 Staging Deployment Troubleshooting Guide

## 🚨 Common Exit Codes and Solutions

### Exit Code 2: Deployment Failure
**Causes:**
- SSH connection failure
- Missing Docker/Docker Compose
- Repository clone/update failure
- Docker Compose build failure
- Missing staging server setup

**Solutions:**
1. **Check SSH Connection:**
   ```bash
   ssh root@your-staging-host "echo 'Connection test'"
   ```

2. **Verify Server Setup:**
   ```bash
   # On staging server
   docker --version
   docker-compose --version
   ls -la /opt/jobblogg-staging/
   ```

3. **Check GitHub Secrets:**
   - `STAGING_HOST` - Server IP/domain
   - `HETZNER_SSH_KEY` - SSH private key
   - `STAGING_PASSWORD` - Basic auth password

### Exit Code 1: Health Check Failure
**Causes:**
- Services not starting properly
- Network connectivity issues
- SSL certificate problems
- Basic auth configuration issues

**Solutions:**
1. **Check Container Status:**
   ```bash
   cd /opt/jobblogg-staging
   docker-compose -f docker-compose.staging.yml ps
   docker-compose -f docker-compose.staging.yml logs
   ```

2. **Test Basic Auth:**
   ```bash
   curl -u "staging:your-password" https://staging.jobblogg.no/
   ```

## 🔍 Debugging Steps

### 1. Check GitHub Actions Logs
- Go to: https://github.com/djrobbieh/JobbLogg/actions
- Click on failed workflow run
- Expand "Deploy to Staging" step
- Look for specific error messages

### 2. SSH into Staging Server
```bash
ssh root@your-staging-host
cd /opt/jobblogg-staging
docker-compose -f docker-compose.staging.yml logs --tail=50
```

### 3. Verify Environment Variables
```bash
# Check if .env.staging was created
cat .env.staging

# Verify all required variables are set
grep -E "(CONVEX|CLERK|STRIPE|RESEND|GOOGLE)" .env.staging
```

## 🛠️ Manual Deployment Steps

If automated deployment fails, try manual deployment:

```bash
# 1. SSH to staging server
ssh root@your-staging-host

# 2. Navigate to staging directory
cd /opt/jobblogg-staging

# 3. Update code
git pull origin main

# 4. Stop existing containers
docker-compose -f docker-compose.staging.yml down

# 5. Build and start
docker-compose -f docker-compose.staging.yml up -d --build

# 6. Check status
docker-compose -f docker-compose.staging.yml ps
docker-compose -f docker-compose.staging.yml logs
```

## 📋 Pre-Deployment Checklist

- [ ] Staging server accessible via SSH
- [ ] Docker and Docker Compose installed
- [ ] All GitHub secrets configured
- [ ] Domain pointing to staging server
- [ ] SSL certificate configured
- [ ] Basic auth configured
- [ ] Repository accessible from server

## 🆘 Emergency Recovery

If staging is completely broken:

```bash
# 1. Stop all containers
docker-compose -f docker-compose.staging.yml down

# 2. Clean up
docker system prune -f
docker volume prune -f

# 3. Fresh start
rm -rf /opt/jobblogg-staging
mkdir -p /opt/jobblogg-staging
cd /opt/jobblogg-staging
git clone https://github.com/djrobbieh/JobbLogg.git .

# 4. Redeploy
docker-compose -f docker-compose.staging.yml up -d --build
```
