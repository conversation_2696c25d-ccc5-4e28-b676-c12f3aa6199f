# JobbLogg Subdomain Configuration

## 📋 Eksterne Tjenester Subdomain Setup

**Formål**: Guide for å konfigurere subdomener for Clerk og email tjenester når Cloudflare er aktivt.  
**Forutsetning**: Cloudflare DNS er aktivt og fungerer

---

## 🔐 Clerk Subdomain (clerk.jobblogg.no)

### 1. Hent Clerk Domain Verdier

**I Clerk Dashboard**:
1. Gå til **Configure** → **Domains**
2. Klikk **Add domain**
3. Skriv inn: `clerk.jobblogg.no`
4. Clerk vil vise DNS records som må legges til

**Typiske verdier** (eksempel):
```
Type: CNAME
Name: clerk
Content: clerk.accounts.dev
```

### 2. Legg til i Cloudflare

**I Cloudflare Dashboard**:
1. Gå til **DNS** → **Records**
2. Klikk **Add record**
3. **Type**: CNAME
4. **Name**: clerk
5. **Content**: [verdi fra Clerk]
6. **Proxy**: 🔴 OFF (grå sky) - Viktig!
7. **TTL**: 300 (5 minutter)

### 3. Verifiser Clerk Domain

**I Clerk Dashboard**:
1. Klikk **Verify domain**
2. Vent på DNS propagering (5-15 minutter)
3. Clerk vil bekrefte når domain er aktivt

**Test**:
```bash
# Sjekk DNS propagering
dig clerk.jobblogg.no

# Test HTTPS (etter Clerk aktivering)
curl -I https://clerk.jobblogg.no
```

---

## 📧 Email Tjenester (Resend)

### 1. Hent Resend Domain Verdier

**I Resend Dashboard**:
1. Gå til **Domains**
2. Klikk **Add Domain**
3. Skriv inn: `jobblogg.no`
4. Resend vil vise DNS records som må legges til

**Typiske verdier** (eksempel):
```
# SPF Record
Type: TXT
Name: @
Content: "v=spf1 include:_spf.resend.com ~all"

# DKIM Record
Type: TXT
Name: resend._domainkey
Content: "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC..."

# DMARC Record (valgfritt)
Type: TXT
Name: _dmarc
Content: "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
```

### 2. Legg til Email Records i Cloudflare

**SPF Record**:
1. **Type**: TXT
2. **Name**: @ (eller jobblogg.no)
3. **Content**: `"v=spf1 include:_spf.resend.com ~all"`
4. **TTL**: 300

**DKIM Record**:
1. **Type**: TXT
2. **Name**: resend._domainkey
3. **Content**: [DKIM key fra Resend]
4. **TTL**: 300

**DMARC Record** (anbefalt):
1. **Type**: TXT
2. **Name**: _dmarc
3. **Content**: `"v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"`
4. **TTL**: 300

### 3. Verifiser Email Domain

**I Resend Dashboard**:
1. Klikk **Verify domain**
2. Vent på DNS propagering (15-30 minutter)
3. Resend vil bekrefte når alle records er korrekte

**Test DNS Records**:
```bash
# Test SPF
dig TXT jobblogg.no | grep spf

# Test DKIM
dig TXT resend._domainkey.jobblogg.no

# Test DMARC
dig TXT _dmarc.jobblogg.no
```

---

## 🧪 Verifisering og Testing

### DNS Propagering Test
```bash
# Sjekk alle subdomener
dig clerk.jobblogg.no
dig TXT jobblogg.no
dig TXT resend._domainkey.jobblogg.no
dig TXT _dmarc.jobblogg.no

# Test fra forskjellige DNS servere
dig @8.8.8.8 clerk.jobblogg.no
dig @1.1.1.1 clerk.jobblogg.no
```

### Email Deliverability Test
```bash
# Test SPF record
nslookup -type=TXT jobblogg.no | grep spf

# Online tools (anbefalt)
# https://mxtoolbox.com/spf.aspx
# https://dmarcian.com/dmarc-inspector/
```

### Clerk Authentication Test
```bash
# Test Clerk subdomain (etter aktivering)
curl -I https://clerk.jobblogg.no

# Sjekk SSL sertifikat
openssl s_client -connect clerk.jobblogg.no:443 -servername clerk.jobblogg.no < /dev/null
```

---

## 🔧 Feilsøking

### Clerk Domain Problemer
- **Problem**: "Domain not verified"
- **Løsning**: Sjekk at CNAME peker til riktig Clerk endpoint
- **Viktig**: Proxy må være OFF (grå sky) i Cloudflare

### Email Delivery Problemer
- **Problem**: Emails går til spam
- **Løsning**: Verifiser alle SPF, DKIM og DMARC records
- **Test**: Bruk online email testing tools

### DNS Propagering Tar Tid
- **Problem**: Records ikke synlige ennå
- **Løsning**: Vent 15-30 minutter, sjekk forskjellige DNS servere
- **Tip**: Bruk https://dnschecker.org for global propagering

---

## 📊 Subdomain Oversikt

| Subdomain | Type | Formål | Status |
|-----------|------|--------|--------|
| **clerk.jobblogg.no** | CNAME | Clerk authentication | Konfigureres ved behov |
| **@** (SPF) | TXT | Email sending policy | Konfigureres ved behov |
| **resend._domainkey** | TXT | Email DKIM signature | Konfigureres ved behov |
| **_dmarc** | TXT | Email DMARC policy | Konfigureres ved behov |

---

## 📞 Support Ressurser

**Clerk Documentation**: https://clerk.com/docs/deployments/custom-domains  
**Resend Documentation**: https://resend.com/docs/dashboard/domains/introduction  
**DNS Testing Tools**: https://dnschecker.org, https://mxtoolbox.com  
**Email Testing**: https://mail-tester.com

---

*Opprettet: 24. august 2025*  
*Klar for implementering når eksterne tjenester skal konfigureres*
