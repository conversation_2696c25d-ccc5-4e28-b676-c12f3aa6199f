# GitHub Actions CI/CD Setup Guide

## 🚀 Oversikt

Denne guiden forklarer hvordan du setter opp GitHub Actions for automatisert deployment av JobbLogg.

---

## 📋 Forutsetninger

### Server Setup
1. **Server**: Ubuntu 24.04 LTS (************)
2. **Docker**: Installert og kjørende
3. **UFW**: Konfigurert med porter 22, 80, 443
4. **Fail2ban**: Aktivert for SSH beskyttelse
5. **Bruker**: `github-actions` bruker opprettet

### Repository Setup
1. **GitHub Repository**: https://github.com/djrobbieh/JobbLogg
2. **Branch Protection**: Main branch beskyttet
3. **Environments**: Staging og Production konfigurert

---

## 🔐 GitHub Secrets Konfigurering

### Repository Secrets (Settings → Secrets and variables → Actions)

#### SSH Tilgang
```
STAGING_SSH_KEY
- SSH private key for staging deployment
- Genereres med: ssh-keygen -t ed25519 -C "github-actions-staging"

PRODUCTION_SSH_KEY  
- SSH private key for production deployment
- Genereres med: ssh-keygen -t ed25519 -C "github-actions-production"

STAGING_HOST
- Verdi: ************

PRODUCTION_HOST
- Verdi: ************
```

#### Container Registry (Valgfritt)
```
DOCKER_REGISTRY_TOKEN
- GitHub Personal Access Token for GHCR
- Scope: write:packages, read:packages
```

#### Notifications (Valgfritt)
```
SLACK_WEBHOOK_URL
- Slack webhook for deployment notifications
```

### Environment Secrets

#### Staging Environment
```
CONVEX_URL_STAGING
VITE_CONVEX_URL_STAGING
VITE_CLERK_PUBLISHABLE_KEY_STAGING
VITE_STRIPE_PUBLISHABLE_KEY_STAGING
STRIPE_SECRET_KEY_STAGING
STRIPE_WEBHOOK_SECRET_STAGING
RESEND_API_KEY_STAGING
```

#### Production Environment
```
CONVEX_URL
VITE_CONVEX_URL
VITE_CLERK_PUBLISHABLE_KEY
VITE_STRIPE_PUBLISHABLE_KEY
STRIPE_SECRET_KEY
STRIPE_WEBHOOK_SECRET
RESEND_API_KEY
```

---

## 🛠️ Server Setup

### 1. Kjør Setup Script
```bash
# På serveren som root
cd /opt
git clone https://github.com/djrobbieh/JobbLogg.git jobblogg
cd jobblogg
chmod +x scripts/setup-ci-cd.sh
./scripts/setup-ci-cd.sh
```

### 2. Konfigurer SSH Nøkler
```bash
# Generer SSH nøkkelpar lokalt
ssh-keygen -t ed25519 -C "github-actions-staging" -f ~/.ssh/github-actions-staging
ssh-keygen -t ed25519 -C "github-actions-production" -f ~/.ssh/github-actions-production

# Kopier public keys til server
ssh-copy-id -i ~/.ssh/github-actions-staging.pub github-actions@************
ssh-copy-id -i ~/.ssh/github-actions-production.pub github-actions@************

# Legg private keys til GitHub Secrets
cat ~/.ssh/github-actions-staging    # → STAGING_SSH_KEY
cat ~/.ssh/github-actions-production # → PRODUCTION_SSH_KEY
```

### 3. Konfigurer Environment Variables
```bash
# På serveren som github-actions bruker
sudo -u github-actions nano /opt/jobblogg/.env.staging
sudo -u github-actions nano /opt/jobblogg/.env.production
```

---

## 🔄 Workflow Oversikt

### Automatiske Triggers
```yaml
Push til main branch:
  → Run tests
  → Build Docker images  
  → Deploy til staging
  → Health check staging
  → (Venter på manual approval)
  → Deploy til production
  → Health check production

Pull Request:
  → Run tests
  → Security scan
  → Code quality check
  → Comment på PR med resultater

Scheduled (Daily 2 AM):
  → Security scan
  → Dependency audit
```

### Manual Triggers
```yaml
Workflow Dispatch:
  → Velg environment (staging/production)
  → Velg om tests skal hoppes over
  → Deploy til valgt environment

Rollback:
  → Manual trigger ved feil
  → Automatisk rollback ved health check feil
```

---

## 🧪 Testing Pipeline

### Lokal Testing
```bash
# Test build lokalt
npm run build

# Test linting
npm run lint

# Test type checking
npm run type-check

# Test import validation
npm run validate:imports
```

### Staging Testing
```bash
# Test staging deployment
curl -f https://staging.jobblogg.no/api/health

# Test staging functionality
# (Manual testing av kritiske features)
```

### Production Verification
```bash
# Test production deployment
curl -f https://jobblogg.no/api/health

# Monitor logs
ssh github-actions@************ 'docker-compose -f /opt/jobblogg/docker-compose.prod.yml logs -f'
```

---

## 🚨 Troubleshooting

### Deployment Feil
```bash
# Sjekk deployment logs
ssh github-actions@************ 'cd /opt/jobblogg && docker-compose logs'

# Sjekk service status
ssh github-actions@************ 'cd /opt/jobblogg && docker-compose ps'

# Manual rollback
ssh github-actions@************ '/opt/jobblogg/scripts/deployment/rollback.sh production'
```

### SSH Problemer
```bash
# Test SSH tilgang
ssh -i ~/.ssh/github-actions-staging github-actions@************ 'whoami'

# Sjekk SSH nøkler på server
ssh github-actions@************ 'cat ~/.ssh/authorized_keys'

# Sjekk SSH agent
ssh-add -l
```

### Environment Variable Problemer
```bash
# Sjekk environment filer
ssh github-actions@************ 'ls -la /opt/jobblogg/.env*'

# Test environment loading
ssh github-actions@************ 'cd /opt/jobblogg && docker-compose config'
```

---

## 📊 Monitoring

### Health Checks
```bash
# Staging health
curl -f https://staging.jobblogg.no/api/health

# Production health  
curl -f https://jobblogg.no/api/health

# Detailed health (hvis implementert)
curl -f https://jobblogg.no/api/health/detailed
```

### Logs
```bash
# Application logs
ssh github-actions@************ 'cd /opt/jobblogg && docker-compose logs -f --tail=100'

# System logs
ssh github-actions@************ 'journalctl -u jobblogg-monitor.service -f'

# Deployment logs
ssh github-actions@************ 'tail -f /opt/jobblogg/logs/deployment.log'
```

### Metrics
```bash
# Container status
ssh github-actions@************ 'docker stats'

# Disk usage
ssh github-actions@************ 'df -h'

# Memory usage
ssh github-actions@************ 'free -h'
```

---

## 🔧 Maintenance

### Backup Management
```bash
# List backups
ssh github-actions@************ 'ls -la /opt/jobblogg/backups/'

# Manual backup
ssh github-actions@************ '/opt/jobblogg/scripts/deployment/backup.sh production'

# Restore from backup
ssh github-actions@************ '/opt/jobblogg/scripts/deployment/rollback.sh production'
```

### Log Rotation
```bash
# Check log rotation config
ssh github-actions@************ 'cat /etc/logrotate.d/jobblogg'

# Manual log rotation
ssh github-actions@************ 'sudo logrotate -f /etc/logrotate.d/jobblogg'
```

### Updates
```bash
# Update system packages
ssh github-actions@************ 'sudo apt update && sudo apt upgrade -y'

# Update Docker images
ssh github-actions@************ 'cd /opt/jobblogg && docker-compose pull'

# Cleanup old images
ssh github-actions@************ 'docker system prune -f'
```

---

## ✅ Deployment Checklist

### Pre-deployment
- [ ] Tests passerer lokalt
- [ ] Code review fullført
- [ ] Environment variables oppdatert
- [ ] Database migrations klar (hvis nødvendig)
- [ ] Backup tatt

### During Deployment
- [ ] Staging deployment suksess
- [ ] Staging health check OK
- [ ] Manual testing på staging
- [ ] Production approval gitt
- [ ] Production deployment suksess
- [ ] Production health check OK

### Post-deployment
- [ ] Smoke tests kjørt
- [ ] Monitoring sjekket
- [ ] Error rates normale
- [ ] Performance metrics OK
- [ ] Rollback plan klar

---

*CI/CD Pipeline Setup Guide - Oppdatert 25. august 2025*
