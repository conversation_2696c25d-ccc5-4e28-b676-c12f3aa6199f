# 🔐 GitHub Secrets Setup for Hetzner Server

**Formål**: Konfigurere GitHub Secrets for automatisk deployment til eksisterende Hetzner server  
**Forutsetninger**: Tilgang til GitHub repository og Hetzner server

---

## 🎯 Oversikt

For å aktivere automatisk deployment til deres eksisterende Hetzner server (jobblogg.no), må følgende GitHub Secrets konfigureres:

---

## 🔑 Påkrevde GitHub Secrets

### 1. Server Tilgang
```bash
HETZNER_SERVER_IP=your-hetzner-server-ip
HETZNER_SSH_KEY=your-hetzner-private-ssh-key
```

### 2. Production Environment Variables
```bash
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k
VITE_CONVEX_URL=https://api.jobblogg.no
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[PRODUCTION_KEY]
```

---

## 🚀 Steg-for-Steg Setup

### Steg 1: Gå til GitHub Repository Settings

1. Gå til https://github.com/djrobbieh/JobbLogg
2. Klikk på **Settings** (øverst til høyre)
3. I venstre sidebar, klikk **Secrets and variables** → **Actions**

### Steg 2: Legg til Server Tilgang Secrets

#### A. HETZNER_SERVER_IP
1. Klikk **New repository secret**
2. **Name**: `HETZNER_SERVER_IP`
3. **Secret**: Din Hetzner server IP-adresse
4. Klikk **Add secret**

#### B. HETZNER_SSH_KEY
1. Klikk **New repository secret**
2. **Name**: `HETZNER_SSH_KEY`
3. **Secret**: Din private SSH nøkkel (hele innholdet av private key filen)
4. Klikk **Add secret**

**Hvordan få SSH nøkkel:**
```bash
# På din lokale maskin (hvis du har tilgang til serveren)
cat ~/.ssh/id_rsa

# Eller generer ny nøkkel spesifikt for GitHub Actions
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/jobblogg_deploy
cat ~/.ssh/jobblogg_deploy

# Legg til public key på serveren
ssh-copy-id -i ~/.ssh/jobblogg_deploy.pub root@your-hetzner-server-ip
```

### Steg 3: Legg til Environment Variables

#### A. VITE_CLERK_PUBLISHABLE_KEY
1. **Name**: `VITE_CLERK_PUBLISHABLE_KEY`
2. **Secret**: `pk_live_Y2xlcmsuam9iYmxvZ2cubm8k` (fra eksisterende setup)

#### B. VITE_CONVEX_URL
1. **Name**: `VITE_CONVEX_URL`
2. **Secret**: `https://api.jobblogg.no` (fra eksisterende setup)

#### C. VITE_GOOGLE_MAPS_API_KEY
1. **Name**: `VITE_GOOGLE_MAPS_API_KEY`
2. **Secret**: `AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs` (fra eksisterende setup)

#### D. VITE_STRIPE_PUBLISHABLE_KEY
1. **Name**: `VITE_STRIPE_PUBLISHABLE_KEY`
2. **Secret**: Din Stripe production publishable key

---

## 🔍 Verifisering

### Sjekk at alle secrets er lagt til:
1. Gå til **Settings** → **Secrets and variables** → **Actions**
2. Du skal se følgende secrets:
   - ✅ `HETZNER_SERVER_IP`
   - ✅ `HETZNER_SSH_KEY`
   - ✅ `VITE_CLERK_PUBLISHABLE_KEY`
   - ✅ `VITE_CONVEX_URL`
   - ✅ `VITE_GOOGLE_MAPS_API_KEY`
   - ✅ `VITE_STRIPE_PUBLISHABLE_KEY`

---

## 🧪 Test Deployment

### Manuell Test:
1. Gå til **Actions** tab i GitHub repository
2. Velg **Deploy** workflow
3. Klikk **Run workflow**
4. Velg **production** environment
5. Klikk **Run workflow**

### Automatisk Test:
1. Push en endring til `main` branch
2. Workflows vil kjøre automatisk:
   - ✅ Security Scan
   - ✅ Test & Quality Check
   - ⏳ Deploy (krever manuell godkjenning for produksjon)

---

## 🔧 Deployment Prosess

### Hva skjer under deployment:

1. **Backup**: Lager backup av eksisterende Docker image
2. **Code Update**: Henter siste kode fra main branch
3. **Build**: Bygger ny Docker image med production environment variables
4. **Deploy**: Stopper gammel container og starter ny
5. **Health Check**: Verifiserer at applikasjonen kjører
6. **Verification**: Tester at https://jobblogg.no er tilgjengelig

### Deployment kommandoer (automatisk):
```bash
# På Hetzner serveren (automatisk via GitHub Actions)
cd /root/JobbLogg
git pull origin main

docker build --no-cache \
  --target production \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY="..." \
  --build-arg VITE_CONVEX_URL="..." \
  --build-arg VITE_GOOGLE_MAPS_API_KEY="..." \
  --build-arg VITE_STRIPE_PUBLISHABLE_KEY="..." \
  -f docker/frontend/Dockerfile \
  -t jobblogg-frontend-production:latest .

docker stop jobblogg-prod || true
docker rm jobblogg-prod || true

docker run -d \
  --name jobblogg-prod \
  --restart unless-stopped \
  -p 3000:3000 \
  -e NODE_ENV=production \
  jobblogg-frontend-production:latest
```

---

## 🚨 Troubleshooting

### Problem: SSH Connection Failed
**Løsning:**
1. Sjekk at `HETZNER_SERVER_IP` er korrekt
2. Verifiser at SSH nøkkel er riktig format (inkluderer `-----BEGIN` og `-----END` linjer)
3. Test SSH tilkobling manuelt: `ssh root@your-server-ip`

### Problem: Build Failed
**Løsning:**
1. Sjekk at alle environment variables er satt
2. Verifiser at Dockerfile eksisterer: `docker/frontend/Dockerfile`
3. Test build lokalt først

### Problem: Health Check Failed
**Løsning:**
1. Sjekk at container kjører: `docker ps`
2. Sjekk container logs: `docker logs jobblogg-prod`
3. Verifiser at port 3000 er tilgjengelig

---

## 🎉 Ferdig!

Når alle secrets er konfigurert, vil CI/CD pipeline automatisk:
- ✅ Kjøre security og quality checks på hver commit
- ✅ Deploye til Hetzner server når main branch oppdateres
- ✅ Verifisere at deployment var vellykket
- ✅ Holde https://jobblogg.no oppdatert med siste kode

**Neste steg**: Test første automatiske deployment! 🚀

---

*GitHub Secrets setup for Hetzner server fullført!*
