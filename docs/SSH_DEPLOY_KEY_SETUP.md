# 🔑 SSH Deploy Key Setup for JobbLogg Staging Deployment

## 🚨 Problem: Git Authentication in Non-Interactive Environment

The staging deployment fails with this error:
```
fatal: could not read Password for 'https://***@github.com': No such device or address
```

**Root Cause:** HTTPS authentication with GitHub token fails in non-interactive environments (no TTY) because Git cannot prompt for passwords.

**Solution:** Use SSH Deploy Key authentication for reliable, non-interactive repository access.

---

## 🔧 Complete Setup Guide

### Step 1: Generate SSH Deploy Key

Run the setup script or generate manually:

```bash
# Option A: Use setup script
./scripts/setup-ssh-deploy-key.sh

# Option B: Generate manually
ssh-keygen -t ed25519 -C "jobblogg-staging-deploy-key" -f ~/.ssh/jobblogg_deploy_key -N ""
```

This creates:
- `~/.ssh/jobblogg_deploy_key` (private key)
- `~/.ssh/jobblogg_deploy_key.pub` (public key)

### Step 2: Add Deploy Key to GitHub Repository

1. **Go to Repository Settings:**
   ```
   https://github.com/djrobbieh/JobbLogg/settings/keys
   ```

2. **Click "Add deploy key"**

3. **Configure Deploy Key:**
   - **Title:** `Staging Server Deploy Key`
   - **Key:** Paste contents of `~/.ssh/jobblogg_deploy_key.pub`
   - **Allow write access:** ✅ Check this box
   - **Click "Add key"**

### Step 3: Add Private Key to GitHub Secrets

1. **Go to Repository Secrets:**
   ```
   https://github.com/djrobbieh/JobbLogg/settings/secrets/actions
   ```

2. **Click "New repository secret"**

3. **Configure Secret:**
   - **Name:** `JOBBLOGG_DEPLOY_KEY`
   - **Value:** Paste entire contents of `~/.ssh/jobblogg_deploy_key` (including BEGIN/END lines)
   - **Click "Add secret"**

### Step 4: Setup SSH Key on Staging Server

SSH into your staging server and run:

```bash
# Create SSH directory
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Add GitHub host key
ssh-keyscan -H github.com >> ~/.ssh/known_hosts

# Create deploy key file
nano ~/.ssh/jobblogg_deploy_key
# Paste the private key content here

# Set correct permissions
chmod 600 ~/.ssh/jobblogg_deploy_key

# Test SSH connection
ssh -T -i ~/.ssh/jobblogg_deploy_key **************
```

**Expected test output:**
```
Hi djrobbieh/JobbLogg! You've successfully authenticated, but GitHub does not provide shell access.
```

---

## 🔄 How It Works

### Current Workflow Behavior:

1. **SSH Deploy Key (Preferred):**
   - Checks for `~/.ssh/jobblogg_deploy_key` on staging server
   - Uses SSH authentication: `**************:djrobbieh/JobbLogg.git`
   - No password prompts, works in non-interactive environment

2. **HTTPS Fallback:**
   - If SSH key not found, uses HTTPS with GitHub token
   - Enhanced with `GIT_TERMINAL_PROMPT=0` to prevent prompts
   - Should work better than previous implementation

### Authentication Flow:
```bash
# 1. Try SSH Deploy Key
if [ -f ~/.ssh/jobblogg_deploy_key ]; then
  eval "$(ssh-agent -s)"
  ssh-add ~/.ssh/jobblogg_deploy_key
  <NAME_EMAIL>:djrobbieh/JobbLogg.git .
  
# 2. Fallback to HTTPS
elif [ -n "$GITHUB_TOKEN" ]; then
  export GIT_TERMINAL_PROMPT=0
  git clone https://github.com/djrobbieh/JobbLogg.git .
fi
```

---

## ✅ Verification Steps

### 1. Test SSH Connection from Staging Server:
```bash
ssh -T -i ~/.ssh/jobblogg_deploy_key **************
```

### 2. Test Git Clone:
```bash
cd /tmp
<NAME_EMAIL>:djrobbieh/JobbLogg.git test-clone
rm -rf test-clone
```

### 3. Trigger Deployment:
- Push to main branch
- Check GitHub Actions for successful deployment
- Look for "Using SSH Deploy Key authentication..." in logs

---

## 🔒 Security Benefits

- **No password storage** on staging server
- **Limited scope** - deploy key only has access to JobbLogg repository
- **Read-only or limited write access** as configured
- **Revocable** - can be removed from GitHub repository settings
- **Audit trail** - GitHub logs all deploy key usage

---

## 🛠️ Troubleshooting

### SSH Connection Issues:
```bash
# Test with verbose output
ssh -T -i ~/.ssh/jobblogg_deploy_key -v **************

# Check SSH agent
ssh-add -l

# Verify key permissions
ls -la ~/.ssh/jobblogg_deploy_key
```

### Git Clone Issues:
```bash
# Test manual clone
GIT_SSH_COMMAND="ssh -i ~/.ssh/jobblogg_deploy_key" <NAME_EMAIL>:djrobbieh/JobbLogg.git
```

---

## 🎯 Expected Results

After setup, staging deployment should show:
```
🔐 Using SSH Deploy Key authentication...
📥 Cloning private repository via SSH...
📊 Current commit: [hash]
📝 Creating staging environment file...
```

This eliminates the "No such device or address" error and provides reliable authentication for private repository access in automated deployments.
