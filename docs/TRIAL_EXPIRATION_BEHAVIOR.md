# JobbLogg Trial Expiration Behavior

## Overview
This document defines the complete trial expiration workflow for JobbLogg, including functionality access, user experience, data access, grace periods, and upgrade paths.

## Trial Lifecycle

### 1. Active Trial (Days 1-7)
**Status:** `trialing`
**Duration:** 7 days from trial start
**Access Level:** Full access to all features

**Functionality:**
- ✅ Create unlimited projects
- ✅ Full project management features
- ✅ Team collaboration and chat
- ✅ Customer sharing and communication
- ✅ All subscription features based on selected plan
- ✅ Plan switching during trial

**User Experience:**
- Trial progress indicator in navigation
- Plan selection and switching available
- No restrictions or limitations

### 2. Trial Expired - Grace Period (Days 8-10)
**Status:** `past_due` 
**Duration:** 3 days after trial expiration
**Access Level:** Read-only with upgrade prompts

**Functionality:**
- ❌ Cannot create new projects
- ❌ Cannot edit existing projects
- ❌ Cannot send new messages in chat
- ❌ Cannot invite team members
- ✅ View existing projects (read-only)
- ✅ View chat history and messages
- ✅ Download project data
- ✅ Access customer information
- ✅ View team member information

**User Experience:**
- Warning banners on all pages
- Upgrade prompts for blocked actions
- Grace period countdown (X days remaining)
- Clear messaging about limited access
- Prominent upgrade call-to-action

### 3. Grace Period Expired (Day 11+)
**Status:** `canceled`
**Duration:** Indefinite until upgrade
**Access Level:** Minimal read-only access

**Functionality:**
- ❌ Cannot create new projects
- ❌ Cannot edit existing projects  
- ❌ Cannot send new messages in chat
- ❌ Cannot invite team members
- ❌ Limited project viewing (summary only)
- ✅ Download project data (for migration)
- ✅ View basic account information
- ✅ Access upgrade options

**User Experience:**
- Full-page upgrade prompts for most actions
- Limited dashboard with upgrade focus
- Data export options available
- Clear path to reactivate subscription

## Access Control Matrix

| Feature | Active Trial | Grace Period | Canceled |
|---------|-------------|--------------|----------|
| Create Projects | ✅ Full | ❌ Blocked | ❌ Blocked |
| Edit Projects | ✅ Full | ❌ Blocked | ❌ Blocked |
| View Projects | ✅ Full | ✅ Read-only | ⚠️ Limited |
| Chat/Messages | ✅ Full | ✅ Read-only | ✅ Read-only |
| Team Management | ✅ Full | ❌ Blocked | ❌ Blocked |
| Customer Sharing | ✅ Full | ❌ Blocked | ❌ Blocked |
| Data Export | ✅ Full | ✅ Full | ✅ Full |
| Plan Management | ✅ Full | ✅ Upgrade only | ✅ Upgrade only |

## User Experience Guidelines

### Grace Period Messaging
**Primary Message:** "Prøveperioden er utløpt"
**Secondary Message:** "Du har X dager igjen med begrenset tilgang"
**Call-to-Action:** "Oppgrader nå for å fortsette å bruke alle funksjoner"

### Canceled State Messaging  
**Primary Message:** "Prøveperioden har utløpt"
**Secondary Message:** "Oppgrader til en betalt plan for å fortsette å bruke JobbLogg"
**Call-to-Action:** "Se abonnementsplaner"

### Norwegian Localization
- **Trial expired:** "Prøveperioden er utløpt"
- **Grace period:** "Du har X dager igjen med begrenset tilgang"
- **Limited access:** "Begrenset tilgang"
- **Read-only:** "Kun lesing"
- **Upgrade now:** "Oppgrader nå"
- **View plans:** "Se abonnementsplaner"

## Implementation Details

### Backend Logic (convex/subscriptions.ts)
```typescript
// Trial expiration check
const isTrialExpired = subscription.status === "trialing" && 
                      subscription.trialEnd && 
                      subscription.trialEnd < now;

// Grace period check  
const isInGracePeriod = subscription.status === "past_due";

// Access permissions
const canCreateProjects = hasActiveSubscription || isInTrial;
const canAccessProjects = hasActiveSubscription || isInTrial || isInGracePeriod;
const hasFullAccess = hasActiveSubscription || isInTrial;
const isReadOnly = isInGracePeriod || isTrialExpired;
```

### Frontend Components
- **TrialExpiredPrompt:** Handles all trial expiration messaging
- **SubscriptionGate:** Enforces access control based on trial status
- **TrialStatus:** Shows trial progress and expiration warnings

### Automated Processes
- **Daily cron job:** Checks for expired trials and updates status
- **Email notifications:** Sent at trial expiration and grace period warnings
- **Status transitions:** Automatic progression through trial lifecycle

## Data Retention Policy

### During Grace Period
- All project data remains accessible (read-only)
- Chat history preserved
- Team member information retained
- Customer data maintained

### After Grace Period
- Project data remains in database
- Access severely limited but data not deleted
- Full restoration upon subscription reactivation
- Export functionality available for migration

## Upgrade Path

### From Grace Period
1. User clicks "Oppgrader nå" button
2. Redirected to Stripe customer portal
3. Selects plan and completes payment
4. Immediate restoration of full access
5. All data and functionality restored

### From Canceled State
1. User accesses upgrade options
2. Guided through plan selection
3. Payment processing via Stripe
4. Account reactivation
5. Full feature restoration

## Testing Scenarios

### Manual Testing
1. **Trial expiration simulation:** Modify trial end date in database
2. **Grace period testing:** Set status to "past_due" 
3. **Access control verification:** Test each feature with different statuses
4. **UI component testing:** Verify all prompts and messages display correctly

### Automated Testing
- Unit tests for access control logic
- Integration tests for trial lifecycle
- UI tests for expiration prompts
- End-to-end upgrade flow testing

## Monitoring and Analytics

### Key Metrics
- Trial completion rate
- Grace period conversion rate  
- Time to upgrade after expiration
- Feature usage during grace period
- Churn rate after grace period

### Alerts
- High trial expiration rate
- Low grace period conversion
- Technical issues with upgrade flow
- Payment processing failures

## Support Considerations

### Common User Questions
1. **"Why can't I create projects?"** → Trial expired, show upgrade options
2. **"How long do I have left?"** → Display grace period countdown
3. **"Will I lose my data?"** → Explain data retention policy
4. **"How do I upgrade?"** → Guide through upgrade process

### Support Actions
- Manual trial extensions (exceptional cases)
- Data export assistance
- Upgrade troubleshooting
- Account reactivation support
