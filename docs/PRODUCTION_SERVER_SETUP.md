# 🖥️ JobbLogg Production Server Setup Guide

**Formål**: Sette opp produksjonsserver for JobbLogg med automatisk deployment  
**Estimert tid**: 2-3 timer  
**Forutsetninger**: Ubuntu 22.04 LTS server

---

## 🎯 Server Spesifikasjoner

### Anbefalt Konfigurasjon:
- **Provider**: DigitalOcean, Linode, eller AWS EC2
- **CPU**: 2 vCPU (minimum), 4 vCPU (anbefalt)
- **RAM**: 4GB (minimum), 8GB (anbefalt)  
- **Storage**: 40GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Nettverk**: 1Gbps

### Estimerte Kostnader:
- **DigitalOcean**: $24/måned (4GB RAM, 2 vCPU)
- **Linode**: $24/måned (4GB RAM, 2 vCPU)
- **AWS EC2**: $25-30/måned (t3.medium)

---

## 🚀 Steg 1: Server Oppsett

### 1.1 Opprett Server
```bash
# Eksempel med DigitalOcean CLI
doctl compute droplet create jobblogg-prod \
  --image ubuntu-22-04-x64 \
  --size s-2vcpu-4gb \
  --region fra1 \
  --ssh-keys your-ssh-key-id
```

### 1.2 Initial Server Konfigurasjon
```bash
# Koble til server
ssh root@your-server-ip

# Oppdater system
apt update && apt upgrade -y

# Opprett deploy bruker
adduser deploy
usermod -aG sudo deploy
usermod -aG docker deploy

# Kopier SSH nøkler
mkdir -p /home/<USER>/.ssh
cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
chown -R deploy:deploy /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

---

## 🐳 Steg 2: Docker Installasjon

```bash
# Installer Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Installer Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verifiser installasjon
docker --version
docker-compose --version

# Start Docker service
systemctl enable docker
systemctl start docker
```

---

## 🌐 Steg 3: Nginx og SSL Setup

### 3.1 Installer Nginx
```bash
apt install nginx certbot python3-certbot-nginx -y
systemctl enable nginx
systemctl start nginx
```

### 3.2 Konfigurer Nginx
```bash
# Opprett konfigurasjon
cat > /etc/nginx/sites-available/jobblogg << 'EOF'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

# Aktiver site
ln -s /etc/nginx/sites-available/jobblogg /etc/nginx/sites-enabled/
rm /etc/nginx/sites-enabled/default
nginx -t
systemctl reload nginx
```

### 3.3 SSL Sertifikat
```bash
# Installer SSL sertifikat
certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatisk fornyelse
certbot renew --dry-run
```

---

## 🔐 Steg 4: Sikkerhet

### 4.1 Firewall Konfigurasjon
```bash
# Konfigurer UFW firewall
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 'Nginx Full'
ufw --force enable
```

### 4.2 SSH Sikkerhet
```bash
# Rediger SSH konfigurasjon
nano /etc/ssh/sshd_config

# Endre følgende linjer:
# PermitRootLogin no
# PasswordAuthentication no
# PubkeyAuthentication yes

# Restart SSH
systemctl restart ssh
```

### 4.3 Fail2Ban
```bash
# Installer Fail2Ban
apt install fail2ban -y

# Konfigurer
cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
EOF

systemctl enable fail2ban
systemctl start fail2ban
```

---

## 📁 Steg 5: Deployment Directory

```bash
# Bytt til deploy bruker
su - deploy

# Opprett deployment directory
mkdir -p /home/<USER>/jobblogg
cd /home/<USER>/jobblogg

# Opprett nødvendige directories
mkdir -p logs data backups
```

---

## 🔑 Steg 6: Environment Variables

```bash
# Opprett environment fil
cat > /home/<USER>/jobblogg/.env << 'EOF'
# Application
NODE_ENV=production
PORT=3000

# Convex Backend
VITE_CONVEX_URL=your-convex-url
CONVEX_DEPLOY_KEY=your-convex-deploy-key

# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
CLERK_SECRET_KEY=your-clerk-secret-key

# Stripe Payments
VITE_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# External APIs
BRREG_API_KEY=your-brreg-api-key
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Database (hvis brukt)
DATABASE_URL=your-database-url
EOF

# Sikre environment fil
chmod 600 .env
```

---

## 🐳 Steg 7: Docker Compose Produksjon

```bash
# Opprett docker-compose.prod.yml
cat > /home/<USER>/jobblogg/docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  jobblogg:
    image: jobblogg:latest
    container_name: jobblogg-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=300
    command: jobblogg-prod
EOF
```

---

## 📊 Steg 8: Monitoring Setup

### 8.1 Health Check Endpoint
```bash
# Test health endpoint (etter deployment)
curl http://localhost:3000/health
```

### 8.2 Log Monitoring
```bash
# Opprett log rotation
cat > /etc/logrotate.d/jobblogg << 'EOF'
/home/<USER>/jobblogg/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

---

## 🔄 Steg 9: Backup Strategy

```bash
# Opprett backup script
cat > /home/<USER>/jobblogg/backup.sh << 'EOF'
#!/bin/bash
# JobbLogg Backup Script

BACKUP_DIR="/home/<USER>/jobblogg/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup environment file
cp .env "$BACKUP_DIR/env_$DATE.backup"

# Backup data directory
tar -czf "$BACKUP_DIR/data_$DATE.tar.gz" data/

# Cleanup old backups (keep 7 days)
find $BACKUP_DIR -name "*.backup" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "$(date): Backup completed" >> logs/backup.log
EOF

chmod +x backup.sh

# Daglig backup
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/jobblogg/backup.sh") | crontab -
```

---

## ✅ Steg 10: Deployment Test

### 10.1 GitHub Secrets Konfigurasjon
Gå til GitHub repository → Settings → Secrets and variables → Actions

Legg til følgende secrets:
```
SSH_HOST=your-server-ip
SSH_USERNAME=deploy
SSH_PRIVATE_KEY=your-private-ssh-key
```

### 10.2 Test Deployment
```bash
# Trigger deployment fra GitHub Actions
# Eller test manuelt:
cd /home/<USER>/jobblogg
docker-compose -f docker-compose.prod.yml up -d

# Sjekk status
docker ps
curl http://localhost:3000/health
```

---

## 🎉 Ferdig!

Server er nå klar for produksjon med:
- ✅ Automatisk deployment via GitHub Actions
- ✅ SSL sertifikat og sikkerhet
- ✅ Monitoring og logging
- ✅ Backup strategi
- ✅ Health checks

**Neste steg**: Test full deployment pipeline fra GitHub Actions!

---

*Produksjonsserver setup fullført! 🚀*
