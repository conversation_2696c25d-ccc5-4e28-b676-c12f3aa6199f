# 🔑 GitHub Secrets Configuration Template

## Required Secrets for Staging Deployment

Copy this template and fill in your actual values, then add each secret to:
`https://github.com/djrobbieh/JobbLogg/settings/secrets/actions`

### 🖥️ Server & Infrastructure
```
STAGING_HOST=your-staging-server-ip-or-domain
HETZNER_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
your-private-key-content-here
-----END OPENSSH PRIVATE KEY-----
STAGING_PASSWORD=your-basic-auth-password
```

### 🔗 Convex Backend (Staging)
```
VITE_CONVEX_URL_STAGING=https://your-staging-convex-deployment.convex.cloud
CONVEX_URL_STAGING=https://your-staging-convex-deployment.convex.cloud
```

### 🔐 Clerk Authentication (Staging)
```
VITE_CLERK_PUBLISHABLE_KEY_STAGING=pk_test_your-clerk-staging-key
```

### 💳 Stripe Payment (Staging - Use Test Keys)
```
VITE_STRIPE_PUBLISHABLE_KEY_STAGING=pk_test_your-stripe-test-publishable-key
STRIPE_SECRET_KEY_STAGING=sk_test_your-stripe-test-secret-key
STRIPE_WEBHOOK_SECRET_STAGING=whsec_your-stripe-webhook-secret
```

### 📧 Email Service (Staging)
```
RESEND_API_KEY_STAGING=re_your-resend-staging-api-key
```

### 🗺️ Shared Services
```
VITE_GOOGLE_MAPS_API_KEY=AIza_your-google-maps-api-key
```

## 🔧 How to Add Secrets

1. Go to: https://github.com/djrobbieh/JobbLogg/settings/secrets/actions
2. Click "New repository secret"
3. Enter secret name (exactly as shown above)
4. Paste the secret value
5. Click "Add secret"
6. Repeat for all 11 secrets

## ✅ Verification

After adding all secrets, the staging deployment will:
- ✅ Run automatically on push to main
- ✅ Deploy to your staging server
- ✅ Be accessible at https://staging.jobblogg.no
- ✅ Require basic auth (staging:your-password)

## 🚨 Security Notes

- Use TEST/STAGING keys for all services (never production keys)
- Keep SSH private key secure
- Use strong passwords for basic auth
- Regularly rotate API keys
- Monitor staging server access logs
