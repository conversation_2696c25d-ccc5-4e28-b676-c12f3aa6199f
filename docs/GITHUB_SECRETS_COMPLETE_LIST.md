# 🔐 Complete GitHub Secrets List for JobbLogg

**Formål**: <PERSON><PERSON><PERSON> oversikt over alle GitHub Secrets som trengs for CI/CD pipeline  
**Repository**: https://github.com/djrobbieh/JobbLogg/settings/secrets/actions

---

## 📋 **ALLE PÅKREVDE SECRETS**

### 🖥️ **SERVER TILGANG (4 secrets)**

#### Staging Server:
```bash
STAGING_HOST=your-hetzner-server-ip
STAGING_SSH_KEY=<content of ~/.ssh/jobblogg-staging private key>
STAGING_USERNAME=staging-deploy
STAGING_PASSWORD=c(mS5jEO7!PT5B>!4'
```

#### Production Server (Hetzner):
```bash
HETZNER_SERVER_IP=your-hetzner-server-ip
HETZNER_SSH_KEY=<content of ~/.ssh/jobblogg-production private key>
```

---

### 🔧 **STAGING ENVIRONMENT VARIABLES (6 secrets)**

```bash
VITE_CONVEX_URL_STAGING=https://your-staging-deployment.convex.cloud
VITE_CLERK_PUBLISHABLE_KEY_STAGING=pk_test_staging_key_here
VITE_STRIPE_PUBLISHABLE_KEY_STAGING=pk_test_staging_stripe_key
STRIPE_SECRET_KEY_STAGING=sk_test_staging_stripe_key
RESEND_API_KEY_STAGING=your_staging_resend_key
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs
```

---

### 🎯 **PRODUCTION ENVIRONMENT VARIABLES (4 secrets)**

```bash
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k
VITE_CONVEX_URL=https://api.jobblogg.no
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[PRODUCTION_KEY]
# VITE_GOOGLE_MAPS_API_KEY already defined above (shared)
```

---

## 🎯 **TOTAL: 14 GITHUB SECRETS**

### Sjekkliste:
- [ ] **STAGING_HOST** - Hetzner server IP
- [ ] **STAGING_SSH_KEY** - Private SSH key for staging
- [ ] **STAGING_USERNAME** - staging-deploy
- [ ] **STAGING_PASSWORD** - Basic auth password
- [ ] **HETZNER_SERVER_IP** - Same as staging host
- [ ] **HETZNER_SSH_KEY** - Private SSH key for production
- [ ] **VITE_CONVEX_URL_STAGING** - Staging Convex URL
- [ ] **VITE_CLERK_PUBLISHABLE_KEY_STAGING** - Staging Clerk key
- [ ] **VITE_STRIPE_PUBLISHABLE_KEY_STAGING** - Staging Stripe key
- [ ] **STRIPE_SECRET_KEY_STAGING** - Staging Stripe secret
- [ ] **RESEND_API_KEY_STAGING** - Staging email key
- [ ] **VITE_GOOGLE_MAPS_API_KEY** - Google Maps (shared)
- [ ] **VITE_CLERK_PUBLISHABLE_KEY** - Production Clerk key
- [ ] **VITE_CONVEX_URL** - Production Convex URL
- [ ] **VITE_STRIPE_PUBLISHABLE_KEY** - Production Stripe key

---

## 🚀 **QUICK SETUP GUIDE**

### 1. Generate SSH Keys:
```bash
./scripts/generate-ssh-keys.sh
```

### 2. Setup Server SSH Access:
```bash
# Staging
ssh root@your-hetzner-server-ip
mkdir -p /opt/jobblogg-staging/.ssh
echo 'your-staging-public-key' >> /opt/jobblogg-staging/.ssh/authorized_keys

# Production (already setup)
echo 'your-production-public-key' >> /root/.ssh/authorized_keys
```

### 3. Add GitHub Secrets:
1. Go to: https://github.com/djrobbieh/JobbLogg/settings/secrets/actions
2. Click "New repository secret" for each secret above
3. Copy exact values from SSH keys and environment configs

### 4. Test Deployment:
1. Go to GitHub Actions
2. Run "Deploy" workflow
3. Select "staging" environment
4. Verify deployment works

---

## 🔍 **SECRET VALUES REFERENCE**

### SSH Keys (generate with script):
```bash
# After running ./scripts/generate-ssh-keys.sh
STAGING_SSH_KEY=$(cat ~/.ssh/jobblogg-staging)
HETZNER_SSH_KEY=$(cat ~/.ssh/jobblogg-production)
```

### Known Values:
```bash
STAGING_USERNAME=staging-deploy
STAGING_PASSWORD=c(mS5jEO7!PT5B>!4'
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k
VITE_CONVEX_URL=https://api.jobblogg.no
```

### Values You Need to Provide:
```bash
STAGING_HOST=your-hetzner-server-ip
HETZNER_SERVER_IP=your-hetzner-server-ip
VITE_CONVEX_URL_STAGING=your-staging-convex-url
VITE_CLERK_PUBLISHABLE_KEY_STAGING=your-staging-clerk-key
VITE_STRIPE_PUBLISHABLE_KEY_STAGING=your-staging-stripe-key
STRIPE_SECRET_KEY_STAGING=your-staging-stripe-secret
RESEND_API_KEY_STAGING=your-staging-resend-key
VITE_STRIPE_PUBLISHABLE_KEY=your-production-stripe-key
```

---

## 🧪 **TESTING SECRETS**

### Test SSH Connection:
```bash
# Test staging
ssh -i ~/.ssh/jobblogg-staging staging-deploy@your-server-ip

# Test production
ssh -i ~/.ssh/jobblogg-production root@your-server-ip
```

### Test GitHub Actions:
1. Push to main branch (triggers staging)
2. Manual trigger production deployment
3. Check logs for any missing secrets

---

## 🚨 **TROUBLESHOOTING**

### Missing Secret Error:
```
Error: Secret STAGING_SSH_KEY not found
```
**Solution**: Add the missing secret to GitHub repository settings

### SSH Connection Failed:
```
Permission denied (publickey)
```
**Solution**: 
1. Verify SSH key is correctly added to server
2. Check key format in GitHub Secrets (include -----BEGIN/END lines)
3. Test SSH connection manually

### Environment Variable Not Set:
```
Error: VITE_CONVEX_URL_STAGING is not defined
```
**Solution**: Add the missing environment variable to GitHub Secrets

---

## 🎉 **COMPLETION**

When all 14 secrets are configured:
- ✅ Automatic staging deployment on main branch push
- ✅ Manual production deployment with approval
- ✅ Secure SSH connections without passwords
- ✅ Environment-specific configurations
- ✅ Health checks and rollback capabilities

**Status**: Ready for production deployment! 🚀

---

*Complete GitHub Secrets setup for JobbLogg CI/CD pipeline*
