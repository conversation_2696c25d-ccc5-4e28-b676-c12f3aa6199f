# JobbLogg Infrastructure Smoke Test

## 📋 Minimal Røyktest for Miljøene

**Formål**: Rask verifisering av at alle miljøer responderer korrekt etter deployment eller endringer.

---

## 🧪 Test 1: Produksjon Health Check

**Kommando**:
```bash
curl -s https://jobblogg.no/api/health
```

**Forventet resultat**: `OK`  
**Status kode**: 200  
**Beskrivelse**: Verifiserer at produksjonsmiljøet er tilgjengelig uten å treffe React-containeren.

---

## 🧪 Test 2: Staging Health Check

**Kommando**:
```bash
curl -s https://staging.jobblogg.no/api/health
```

**Forventet resultat**: `OK`  
**Status kode**: 200  
**Beskrivelse**: Verifiserer at staging-miljøet er tilgjengelig uten å treffe React-containeren.

---

## 🧪 Test 3: Mock API Tilgjengelighet

**Kommando**:
```bash
curl -I https://api.jobblogg.no
```

**Forventet resultat**: `HTTP/2 200`  
**Beskrivelse**: Verifiserer at mock Convex API på port 3211 responderer.

---

## 🚀 Rask Verifisering (Alle tester)

**Kjør alle tester på en gang**:
```bash
echo "=== JOBBLOGG INFRASTRUCTURE SMOKE TEST ==="
echo ""

echo "1. Produksjon Health:"
curl -s https://jobblogg.no/api/health
echo ""

echo "2. Staging Health:"
curl -s https://staging.jobblogg.no/api/health
echo ""

echo "3. Mock API Status:"
curl -I https://api.jobblogg.no 2>/dev/null | head -1
echo ""

echo "=== TEST FULLFØRT ==="
```

**Forventet output**:
```
=== JOBBLOGG INFRASTRUCTURE SMOKE TEST ===

1. Produksjon Health:
OK

2. Staging Health:
OK

3. Mock API Status:
HTTP/2 200

=== TEST FULLFØRT ===
```

---

## 🔧 Feilsøking

### Health Check Feiler
- **Problem**: Returnerer HTML i stedet for "OK"
- **Årsak**: Caddy `handle /api/health` regel mangler eller feil
- **Løsning**: Sjekk `/etc/caddy/Caddyfile` og reload Caddy

### Mock API Feiler
- **Problem**: Connection refused eller 502
- **Årsak**: Mock Convex container ikke kjører på port 3211
- **Løsning**: `docker ps | grep mock-convex` og restart container

### SSL/TLS Feil
- **Problem**: Certificate errors
- **Årsak**: Caddy ikke generert sertifikater ennå
- **Løsning**: Vent 1-2 minutter og prøv igjen

---

## 📊 Automatisering

**Cron job for daglig test** (valgfritt):
```bash
# Legg til i crontab: crontab -e
0 6 * * * /root/smoke-test.sh >> /var/log/smoke-test.log 2>&1
```

**Script**: `/root/smoke-test.sh`
```bash
#!/bin/bash
DATE=$(date '+%Y-%m-%d %H:%M:%S')
echo "[$DATE] Starting smoke test..."

PROD_HEALTH=$(curl -s https://jobblogg.no/api/health)
STAGING_HEALTH=$(curl -s https://staging.jobblogg.no/api/health)
API_STATUS=$(curl -I https://api.jobblogg.no 2>/dev/null | head -1)

if [[ "$PROD_HEALTH" == "OK" && "$STAGING_HEALTH" == "OK" && "$API_STATUS" == *"200"* ]]; then
    echo "[$DATE] ✅ All tests passed"
else
    echo "[$DATE] ❌ Tests failed: Prod=$PROD_HEALTH, Staging=$STAGING_HEALTH, API=$API_STATUS"
fi
```

---

*Opprettet: 24. august 2025*  
*Sist oppdatert: 24. august 2025*
