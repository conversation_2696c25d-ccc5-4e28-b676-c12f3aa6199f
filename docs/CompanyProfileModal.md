# Company Profile Modal - Technical Documentation

## Component Overview

### Purpose and Functionality
The `CompanyProfileModal` component provides a comprehensive interface for contractors to view and edit their company profile information. It serves as the primary management interface for contractor company data within the JobbLogg application.

**Key Features:**
- Display and edit company information (name, contact details, address)
- Integration with Brønnøysundregisteret (Norwegian Business Registry)
- Intelligent field locking for registry-sourced data
- Dynamic field labeling based on organizational structure
- Comprehensive form validation and error handling

### Access Method
The modal is accessed via the Clerk user button in the application header:
```
User Avatar → "Bedriftsprofil" menu option
```

### Integration with Contractor Onboarding
The component reuses robust logic patterns from the contractor onboarding wizard:
- Form validation and error handling
- Field locking mechanisms for Brønnøysundregisteret data
- Custom address toggle functionality
- Dynamic field labeling and string conversion
- Auto-population with proper data handling

## Component Interface

### Props
```typescript
interface CompanyProfileModalProps {
  isOpen: boolean;           // Controls modal visibility
  onClose: () => void;       // Callback when modal is closed
  onSuccess?: () => void;    // Optional callback on successful update
}
```

### Form Data Structure
```typescript
interface CompanyFormData {
  companyName: string;       // Company name
  orgNumber: string;         // Organization number (9 digits)
  contactPerson: string;     // Contact person name
  phone: string;             // Phone number (8 digits, stored with +47 prefix)
  email: string;             // Email address
  streetAddress: string;     // Street address
  postalCode: string;        // Postal code
  city: string;              // City name
  entrance: string;          // Entrance/floor information
  notes: string;             // Additional notes
  useCustomAddress: boolean; // Toggle for custom vs registry address
}
```

## Field Documentation

### Company Information Section

#### 1. Company Name (`companyName`)
- **Type**: `TextInput` (editable) or `LockedInput` (registry data)
- **Validation**: Required field
- **Locking Rule**: Locked when organization number exists (indicates registered company)
- **Helper Text**: 
  - Locked: "Registrert bedrift - navn kan ikke endres" or Brønnøysundregisteret timestamp
  - Editable: None
- **Placeholder**: "Skriv inn bedriftsnavn"

#### 2. Organization Number (`orgNumber`)
- **Type**: `LockedInput` (always locked)
- **Validation**: Required, must be exactly 9 digits
- **Locking Rule**: Always locked (cannot be changed after registration)
- **Helper Text**: "Organisasjonsnummer kan ikke endres"
- **Format**: Displayed as entered, stored without spaces

### Contact Information Section

#### 3. Contact Person (`contactPerson`)
- **Type**: `TextInput` (editable) or `LockedInput` (registry data)
- **Dynamic Label**: 
  - "Daglig leder" (Managing Director)
  - "Innehaver" (Owner)
  - Fallback: "Daglig leder"
- **Validation**: Required field with dynamic error message
- **Locking Rule**: Locked when Brønnøysundregisteret managing director data exists
- **Helper Text**: 
  - Locked: Brønnøysundregisteret timestamp
  - Editable: None
- **Placeholder**: "Skriv inn kontaktperson"

#### 4. Phone Number (`phone`)
- **Type**: `PhoneInput` (editable) or `LockedInput` (registry data)
- **Dynamic Label**:
  - "Mobilnummer" (Mobile number) - default and for 'mobil' source
  - "Telefonnummer" (Phone number) - for 'telefon' source
- **Validation**: Optional, must be 8 digits if provided
- **Format**: Stored with +47 prefix, displayed without prefix in input
- **Locking Rule**: Locked when Brønnøysundregisteret registry contact phone exists
- **Placeholder**: "12345678"

#### 5. Email Address (`email`)
- **Type**: `TextInput` (editable) or `LockedInput` (registry data)
- **Label**: "E-postadresse"
- **Validation**: Optional, must be valid email format if provided
- **Locking Rule**: Locked when Brønnøysundregisteret registry contact email exists
- **Placeholder**: "<EMAIL>"

### Address Section

#### 6. Street Address (`streetAddress`)
- **Type**: `TextInput` (editable) or `LockedInput` (registry data)
- **Label**: "Gateadresse"
- **Validation**: Optional
- **Locking Rule**: Locked when Brønnøysundregisteret address data exists AND custom address toggle is OFF
- **Placeholder**: "Storgata 15"

#### 7. Postal Code (`postalCode`)
- **Type**: `TextInput` (editable) or `LockedInput` (registry data)
- **Label**: "Postnummer"
- **Validation**: Optional
- **Locking Rule**: Same as street address
- **Placeholder**: "0123"

#### 8. City (`city`)
- **Type**: `TextInput` (editable) or `LockedInput` (registry data)
- **Label**: "Poststed"
- **Validation**: Optional
- **Locking Rule**: Same as street address
- **Placeholder**: "Oslo"

#### 9. Entrance/Floor (`entrance`)
- **Type**: `TextInput` (always editable)
- **Label**: "Inngang/etasje"
- **Validation**: Optional
- **Locking Rule**: Never locked (always user-customizable)
- **Placeholder**: "Inngang, etasje, etc."

#### 10. Custom Address Toggle (`useCustomAddress`)
- **Type**: `ToggleSwitch`
- **Label**: "Bruk tilpasset adresse"
- **Function**: When enabled, unlocks address fields for custom input
- **Default**: OFF (use registry address)

### Notes Section

#### 11. Notes (`notes`)
- **Type**: `TextArea`
- **Label**: "Notater"
- **Validation**: Optional
- **Locking Rule**: Never locked
- **Placeholder**: "Tilleggsnotater om bedriften..."
- **Rows**: 4

## Brønnøysundregisteret Integration

### Data Structure
The `brregData` object contains information fetched from the Norwegian Business Registry:

```typescript
interface CompanyInfo {
  name?: string;                    // Company name from registry
  organizationNumber?: string;      // Organization number
  organizationForm?: string;        // Legal form (e.g., "Aksjeselskap")
  organizationFormCode?: string;    // Form code (e.g., "AS")
  
  managingDirector?: {
    fullName?: string;              // Full name of managing director
    firstName?: string;             // First name
    lastName?: string;              // Last name
    roleDescription?: string;       // Role description (e.g., "Daglig leder")
  };
  
  registryContact?: {
    phone?: string;                 // Phone from registry
    email?: string;                 // Email from registry
    phoneSource?: 'mobil' | 'telefon'; // Source type for dynamic labeling
  };
  
  visitingAddress?: {
    street?: string;                // Visiting address street
    postalCode?: string;            // Postal code
    city?: string;                  // City
  };
  
  businessAddress?: {
    street?: string;                // Business address (fallback)
    postalCode?: string;
    city?: string;
  };
}
```

### Field Mapping
| Registry Field | Form Field | Notes |
|----------------|------------|-------|
| `name` | `companyName` | Auto-populated, locked when present |
| `managingDirector.fullName` | `contactPerson` | Auto-populated, locked when present |
| `registryContact.phone` | `phone` | Auto-populated, locked when present |
| `registryContact.email` | `email` | Auto-populated, locked when present |
| `visitingAddress.*` | Address fields | Auto-populated, locked unless custom toggle enabled |
| `businessAddress.*` | Address fields | Fallback if visiting address not available |

### Dynamic Field Labeling
The component uses registry data to provide contextually appropriate field labels:

#### Contact Person Label Logic
```typescript
const getContactPersonLabel = (): string => {
  // Use role description from registry if available
  if (brregData?.managingDirector?.roleDescription) {
    return brregData.managingDirector.roleDescription;
  }
  // Default fallback
  return 'Daglig leder';
};
```

#### Phone Label Logic
```typescript
const getPhoneLabel = (): string => {
  const phoneSource = brregData?.registryContact?.phoneSource;
  if (phoneSource === 'mobil') {
    return 'Mobilnummer';
  } else if (phoneSource === 'telefon') {
    return 'Telefonnummer';
  }
  return 'Mobilnummer'; // Default
};
```

## Field Locking System

### Auto-Populated Fields State
The component maintains an `autoPopulatedFields` state object that controls which fields are locked:

```typescript
interface AutoPopulatedFields {
  companyName: boolean;      // Lock company name
  contactPerson: boolean;    // Lock contact person
  phone: boolean;           // Lock phone number
  email: boolean;           // Lock email address
  streetAddress: boolean;   // Lock street address
  postalCode: boolean;      // Lock postal code
  city: boolean;           // Lock city
}
```

### Field Locking Rules

#### Company Name
- **Locked when**: Organization number exists (indicates registered company)
- **Logic**: `hasOrgNumber = !!contractorCompany.orgNumber`
- **Visual**: Gray background, lock icon, helper text

#### Contact Person
- **Locked when**: Brønnøysundregisteret managing director data exists
- **Logic**:
  ```typescript
  const shouldLockContactPerson = (): boolean => {
    // If we have managing director data from Brønnøysundregisteret
    if (contractorCompany.brregData?.managingDirector) {
      return true;
    }
    // If dynamic labeling would show role-specific label
    if (brregData?.managingDirector?.roleDescription) {
      return true;
    }
    return false;
  };
  ```
- **Visual**: Gray background, lock icon, Brønnøysundregisteret timestamp

#### Phone Number
- **Locked when**: Brønnøysundregisteret registry contact phone exists
- **Logic**: `hasBreregData && !!contractorCompany.brregData?.registryContact?.phone`
- **Visual**: Gray background, lock icon, Brønnøysundregisteret timestamp

#### Email Address
- **Locked when**: Brønnøysundregisteret registry contact email exists
- **Logic**: `hasBreregData && !!contractorCompany.brregData?.registryContact?.email`
- **Visual**: Gray background, lock icon, Brønnøysundregisteret timestamp

#### Address Fields (Street, Postal Code, City)
- **Locked when**:
  - Brønnøysundregisteret address data exists AND
  - Custom address toggle is OFF AND
  - Address data is available (visiting or business address)
- **Logic**:
  ```typescript
  streetAddress: hasBreregData &&
                !contractorCompany.useCustomAddress &&
                !!(contractorCompany.brregData?.visitingAddress?.street ||
                   contractorCompany.brregData?.businessAddress?.street)
  ```
- **Visual**: Gray background, lock icon, Brønnøysundregisteret timestamp

#### Never Locked Fields
- **Organization Number**: Always locked (different visual treatment)
- **Entrance/Floor**: Always editable (user customization)
- **Notes**: Always editable (user customization)

### Visual Indicators

#### Locked Fields (`LockedInput`)
- **Background**: Gray (`bg-jobblogg-neutral`)
- **Icon**: Lock icon in the field
- **Helper Text**:
  - With timestamp: "Hentet fra Brønnøysundregisteret DD.MM.YYYY HH:MM"
  - Without timestamp: "Hentet fra Brønnøysundregisteret"
  - Organization number: "Organisasjonsnummer kan ikke endres"
  - Company name: "Registrert bedrift - navn kan ikke endres"

#### Editable Fields
- **Background**: White
- **Icon**: None
- **Helper Text**: None (unless validation error)

## State Management

### Form Data Loading
When the modal opens, form data is loaded from the contractor company record:

```typescript
useEffect(() => {
  if (isOpen && contractorCompany) {
    // Load form data with safe string conversion
    setFormData({
      companyName: safeStringConvert(contractorCompany.name),
      orgNumber: safeStringConvert(contractorCompany.orgNumber),
      contactPerson: safeStringConvert(contractorCompany.contactPerson),
      phone: safeStringConvert(contractorCompany.phone).replace('+47 ', '') || '',
      email: safeStringConvert(contractorCompany.email),
      streetAddress: safeStringConvert(contractorCompany.streetAddress),
      postalCode: safeStringConvert(contractorCompany.postalCode),
      city: safeStringConvert(contractorCompany.city),
      entrance: safeStringConvert(contractorCompany.entrance),
      notes: safeStringConvert(contractorCompany.notes),
      useCustomAddress: contractorCompany.useCustomAddress || false
    });

    // Set Brønnøysundregisteret data if available
    if (contractorCompany.brregData) {
      setBrregData(contractorCompany.brregData as CompanyInfo);
      setBrregFetchedAt(contractorCompany.brregFetchedAt || null);
    }

    // Determine field locking based on available data
    const hasBreregData = !!contractorCompany.brregFetchedAt || !!contractorCompany.brregData;
    const hasOrgNumber = !!contractorCompany.orgNumber;

    setAutoPopulatedFields({
      companyName: hasOrgNumber,
      contactPerson: shouldLockContactPerson(),
      phone: hasBreregData && !!contractorCompany.brregData?.registryContact?.phone,
      email: hasBreregData && !!contractorCompany.brregData?.registryContact?.email,
      streetAddress: hasBreregData && !contractorCompany.useCustomAddress &&
                    !!(contractorCompany.brregData?.visitingAddress?.street ||
                       contractorCompany.brregData?.businessAddress?.street),
      postalCode: hasBreregData && !contractorCompany.useCustomAddress &&
                 !!(contractorCompany.brregData?.visitingAddress?.postalCode ||
                    contractorCompany.brregData?.businessAddress?.postalCode),
      city: hasBreregData && !contractorCompany.useCustomAddress &&
           !!(contractorCompany.brregData?.visitingAddress?.city ||
              contractorCompany.brregData?.businessAddress?.city)
    });

    setErrors({});
    setSuccessMessage('');
  }
}, [isOpen, contractorCompany]);
```

### Error Handling
The component maintains an errors state object for field-specific validation errors:

```typescript
const [errors, setErrors] = useState<{ [key: string]: string }>({});
```

### Success Messages
Success messages are displayed after successful form submission:

```typescript
const [successMessage, setSuccessMessage] = useState<string>('');
```

### Modal State Management
- **Opening**: Loads data, resets errors, sets field locking
- **Closing**: Clears form data, resets state
- **Submission**: Validates, saves, shows success message

## Validation Rules

### Required Fields
- **Company Name**: Must not be empty
- **Organization Number**: Must be exactly 9 digits
- **Contact Person**: Must not be empty (with dynamic error message)

### Optional Fields with Validation
- **Email**: Must be valid email format if provided
- **Phone**: Must be exactly 8 digits if provided

### Validation Logic
```typescript
const validateForm = (): boolean => {
  const newErrors: { [key: string]: string } = {};

  if (!formData.companyName.trim()) {
    newErrors.companyName = 'Bedriftsnavn er påkrevd';
  }

  if (!formData.orgNumber.trim()) {
    newErrors.orgNumber = 'Organisasjonsnummer er påkrevd';
  } else if (!/^\d{9}$/.test(formData.orgNumber.replace(/\s/g, ''))) {
    newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
  }

  if (!formData.contactPerson.trim()) {
    newErrors.contactPerson = getContactPersonValidationError();
  }

  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    newErrors.email = 'Ugyldig e-postadresse';
  }

  if (formData.phone && !/^\d{8}$/.test(formData.phone.replace(/\s/g, ''))) {
    newErrors.phone = 'Telefonnummer må være 8 siffer';
  }

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};
```

## Technical Implementation

### Key Functions

#### `safeStringConvert(value: any): string`
Safely converts any value to a string, handling various data types:
- Strings: Returns as-is
- Numbers: Converts to string
- Booleans: Converts to string
- Arrays: Returns first element as string
- Objects: Returns JSON string
- Null/undefined: Returns empty string

#### `getContactPersonLabel(): string`
Returns dynamic label for contact person field based on registry data:
- Uses `brregData.managingDirector.roleDescription` if available
- Falls back to "Daglig leder"

#### `getPhoneLabel(): string`
Returns dynamic label for phone field based on registry data source:
- "Mobilnummer" for mobile numbers or default
- "Telefonnummer" for landline numbers

#### `shouldLockContactPerson(): boolean`
Determines if contact person field should be locked:
- Checks for managing director data in registry
- Checks for role description availability

#### `validateForm(): boolean`
Validates all form fields and returns validation status:
- Sets field-specific error messages
- Returns true if all validations pass

#### `handleSubmit(e: React.FormEvent): Promise<void>`
Handles form submission:
- Prevents default form submission
- Validates form data
- Formats phone number with +47 prefix
- Calls Convex mutation to update company
- Shows success message

### Convex Integration

#### Query: `api.contractorCompany.getContractorCompanyWithDetails`
Fetches current contractor company data including:
- Basic company information
- Brønnøysundregisteret data
- Fetch timestamps

#### Mutation: `api.contractorCompany.updateContractorCompany`
Updates contractor company information with:
- Form data validation
- Phone number formatting
- Optional field handling

### TypeScript Interfaces

#### `CompanyProfileModalProps`
Props interface for the modal component

#### `CompanyFormData`
Form data structure for all editable fields

#### `CompanyInfo`
Brønnøysundregisteret data structure (imported from services)

### Error Handling
- Field-specific validation errors
- Network error handling during submission
- User-friendly error messages in Norwegian
- Success feedback after successful updates

### Performance Considerations
- Data loading only when modal opens
- Efficient re-rendering with proper state management
- Debounced validation (if implemented)
- Optimistic UI updates

## Usage Example

```tsx
import { CompanyProfileModal } from './components/CompanyProfileModal';

function App() {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <button onClick={() => setShowModal(true)}>
        Edit Company Profile
      </button>

      <CompanyProfileModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSuccess={() => {
          console.log('Company profile updated successfully!');
          setShowModal(false);
        }}
      />
    </>
  );
}
```

## Debugging

### Debug Logging
The component includes comprehensive debug logging:

```typescript
console.log('[CompanyProfileModal] Full contractor company data:', {
  contractorCompany,
  brregData: contractorCompany.brregData,
  brregFetchedAt: contractorCompany.brregFetchedAt,
  managingDirector: contractorCompany.brregData?.managingDirector,
  contactPerson: contractorCompany.contactPerson
});

console.log('[CompanyProfileModal] Field locking status:', {
  hasBreregData,
  hasOrgNumber,
  shouldLockContactPerson: shouldLockContactPerson(),
  autoPopulated
});
```

### Common Issues
1. **Fields not locking**: Check `brregFetchedAt` and `brregData` presence
2. **Dynamic labels not working**: Verify `managingDirector.roleDescription` exists
3. **Validation errors**: Check field format requirements
4. **Save failures**: Verify Convex connection and user authentication

---

*Last updated: 2025-01-17*
