# 🔒 JobbLogg Staging Security Setup

Dette dokumentet beskriver hvordan staging-miljøet er sikret mot uautorisert tilgang og søkemotorindeksering.

## 🛡️ Sikkerhetstiltak Implementert

### 1. HTTP Basic Authentication
- **Brukernavn**: `staging`
- **Passord**: `c(mS5jEO7!PT5B>!4'`
- Be<PERSON>tter hele staging-siden
- Implementert både i Traefik og Nginx

### 2. SEO-beskyttelse
- `robots.txt` som blokkerer alle søkemotorer
- `X-Robots-Tag` headers: `noindex, nofollow, noarchive, nosnippet`
- Automatisk `[STAGING]` prefix i sidetittel
- Meta-tags som forhindrer indeksering

### 3. Miljøbasert konfigurasjon
- Separate environment-filer for staging
- Test-keys for alle tredjeparts-tjenester
- Staging-spesifikke Convex deployments

## 🚀 Deployment Alternativer

### Alternativ 1: Traefik (Anbefalt)
```bash
# Deploy med Traefik basic auth
./scripts/deploy-staging-secure.sh

# Eller eksplisitt:
./scripts/deploy-staging-secure.sh --traefik
```

### Alternativ 2: Nginx
```bash
# Deploy med Nginx basic auth
./scripts/deploy-staging-secure.sh --nginx
```

## 📋 GitHub Secrets Påkrevd

Legg til følgende secrets i GitHub repository:

```
STAGING_HOST=your-staging-server-ip
STAGING_PASSWORD=c(mS5jEO7!PT5B>!4'
SSH_PRIVATE_KEY=your-ssh-private-key
```

## 🔧 Lokal Testing

Test basic auth lokalt:
```bash
# Start staging environment
docker compose -f docker-compose.staging.yml up -d

# Test med curl
curl -u "staging:c(mS5jEO7!PT5B>!4'" http://localhost:5174/health
```

## 🌐 Tilgang til Staging

**URL**: https://staging.jobblogg.no
**Brukernavn**: staging
**Passord**: c(mS5jEO7!PT5B>!4'

## 📁 Filer Involvert

### Konfigurasjon
- `docker-compose.staging.yml` - Staging Docker setup
- `docker/nginx/nginx.staging.conf` - Nginx konfigurasjon med basic auth
- `docker/nginx/.htpasswd` - Passord-fil for basic auth
- `.env.staging` - Staging environment variabler

### Scripts
- `scripts/deploy-staging-secure.sh` - Deployment script
- `src/utils/seo.ts` - SEO utilities for staging detection

### Security Headers
- `public/robots.txt` - Blokkerer søkemotorer
- Automatiske meta-tags via JavaScript

## 🔍 Monitoring og Logging

```bash
# Se logs
docker compose -f docker-compose.staging.yml logs -f

# Health check
curl -u "staging:c(mS5jEO7!PT5B>!4'" https://staging.jobblogg.no/health

# Stop services
docker compose -f docker-compose.staging.yml down
```

## ⚠️ Sikkerhetsmerknad

- Passordet er synlig i denne dokumentasjonen for enkelhets skyld
- I produksjon bør passord lagres som secrets/environment variabler
- Vurder å rotere passord regelmessig
- Overvåk tilgangslogger for uautoriserte forsøk

## 🔄 Passord Endring

For å endre staging-passordet:

1. Generer nytt hash:
```bash
openssl passwd -apr1 'nytt-passord'
```

2. Oppdater `docker/nginx/.htpasswd`:
```
staging:$apr1$newHash$...
```

3. Oppdater Traefik labels i `docker-compose.staging.yml`
4. Oppdater GitHub secrets
5. Redeploy staging environment
