# JobbLogg Architecture Review - Revision 3

## 📋 Finale Operasjonelle Detaljer

**Dato**: August 2025  
**Basert på**: REV_2 + operasjonelle avklaringer

---

## 🔄 PR-Previews

**Q: Skal backend være delt dev-instans eller isolert backend per PR?**

**A: Isolert backend per PR**
- **Backend**: Egen Convex deployment per PR (`pr-123:jobblogg-preview`)
- **URL**: `https://pr-123.staging.jobblogg.no`
- **Cleanup**: Automatisk sletting etter PR merge/close
- **Begrensning**: Maksimalt 5 aktive PR-previews samtidig

---

## 🏥 Helseendepunkt

**Q: Nøyaktig URL og forventet svartekst for røyktest?**

**A: Standardiserte health checks**
- **Staging**: `https://staging.jobblogg.no/api/health`
- **Prod**: `https://jobblogg.no/api/health`
- **Forventet svar**: 
  ```json
  {
    "status": "healthy",
    "timestamp": "2025-08-22T10:30:00Z",
    "services": {
      "convex": "connected",
      "clerk": "connected",
      "stripe": "connected"
    }
  }
  ```

---

## 🧪 Røyktest

**Q: Testkontoer, testdata og eksakte steg?**

**A: Automatisert røyktest sekvens**
- **Testkonto**: `<EMAIL>` (passord i GitHub Secrets)
- **Teststeg**:
  1. Innlogging med testkonto
  2. Opprett nytt prosjekt "Røyktest {timestamp}"
  3. Last opp testbilde (1MB PNG)
  4. Send testmelding i chat
  5. Verifiser prosjekt vises i dashboard
- **Testbetaling**: Stripe test card `****************`
- **Cleanup**: Slett testobjekter etter vellykket test

---

## 🚀 Releasevindu og Godkjenning

**Q: Hvem godkjenner og hvilke tidsvinduer?**

**A: Strukturert release prosess**
- **Godkjenner**: djrobbieh (owner)
- **Prod-vindu**: Mandag-torsdag 09:00-15:00 CET
- **Emergency**: 24/7 for kritiske sikkerhetsfiks
- **Godkjenning**: GitHub PR approval + manual "Deploy to Prod" trigger
- **Kommunikasjon**: Slack/email varsling før prod deploy

---

## 🔄 Migreringer

**Q: Policy for destruktive endringer?**

**A: Kun fremoverrettede migreringer**
- **Destruktive endringer**: Forbudt i produksjon
- **Nedgraderinger**: Krever kompenserende migrering
- **Schema endringer**: Bakoverkompatible i minimum 2 releases
- **Prosess**: 
  1. Legg til nye felter (optional)
  2. Deploy kode som bruker nye felter
  3. Fjern gamle felter i neste release

---

## 📊 Staging-Data

**Q: Kilde, seeding frekvens og opprydding?**

**A: Automatisert datamanagement**
- **Kilde**: Anonymiserte prod-data (månedlig eksport)
- **Seeding**: Hver natt kl 02:00 CET
- **Opprydding**: Slett objekter eldre enn 7 dager
- **Testdata**: 
  - 10 anonyme bedrifter
  - 50 testprosjekter
  - 200 chat-meldinger
  - 100 testbilder

---

## 🌐 Cache og CDN

**Q: Purge strategi og cache-tider?**

**A: Optimalisert caching**
- **HTML**: 5 minutter cache, purge ved release
- **JS/CSS**: 1 år cache med hash, automatisk invalidering
- **Bilder**: 30 dager cache
- **API**: Ingen cache
- **Purge ved release**: 
  - HTML filer
  - Service worker
  - Manifest.json

---

## 🛡️ Rate-Limits og Misbruk

**Q: Terskler, håndhevelse og feilmeldinger?**

**A: Lagdelt rate limiting**
- **Per IP**: 100 req/min (generelt), 10 req/min (login)
- **Per bruker**: 1000 req/hour (autentisert)
- **Filopplasting**: 10 filer/min per bruker
- **Håndhevelse**: Caddy + Convex rate limiting
- **Feilkode**: 429 Too Many Requests
- **Melding**: "For mange forespørsler. Prøv igjen om {seconds} sekunder."

---

## 📈 Feilbudsjett og Rollback-Terskler

**Q: Eksakte tall for automatisk rollback?**

**A: Automatiske rollback triggere**
- **Feilrate**: >2% i 5 minutter
- **Responstid**: >3 sekunder median i 5 minutter
- **Health check**: 3 påfølgende feil
- **Rollback tid**: Maksimalt 3 minutter
- **Varsling**: Umiddelbar Slack alert til djrobbieh

---

## 💾 Backup-Praksis

**Q: Klokkeslett, dokumentasjon og ansvar?**

**A: Strukturert backup regime**
- **Klokkeslett**: 03:00 CET daglig
- **Restore-test**: Første mandag hver måned kl 10:00
- **Dokumentasjon**: `BACKUP_RESTORE_LOG.md` i repo
- **Ansvarlig**: djrobbieh
- **Prosess**: 
  1. Automatisk backup til R2
  2. Månedlig restore til staging
  3. Dokumenter resultat og tid

---

## 📝 Logging og Personvern

**Q: Maskerte felter, retensjon og tilgang?**

**A: GDPR-compliant logging**
- **Alltid maskert**: 
  - Email (vis kun domene)
  - Telefonnummer (vis kun +47 xxx xx xxx)
  - Personnummer/org.nr (vis kun første 3 siffer)
  - IP-adresser (vis kun første 3 oktetter)
- **Retensjon**: 90 dager
- **Tilgang**: Kun djrobbieh og system-kontoer
- **Format**: Strukturert JSON med timestamp og trace-ID

---

## 🚩 Feature Toggles

**Q: Hvor toggles lever og første flagg?**

**A: Repo-baserte feature flags**
- **Lokasjon**: `src/config/featureFlags.json`
- **Første flagg**:
  ```json
  {
    "ENABLE_STRIPE_PAYMENTS": true,
    "ENABLE_TEAM_INVITATIONS": true,
    "ENABLE_PROJECT_SHARING": false,
    "ENABLE_PUSH_NOTIFICATIONS": false,
    "ENABLE_ADVANCED_SEARCH": false,
    "ENABLE_BULK_OPERATIONS": false
  }
  ```
- **Override**: Environment variables kan overstyre

---

## 🔐 Secret-Rotasjon

**Q: Frekvens, ansvar og prosedyre?**

**A: Kvartalsvis rotasjon**
- **Frekvens**: Hver 3. måned (mars, juni, september, desember)
- **Ansvarlig**: djrobbieh
- **Prosedyre**:
  1. **Stripe**: Generer nye keys, oppdater webhooks
  2. **Clerk**: Rotér secret keys, test webhooks
  3. **Convex**: Generer nye deploy keys
  4. **Resend**: Rotér API key
  5. **R2**: Rotér access keys
- **Dokumentasjon**: Logg rotasjon i `SECRET_ROTATION_LOG.md`

---

## 🏗️ Infrastruktur som Kode

**Q: Skal server-oppsett sjekkes inn som kode?**

**A: Versjonert infrastruktur**
- **Filer i repo**:
  - `infrastructure/docker-compose.prod.yml`
  - `infrastructure/Caddyfile`
  - `infrastructure/bootstrap.sh`
- **Bootstrap-instrukser**: `HETZNER_SETUP.md`
- **Versjonering**: Tag infrastruktur-endringer
- **Deployment**: Ansible playbook for server-oppsett

---

## 🔒 Sikkerhet på Hetzner

**Q: SSH-nøkler, brannmur og MFA-krav?**

**A: Hardened server security**
- **SSH**: Kun nøkkel-basert, disable passord-login
- **Nøkler**: Separate keys for CI og admin (4096-bit RSA)
- **UFW**: Default deny, kun 22/80/443 åpen
- **MFA**: Påkrevd på GitHub, Hetzner Console, alle tjenester
- **Sudo**: Krever passord, logg alle sudo-kommandoer
- **Fail2ban**: Automatisk blokkering ved mislykkede login

---

## 🎯 DR-Mål

**Q: RTO og RPO i minutter?**

**A: Definerte recovery targets**
- **RTO (Recovery Time Objective)**: 30 minutter
- **RPO (Recovery Point Objective)**: 4 timer (backup frekvens)
- **Kritisk data**: Maksimalt 1 time datatap
- **Akseptabel nedetid**: 2 timer per måned
- **Disaster scenarios**: Server feil, database korrupsjon, DNS problemer

---

## 📊 Observabilitet

**Q: Dashboard navn, lenker og varsling?**

**A: Strukturerte dashboards**
- **Frontend Dashboard**: `https://grafana.jobblogg.no/d/frontend`
- **Backend Dashboard**: `https://grafana.jobblogg.no/d/convex`
- **Infrastructure Dashboard**: `https://grafana.jobblogg.no/d/infrastructure`
- **Varsling**: 
  - Slack: `#jobblogg-alerts`
  - Email: <EMAIL>
  - SMS: Kun kritiske feil (>5% error rate)

---

## 🔗 Webhooks

**Q: Endepunkter, signeringshemmeligheter og feilhåndtering?**

**A: Robust webhook håndtering**
- **Endepunkter**:
  - Staging: `https://staging.jobblogg.no/api/webhooks/{service}`
  - Prod: `https://jobblogg.no/api/webhooks/{service}`
- **Tjenester**: Stripe, Clerk, Resend
- **Signering**: Separate secrets per miljø og tjeneste
- **Feilhåndtering**: 
  - 3 retry attempts med exponential backoff
  - Etter 3 feil: Slack alert + email til djrobbieh
  - Dead letter queue for manuell håndtering
- **Logging**: All webhook aktivitet logges med payload hash

---

## 🎯 Implementeringsrekkefølge

### Uke 1: Sikkerhet og Secrets
1. Rotér alle produksjonsnøkler
2. Implementer GitHub Secrets
3. Sett opp MFA på alle tjenester

### Uke 2: CI/CD Pipeline
1. GitHub Actions workflow
2. Automatisk testing og deployment
3. Staging miljø oppsett

### Uke 3: Observabilitet
1. Grafana dashboards
2. Strukturert logging
3. Alert konfiguration

### Uke 4: Operasjonelle Prosesser
1. Backup og restore testing
2. Feature toggles implementering
3. Webhook feilhåndtering

---

## ✅ Godkjenning og Neste Steg

**Klar for implementering**: Alle operasjonelle detaljer er definert
**Neste steg**: Start med Uke 1 (Sikkerhet og Secrets)
**Review**: Ukentlig status-møte hver fredag kl 14:00

---

*Komplett arkitekturplan klar for produksjonssetting*
