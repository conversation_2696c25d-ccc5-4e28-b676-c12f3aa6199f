import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
// import { api } from "./_generated/api"; // TODO: Re-enable when type instantiation issues are resolved

// Create a subcontractor invitation with enhanced context
export const createSubcontractorInvitation = mutation({
  args: {
    projectId: v.id("projects"),
    subcontractorUserId: v.string(),
    invitedBy: v.string(),
    specialization: v.string(),
    invitationMessage: v.optional(v.string()),
    estimatedDuration: v.optional(v.string()),
    urgency: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    startDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.invitedBy) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the inviting user
    const invitingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.invitedBy))
      .first();

    if (!invitingUser) {
      throw new Error("Inviterende bruker ikke funnet");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check if inviting user has permission to invite to this project
    const inviterAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.invitedBy,
      projectId: args.projectId,
    });

    if (!inviterAccess.hasAccess || (inviterAccess.accessLevel !== "owner" && inviterAccess.accessLevel !== "administrator")) {
      throw new Error("Du har ikke tilgang til å invitere til dette prosjektet");
    }

    // Get the subcontractor user
    const subcontractorUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.subcontractorUserId))
      .first();

    if (!subcontractorUser) {
      throw new Error("Underleverandør ikke funnet");
    }

    // Get customer data for project preview
    const customer = project.customerId ? await ctx.db.get(project.customerId) : null;

    // Get inviting user's company
    const invitingCompany = invitingUser.contractorCompanyId 
      ? await ctx.db.get(invitingUser.contractorCompanyId) 
      : null;

    // Check if invitation already exists
    const existingAssignment = await ctx.db
      .query("projectAssignments")
      .withIndex("by_project_and_user", (q) => q.eq("projectId", args.projectId).eq("assignedUserId", args.subcontractorUserId))
      .first();

    if (existingAssignment && existingAssignment.isActive !== false) {
      throw new Error("Brukeren er allerede tildelt dette prosjektet");
    }

    // Set invitation expiry (7 days from now)
    const now = Date.now();
    const expiresAt = now + (7 * 24 * 60 * 60 * 1000);

    // Get comprehensive inviter information
    let inviterName = '';
    let inviterEmail = '';
    let inviterPhone = '';
    let inviterRole = '';

    // Get inviter's identity for email
    const inviterIdentity = await ctx.auth.getUserIdentity();

    // Try invitation data first (for users who were invited)
    if (invitingUser.invitationFirstName && invitingUser.invitationLastName) {
      inviterName = `${invitingUser.invitationFirstName} ${invitingUser.invitationLastName}`;
    } else if (invitingUser.invitationFirstName) {
      inviterName = invitingUser.invitationFirstName;
    }

    // If no invitation name data, try company contact person (for administrators)
    if (!inviterName && invitingUser.role === "administrator" && invitingCompany?.contactPerson) {
      inviterName = invitingCompany.contactPerson;
    }

    // Final fallback to role-based name
    if (!inviterName) {
      inviterName = invitingUser.role === "administrator" ? "Administrator" : "Teammedlem";
    }

    // Get inviter email
    if (invitingUser.invitationEmail) {
      inviterEmail = invitingUser.invitationEmail;
    } else if (inviterIdentity?.email) {
      inviterEmail = inviterIdentity.email;
    } else if (invitingCompany?.email) {
      inviterEmail = invitingCompany.email;
    }

    // Get inviter phone
    if (invitingUser.invitationPhone) {
      inviterPhone = invitingUser.invitationPhone;
    } else if (invitingCompany?.phone) {
      inviterPhone = invitingCompany.phone;
    }

    // Get inviter role/title
    if (invitingUser.role === "administrator") {
      inviterRole = "Daglig leder";
    } else {
      inviterRole = "Prosjektleder";
    }

    // Create enhanced project preview cache
    const projectPreview = {
      name: project.name,
      description: project.description || '',
      address: customer?.streetAddress || customer?.address || 'Ikke oppgitt',
      customerName: customer?.name || 'Ikke oppgitt',
      mainContractorCompany: invitingCompany?.name || 'Ikke oppgitt',
      inviterName,
      inviterEmail,
      inviterPhone,
      inviterRole,
    };

    let assignmentId;

    if (existingAssignment) {
      // Reactivate existing assignment as invitation
      await ctx.db.patch(existingAssignment._id, {
        accessLevel: "subcontractor",
        assignedBy: args.invitedBy,
        assignedAt: now,
        isActive: false, // Keep inactive until accepted
        isSubcontractor: true,
        subcontractorSpecialization: args.specialization,
        
        // Invitation fields
        invitationStatus: "pending",
        invitedAt: now,
        expiresAt,
        invitationMessage: args.invitationMessage,
        estimatedDuration: args.estimatedDuration,
        urgency: args.urgency,
        startDate: args.startDate,
        projectPreview,
        
        // Clear previous response data
        respondedAt: undefined,
        responseMessage: undefined,
        revokedAt: undefined,
        revokedBy: undefined,
      });

      assignmentId = existingAssignment._id;
    } else {
      // Create new invitation assignment
      assignmentId = await ctx.db.insert("projectAssignments", {
        projectId: args.projectId,
        assignedUserId: args.subcontractorUserId,
        assignedBy: args.invitedBy,
        assignedAt: now,
        accessLevel: "subcontractor",
        isActive: false, // Keep inactive until accepted
        assignedCompanyId: subcontractorUser.contractorCompanyId,
        isSubcontractor: true,
        subcontractorSpecialization: args.specialization,
        
        // Invitation fields
        invitationStatus: "pending",
        invitedAt: now,
        expiresAt,
        invitationMessage: args.invitationMessage,
        estimatedDuration: args.estimatedDuration,
        urgency: args.urgency,
        startDate: args.startDate,
        projectPreview,
      });
    }

    // Get subcontractor's email address
    let subcontractorEmail = '';

    // First try to get email from user's invitation data
    if (subcontractorUser.invitationEmail) {
      subcontractorEmail = subcontractorUser.invitationEmail;
    } else {
      // If no invitation email, we need to get it from Clerk
      // For now, we'll require that subcontractors have an invitationEmail
      // This should be set when they register or are invited to the platform
      console.warn('Subcontractor user has no invitationEmail:', args.subcontractorUserId);

      // Try to get email from company data if available
      if (subcontractorUser.contractorCompanyId) {
        const subcontractorCompany = await ctx.db.get(subcontractorUser.contractorCompanyId);
        if (subcontractorCompany?.email) {
          subcontractorEmail = subcontractorCompany.email;
        }
      }
    }

    // Only send email if we have a valid email address
    if (subcontractorEmail) {
      // Create personalized greeting name
      let personalizedName = '';

      // First try to use the person's name (preferred)
      const fullName = `${subcontractorUser.invitationFirstName || ''} ${subcontractorUser.invitationLastName || ''}`.trim();
      if (fullName) {
        personalizedName = fullName;
      } else {
        // Fallback to company information
        if (subcontractorUser.contractorCompanyId) {
          const subcontractorCompany = await ctx.db.get(subcontractorUser.contractorCompanyId);
          if (subcontractorCompany) {
            // Try company contact person first, then company name
            if (subcontractorCompany.contactPerson) {
              personalizedName = subcontractorCompany.contactPerson;
            } else {
              personalizedName = subcontractorCompany.name;
            }
          }
        }
      }

      // Final fallback if no name found
      if (!personalizedName) {
        personalizedName = 'Underleverandør';
      }

      // Schedule invitation email to be sent
      try {
        // TODO: Re-enable invitation email when type instantiation issue is resolved
        // await ctx.scheduler.runAfter(0, api.emails.sendInvitationEmail, {
        //   invitationId: assignmentId,
        //   subcontractorEmail,
        //   subcontractorName: personalizedName,
        //   projectName: project.name,
        //   projectAddress: projectPreview.address,
        //   mainContractorName: projectPreview.mainContractorCompany,
        //   inviterName: projectPreview.inviterName,
        //   inviterEmail: projectPreview.inviterEmail,
        //   inviterPhone: projectPreview.inviterPhone,
        //   inviterRole: projectPreview.inviterRole,
        //   specialization: args.specialization,
        //   projectDescription: project.description || '',
        //   estimatedDuration: args.estimatedDuration || 'Ikke oppgitt',
        //   invitationMessage: args.invitationMessage,
        //   urgency: args.urgency,
        //   expiresAt,
        // });
        console.log("⚠️ Invitation email temporarily disabled due to type issues");
        console.log('✅ Invitation email scheduled for:', subcontractorEmail);
      } catch (emailError) {
        console.error('Failed to send invitation email:', emailError);
        // Don't fail the invitation creation if email fails
      }
    } else {
      console.error('❌ No email address found for subcontractor:', args.subcontractorUserId);
      console.error('Subcontractor user data:', {
        clerkUserId: subcontractorUser.clerkUserId,
        invitationEmail: subcontractorUser.invitationEmail,
        contractorCompanyId: subcontractorUser.contractorCompanyId,
      });
    }

    return {
      success: true,
      invitationId: assignmentId,
      message: `Invitasjon sendt til ${`${subcontractorUser.invitationFirstName || ''} ${subcontractorUser.invitationLastName || ''}`.trim() || 'underleverandør'}`,
      expiresAt,
    };
  },
});

// Respond to a subcontractor invitation
export const respondToInvitation = mutation({
  args: {
    invitationId: v.id("projectAssignments"),
    response: v.union(v.literal("accept"), v.literal("decline")),
    userId: v.string(),
    responseMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the invitation
    const invitation = await ctx.db.get(args.invitationId);
    if (!invitation) {
      throw new Error("Invitasjon ikke funnet");
    }

    // Get the responding user to check permissions
    const respondingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!respondingUser) {
      throw new Error("Bruker ikke funnet");
    }

    // Get the invited user to check company relationship
    const invitedUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", invitation.assignedUserId))
      .first();

    if (!invitedUser) {
      throw new Error("Invitert bruker ikke funnet");
    }

    // Allow invitation response if:
    // 1. User is the directly invited user (daglig leder), OR
    // 2. User is an administrator in the same company as the invited user
    const canRespond =
      invitation.assignedUserId === args.userId || // Direct invitee
      (respondingUser.role === "administrator" &&
       respondingUser.contractorCompanyId === invitedUser.contractorCompanyId); // Administrator in same company

    if (!canRespond) {
      throw new Error("Du har ikke tilgang til å behandle denne invitasjonen");
    }

    // Check if invitation is still pending
    if (invitation.invitationStatus !== "pending") {
      throw new Error("Denne invitasjonen er allerede besvart eller utløpt");
    }

    // Check if invitation has expired
    if (invitation.expiresAt && Date.now() > invitation.expiresAt) {
      // Mark as expired
      await ctx.db.patch(args.invitationId, {
        invitationStatus: "expired",
      });
      throw new Error("Denne invitasjonen har utløpt");
    }

    const now = Date.now();
    const newStatus = args.response === "accept" ? "accepted" : "declined";

    // Update invitation with response
    await ctx.db.patch(args.invitationId, {
      invitationStatus: newStatus,
      respondedAt: now,
      responseMessage: args.responseMessage,
      isActive: args.response === "accept", // Activate assignment if accepted
    });

    // Schedule confirmation email to be sent
    try {
      // TODO: Re-enable response confirmation email when type instantiation issue is resolved
      // await ctx.scheduler.runAfter(0, api.emails.sendResponseConfirmationEmail, {
      //   invitationId: args.invitationId,
      //   response: args.response,
      //   responseMessage: args.responseMessage,
      // });
      console.log("⚠️ Response confirmation email temporarily disabled due to type issues");
    } catch (emailError) {
      console.error('Failed to send confirmation email:', emailError);
      // Don't fail the response if email fails
    }

    // Notify the inviter about the response (both accept and decline)
    try {
      // Get the inviting user to send notification
      const invitingUser = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", invitation.assignedBy))
        .first();

      if (invitingUser) {
        // Build subcontractor name for notification
        const subcontractorName = `${respondingUser.invitationFirstName || ''} ${respondingUser.invitationLastName || ''}`.trim() || 'Underleverandør';

        // Create notification based on response type
        const notificationData = {
          userId: invitation.assignedBy,
          data: {
            invitationId: args.invitationId,
            projectId: invitation.projectId,
            projectName: invitation.projectPreview?.name,
            subcontractorName,
            responseMessage: args.responseMessage,
            responseType: args.response,
          },
          isRead: false,
          createdAt: now,
        };

        if (args.response === "accept") {
          // Acceptance notification
          await ctx.db.insert("notifications", {
            ...notificationData,
            type: "invitation_accepted",
            title: "Invitasjon godtatt! 🎉",
            message: `${subcontractorName} har godtatt invitasjonen til prosjektet "${invitation.projectPreview?.name || 'Prosjekt'}"`,
          });
          console.log(`✅ Acceptance notification created for user ${invitation.assignedBy}`);
        } else {
          // Decline notification
          await ctx.db.insert("notifications", {
            ...notificationData,
            type: "invitation_declined",
            title: "Invitasjon avslått",
            message: `${subcontractorName} har avslått invitasjonen til prosjektet "${invitation.projectPreview?.name || 'Prosjekt'}"`,
          });
          console.log(`❌ Decline notification created for user ${invitation.assignedBy}`);
        }
      }
    } catch (notificationError) {
      console.error('Failed to create invitation response notification:', notificationError);
      // Don't fail the response if notification creation fails
    }

    return {
      success: true,
      message: args.response === "accept" 
        ? "Invitasjon godtatt! Prosjektet er nå tilgjengelig i din oversikt."
        : "Invitasjon avslått.",
      status: newStatus,
    };
  },
});

// Get ONLY incoming subcontractor invitations for notifications (invitations received by the user)
export const getIncomingSubcontractorInvitations = query({
  args: {
    userId: v.string(),
    status: v.optional(v.union(v.literal("pending"), v.literal("all"))),
  },
  handler: async (ctx, args) => {
    const statusFilter = args.status || "pending";

    // Get the requesting user to check their company
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return [];
    }

    let incomingInvitations = [];

    if (statusFilter === "all") {
      // Get all incoming invitations for user or their company (if administrator)
      if (requestingUser.role === "administrator" && requestingUser.contractorCompanyId) {
        // Get all incoming invitations for users in the same company
        const companyUsers = await ctx.db
          .query("users")
          .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
          .collect();

        const companyUserIds = companyUsers.map(user => user.clerkUserId);

        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.neq(q.field("invitationStatus"), undefined),
            q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
          ))
          .order("desc")
          .collect();
      } else {
        // Get only direct incoming invitations for this user
        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .withIndex("by_assigned_user", (q) => q.eq("assignedUserId", args.userId))
          .filter((q) => q.neq(q.field("invitationStatus"), undefined))
          .order("desc")
          .collect();
      }
    } else {
      // Get only pending incoming invitations
      if (requestingUser.role === "administrator" && requestingUser.contractorCompanyId) {
        // Get pending incoming invitations for users in the same company
        const companyUsers = await ctx.db
          .query("users")
          .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
          .collect();

        const companyUserIds = companyUsers.map(user => user.clerkUserId);

        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.eq(q.field("invitationStatus"), "pending"),
            q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
          ))
          .order("desc")
          .collect();
      } else {
        // Get only direct pending incoming invitations for this user
        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .withIndex("by_user_and_invitation_status", (q) => q.eq("assignedUserId", args.userId).eq("invitationStatus", "pending"))
          .order("desc")
          .collect();
      }
    }

    // Enrich invitations with project and inviter data
    const enrichedInvitations = await Promise.all(
      incomingInvitations.map(async (invitation) => {
        const project = await ctx.db.get(invitation.projectId);
        const inviter = await ctx.db
          .query("users")
          .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", invitation.assignedBy))
          .first();

        return {
          ...invitation,
          project,
          inviter: inviter ? {
            name: inviter.invitationFirstName && inviter.invitationLastName
              ? `${inviter.invitationFirstName} ${inviter.invitationLastName}`
              : inviter.invitationFirstName || 'Inviter',
            email: inviter.invitationEmail,
            phone: inviter.invitationPhone,
            role: inviter.role,
          } : null,
          // Mark as incoming invitation
          direction: 'incoming' as const,
        };
      })
    );

    return enrichedInvitations;
  },
});

// Get subcontractor invitations for a user (both incoming and outgoing)
export const getSubcontractorInvitations = query({
  args: {
    userId: v.string(),
    status: v.optional(v.union(v.literal("pending"), v.literal("all"))),
  },
  handler: async (ctx, args) => {
    const statusFilter = args.status || "pending";

    // Get the requesting user to check their company
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return [];
    }

    let incomingInvitations = [];
    let outgoingInvitations = [];

    if (statusFilter === "all") {
      // Get all incoming invitations for user or their company (if administrator)
      if (requestingUser.role === "administrator" && requestingUser.contractorCompanyId) {
        // Get all incoming invitations for users in the same company
        const companyUsers = await ctx.db
          .query("users")
          .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
          .collect();

        const companyUserIds = companyUsers.map(user => user.clerkUserId);

        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.neq(q.field("invitationStatus"), undefined),
            q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
          ))
          .order("desc")
          .collect();

        // Get all outgoing invitations sent by users in the same company
        outgoingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.neq(q.field("invitationStatus"), undefined),
            q.or(...companyUserIds.map(userId => q.eq(q.field("assignedBy"), userId)))
          ))
          .order("desc")
          .collect();
      } else {
        // Get only direct incoming invitations for this user
        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .withIndex("by_assigned_user", (q) => q.eq("assignedUserId", args.userId))
          .filter((q) => q.neq(q.field("invitationStatus"), undefined))
          .order("desc")
          .collect();

        // Get only direct outgoing invitations sent by this user
        outgoingInvitations = await ctx.db
          .query("projectAssignments")
          .withIndex("by_assigned_by", (q) => q.eq("assignedBy", args.userId))
          .filter((q) => q.neq(q.field("invitationStatus"), undefined))
          .order("desc")
          .collect();
      }
    } else {
      // Get only pending invitations
      if (requestingUser.role === "administrator" && requestingUser.contractorCompanyId) {
        // Get pending incoming invitations for users in the same company
        const companyUsers = await ctx.db
          .query("users")
          .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
          .collect();

        const companyUserIds = companyUsers.map(user => user.clerkUserId);

        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.eq(q.field("invitationStatus"), "pending"),
            q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
          ))
          .order("desc")
          .collect();

        // Get pending outgoing invitations sent by users in the same company
        outgoingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.eq(q.field("invitationStatus"), "pending"),
            q.or(...companyUserIds.map(userId => q.eq(q.field("assignedBy"), userId)))
          ))
          .order("desc")
          .collect();
      } else {
        // Get only direct pending incoming invitations for this user
        incomingInvitations = await ctx.db
          .query("projectAssignments")
          .withIndex("by_user_and_invitation_status", (q) => q.eq("assignedUserId", args.userId).eq("invitationStatus", "pending"))
          .order("desc")
          .collect();

        // Get only direct pending outgoing invitations sent by this user
        outgoingInvitations = await ctx.db
          .query("projectAssignments")
          .filter((q) => q.and(
            q.eq(q.field("invitationStatus"), "pending"),
            q.eq(q.field("assignedBy"), args.userId)
          ))
          .order("desc")
          .collect();
      }
    }

    // Combine and deduplicate invitations (in case user is both sender and receiver)
    const allInvitations = [...incomingInvitations, ...outgoingInvitations];
    const uniqueInvitations = allInvitations.filter((invitation, index, self) =>
      index === self.findIndex(inv => inv._id === invitation._id)
    );

    // Enrich invitations with project, inviter, and subcontractor data, plus direction indicator
    const enrichedInvitations = await Promise.all(
      uniqueInvitations.map(async (invitation) => {
        const project = await ctx.db.get(invitation.projectId);
        const inviter = await ctx.db
          .query("users")
          .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", invitation.assignedBy))
          .first();

        // Determine if this is an incoming or outgoing invitation - TODO: Use for invitation direction logic
        // const _isIncoming = invitation.assignedUserId === args.userId ||
        //   (requestingUser.role === "administrator" && requestingUser.contractorCompanyId &&
        //    incomingInvitations.some(inv => inv._id === invitation._id));

        const isOutgoing = invitation.assignedBy === args.userId ||
          (requestingUser.role === "administrator" && requestingUser.contractorCompanyId &&
           outgoingInvitations.some(inv => inv._id === invitation._id));

        // For outgoing invitations, get subcontractor information
        let subcontractorInfo = null;
        if (isOutgoing) {
          // Get the subcontractor user
          const subcontractorUser = await ctx.db
            .query("users")
            .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", invitation.assignedUserId))
            .first();

          if (subcontractorUser) {
            // Get subcontractor company information
            const subcontractorCompany = subcontractorUser.contractorCompanyId
              ? await ctx.db.get(subcontractorUser.contractorCompanyId)
              : null;

            // Build subcontractor contact person name
            let contactPersonName = '';
            if (subcontractorUser.invitationFirstName && subcontractorUser.invitationLastName) {
              contactPersonName = `${subcontractorUser.invitationFirstName} ${subcontractorUser.invitationLastName}`;
            } else if (subcontractorUser.invitationFirstName) {
              contactPersonName = subcontractorUser.invitationFirstName;
            } else if (subcontractorUser.role === "administrator" && subcontractorCompany?.contactPerson) {
              contactPersonName = subcontractorCompany.contactPerson;
            }

            subcontractorInfo = {
              userId: subcontractorUser.clerkUserId,
              contactPersonName: contactPersonName || 'Ukjent kontakt',
              company: subcontractorCompany,
              email: subcontractorUser.invitationEmail || subcontractorCompany?.email,
              phone: subcontractorUser.invitationPhone || subcontractorCompany?.phone,
              role: subcontractorUser.role,
            };
          }
        }

        // Update inviter name in projectPreview if it's generic
        let updatedInvitation = { ...invitation };
        if (invitation.projectPreview && inviter &&
            (invitation.projectPreview.inviterName === 'Administrator' ||
             invitation.projectPreview.inviterName === 'Teammedlem')) {

          // Get inviter name with comprehensive fallback logic
          let inviterName = '';

          // Try invitation data first
          if (inviter.invitationFirstName && inviter.invitationLastName) {
            inviterName = `${inviter.invitationFirstName} ${inviter.invitationLastName}`;
          } else if (inviter.invitationFirstName) {
            inviterName = inviter.invitationFirstName;
          }

          // If no invitation name data, try company contact person (for administrators)
          if (!inviterName && inviter.role === "administrator" && inviter.contractorCompanyId) {
            const inviterCompany = await ctx.db.get(inviter.contractorCompanyId);
            if (inviterCompany?.contactPerson) {
              inviterName = inviterCompany.contactPerson;
            }
          }

          // Final fallback to role-based name
          if (!inviterName) {
            inviterName = inviter.role === "administrator" ? "Administrator" : "Teammedlem";
          }

          // Update the projectPreview with the correct inviter name
          updatedInvitation = {
            ...invitation,
            projectPreview: {
              ...invitation.projectPreview,
              inviterName,
            }
          };
        }

        return {
          ...updatedInvitation,
          project,
          inviter,
          subcontractorInfo, // Add subcontractor information for outgoing invitations
          invitationDirection: isOutgoing ? "outgoing" : "incoming", // Add direction indicator
        };
      })
    );

    return enrichedInvitations;
  },
});

// Get invitation statistics for notifications (only incoming invitations)
export const getInvitationNotificationStats = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the requesting user to check their company
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return {
        pendingCount: 0,
        acceptedCount: 0,
        totalCount: 0,
      };
    }

    let pendingIncoming = [], acceptedIncoming = [], declinedIncoming = [];

    if (requestingUser.role === "administrator" && requestingUser.contractorCompanyId) {
      // Get invitations for all users in the same company
      const companyUsers = await ctx.db
        .query("users")
        .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
        .collect();

      const companyUserIds = companyUsers.map(user => user.clerkUserId);

      // Only incoming invitations (received by company) - no outgoing invitations
      pendingIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "pending"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
        ))
        .collect();

      acceptedIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "accepted"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
        ))
        .collect();

      declinedIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "declined"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
        ))
        .collect();
    } else {
      // Get only direct incoming invitations for this user - no outgoing invitations
      pendingIncoming = await ctx.db
        .query("projectAssignments")
        .withIndex("by_user_and_invitation_status", (q) => q.eq("assignedUserId", args.userId).eq("invitationStatus", "pending"))
        .collect();

      acceptedIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "accepted"),
          q.eq(q.field("assignedUserId"), args.userId)
        ))
        .collect();

      declinedIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "declined"),
          q.eq(q.field("assignedUserId"), args.userId)
        ))
        .collect();
    }

    return {
      pendingCount: pendingIncoming.length,
      acceptedCount: acceptedIncoming.length,
      declinedCount: declinedIncoming.length,
      totalCount: pendingIncoming.length + acceptedIncoming.length + declinedIncoming.length,
    };
  },
});

// Get invitation statistics for dashboard (both incoming and outgoing)
export const getInvitationStats = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the requesting user to check their company
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return {
        pendingCount: 0,
        acceptedCount: 0,
        totalCount: 0,
      };
    }

    let pendingIncoming = [], pendingOutgoing = [], acceptedIncoming = [], acceptedOutgoing = [], declinedIncoming = [], declinedOutgoing = [];

    if (requestingUser.role === "administrator" && requestingUser.contractorCompanyId) {
      // Get invitations for all users in the same company
      const companyUsers = await ctx.db
        .query("users")
        .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
        .collect();

      const companyUserIds = companyUsers.map(user => user.clerkUserId);

      // Incoming invitations (received by company)
      pendingIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "pending"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
        ))
        .collect();

      acceptedIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "accepted"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
        ))
        .collect();

      declinedIncoming = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "declined"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedUserId"), userId)))
        ))
        .collect();

      // Outgoing invitations (sent by company)
      pendingOutgoing = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "pending"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedBy"), userId)))
        ))
        .collect();

      acceptedOutgoing = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "accepted"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedBy"), userId)))
        ))
        .collect();

      declinedOutgoing = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "declined"),
          q.or(...companyUserIds.map(userId => q.eq(q.field("assignedBy"), userId)))
        ))
        .collect();
    } else {
      // Get only direct invitations for this user
      pendingIncoming = await ctx.db
        .query("projectAssignments")
        .withIndex("by_user_and_invitation_status", (q) => q.eq("assignedUserId", args.userId).eq("invitationStatus", "pending"))
        .collect();

      acceptedIncoming = await ctx.db
        .query("projectAssignments")
        .withIndex("by_user_and_invitation_status", (q) => q.eq("assignedUserId", args.userId).eq("invitationStatus", "accepted"))
        .collect();

      declinedIncoming = await ctx.db
        .query("projectAssignments")
        .withIndex("by_user_and_invitation_status", (q) => q.eq("assignedUserId", args.userId).eq("invitationStatus", "declined"))
        .collect();

      // Get outgoing invitations sent by this user
      pendingOutgoing = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "pending"),
          q.eq(q.field("assignedBy"), args.userId)
        ))
        .collect();

      acceptedOutgoing = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "accepted"),
          q.eq(q.field("assignedBy"), args.userId)
        ))
        .collect();

      declinedOutgoing = await ctx.db
        .query("projectAssignments")
        .filter((q) => q.and(
          q.eq(q.field("invitationStatus"), "declined"),
          q.eq(q.field("assignedBy"), args.userId)
        ))
        .collect();
    }

    // Combine and deduplicate
    const allPending = [...pendingIncoming, ...pendingOutgoing];
    const allAccepted = [...acceptedIncoming, ...acceptedOutgoing];
    const allDeclined = [...declinedIncoming, ...declinedOutgoing];

    const uniquePending = allPending.filter((invitation, index, self) =>
      index === self.findIndex(inv => inv._id === invitation._id)
    );

    const uniqueAccepted = allAccepted.filter((invitation, index, self) =>
      index === self.findIndex(inv => inv._id === invitation._id)
    );

    const uniqueDeclined = allDeclined.filter((invitation, index, self) =>
      index === self.findIndex(inv => inv._id === invitation._id)
    );

    return {
      pendingCount: uniquePending.length,
      acceptedCount: uniqueAccepted.length,
      declinedCount: uniqueDeclined.length,
      totalCount: uniquePending.length + uniqueAccepted.length + uniqueDeclined.length,
      // Additional breakdown for debugging/future use
      incomingPendingCount: pendingIncoming.length,
      outgoingPendingCount: pendingOutgoing.length,
      incomingAcceptedCount: acceptedIncoming.length,
      outgoingAcceptedCount: acceptedOutgoing.length,
      incomingDeclinedCount: declinedIncoming.length,
      outgoingDeclinedCount: declinedOutgoing.length,
    };
  },
});

// Get invitation response notifications for contractors (when subcontractors respond to their invitations)
export const getInvitationResponseNotifications = query({
  args: {
    userId: v.string(),
    status: v.optional(v.union(v.literal("unread"), v.literal("all"))),
  },
  handler: async (ctx, args) => {
    const statusFilter = args.status || "unread";

    // Get the requesting user
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return [];
    }

    // Build query filter for response notifications
    let notificationQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.or(
        q.eq(q.field("type"), "invitation_accepted"),
        q.eq(q.field("type"), "invitation_declined")
      ));

    // Apply read status filter
    if (statusFilter === "unread") {
      notificationQuery = notificationQuery.filter((q) => q.eq(q.field("isRead"), false));
    }

    const notifications = await notificationQuery
      .order("desc")
      .collect();

    // Take only the most recent 50 notifications
    const recentNotifications = notifications.slice(0, 50);

    // Enrich notifications with additional data
    const enrichedNotifications = await Promise.all(
      recentNotifications.map(async (notification) => {
        let projectInfo = null;
        let invitationInfo = null;

        // Get project information if available
        if (notification.data?.projectId) {
          projectInfo = await ctx.db.get(notification.data.projectId);
        }

        // Get invitation information if available
        if (notification.data?.invitationId) {
          invitationInfo = await ctx.db.get(notification.data.invitationId);
        }

        return {
          ...notification,
          projectInfo,
          invitationInfo,
          // Add helper properties for UI
          isAcceptance: notification.type === "invitation_accepted",
          isDecline: notification.type === "invitation_declined",
          timeAgo: Date.now() - notification.createdAt,
        };
      })
    );

    return enrichedNotifications;
  },
});

// Mark invitation response notifications as read
export const markResponseNotificationsAsRead = mutation({
  args: {
    userId: v.string(),
    notificationIds: v.optional(v.array(v.id("notifications"))),
  },
  handler: async (ctx, args) => {
    // Verify user identity
    const identity = await ctx.auth.getUserIdentity();
    if (!identity || identity.subject !== args.userId) {
      throw new Error("Unauthorized");
    }

    const now = Date.now();

    if (args.notificationIds && args.notificationIds.length > 0) {
      // Mark specific notifications as read
      await Promise.all(
        args.notificationIds.map(async (notificationId) => {
          const notification = await ctx.db.get(notificationId);
          if (notification && notification.userId === args.userId && !notification.isRead) {
            await ctx.db.patch(notificationId, {
              isRead: true,
              readAt: now,
            });
          }
        })
      );
    } else {
      // Mark all unread response notifications as read
      const unreadNotifications = await ctx.db
        .query("notifications")
        .withIndex("by_user_and_read", (q) => q.eq("userId", args.userId).eq("isRead", false))
        .filter((q) => q.or(
          q.eq(q.field("type"), "invitation_accepted"),
          q.eq(q.field("type"), "invitation_declined")
        ))
        .collect();

      await Promise.all(
        unreadNotifications.map(async (notification) => {
          await ctx.db.patch(notification._id, {
            isRead: true,
            readAt: now,
          });
        })
      );
    }

    return { success: true, markedCount: args.notificationIds?.length || 0 };
  },
});
