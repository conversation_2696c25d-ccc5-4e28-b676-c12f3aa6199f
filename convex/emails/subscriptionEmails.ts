import { v } from "convex/values";
import { internalMutation } from "../_generated/server";

// Send subscription-related email notifications
export const sendSubscriptionEmail = internalMutation({
  args: {
    userId: v.string(),
    type: v.union(
      v.literal("trial_reminder_day_3"),
      v.literal("trial_reminder_day_5"),
      v.literal("trial_reminder_24h"),
      v.literal("trial_expired"),
      v.literal("payment_failed"),
      v.literal("subscription_activated"),
      v.literal("subscription_canceled")
    ),
    userEmail: v.string(),
    userName: v.string(),
    metadata: v.optional(v.object({
      daysLeft: v.optional(v.number()),
      planName: v.optional(v.string()),
      amount: v.optional(v.string()),
    })),
  },
  handler: async (_ctx, args) => {
    const emailTemplate = getEmailTemplate(args.type, args.userName, args.metadata);
    
    if (!emailTemplate) {
      console.error("No email template found for type:", args.type);
      return { success: false, error: "No template found" };
    }

    try {
      // TODO: Integrate with actual email service (Resend, SendGrid, etc.)
      console.log(`📧 Sending ${args.type} email to ${args.userEmail}`);
      console.log("Subject:", emailTemplate.subject);
      console.log("Message:", emailTemplate.message);

      // For now, just log the email content
      // In production, you would send the actual email here
      
      return { success: true };
    } catch (error) {
      console.error("Failed to send subscription email:", error);
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
    }
  },
});

// Get email template based on type
function getEmailTemplate(
  type: string, 
  userName: string, 
  metadata?: { daysLeft?: number; planName?: string; amount?: string }
) {
  const baseUrl = process.env.CONVEX_SITE_URL || "https://jobblogg.no";
  const dashboardUrl = `${baseUrl}/dashboard`;
  const upgradeUrl = `${baseUrl}/upgrade`;

  switch (type) {
    case "trial_reminder_day_3":
      return {
        subject: "⏰ 4 dager igjen av gratis prøveperioden - JobbLogg",
        message: `Hei ${userName},

Du har 4 dager igjen av din gratis prøveperiode på JobbLogg.

Så langt har du fått teste alle våre funksjoner uten kostnad. For å fortsette å bruke JobbLogg etter prøveperioden, må du velge et abonnement.

Våre planer:
• Liten bedrift (1-9 ansatte): 299 kr/mnd
• Mellomstor bedrift (10-49 ansatte): 999 kr/mnd  
• Stor bedrift (50-249 ansatte): 2999 kr/mnd

Velg abonnement: ${upgradeUrl}

Har du spørsmål? Svar på denne e-posten så hjelper vi deg.

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "trial_reminder_day_5":
      return {
        subject: "⚠️ 2 dager igjen av gratis prøveperioden - JobbLogg",
        message: `Hei ${userName},

Du har kun 2 dager igjen av din gratis prøveperiode på JobbLogg.

For å unngå avbrudd i tjenesten, oppgrader til et betalt abonnement i dag.

Våre planer starter fra kun 299 kr/mnd for små bedrifter.

Oppgrader nå: ${upgradeUrl}

Trenger du hjelp med å velge riktig plan? Svar på denne e-posten så hjelper vi deg.

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "trial_reminder_24h":
      return {
        subject: "🚨 24 timer igjen av gratis prøveperioden - JobbLogg",
        message: `Hei ${userName},

Din gratis prøveperiode på JobbLogg utløper i morgen!

For å fortsette å bruke alle funksjoner, må du oppgradere til et betalt abonnement i dag.

Oppgrader umiddelbart: ${upgradeUrl}

Etter prøveperioden får du 3 dager med begrenset tilgang (kun lesing) før kontoen suspenderes.

Har du spørsmål? Ring oss på 123 45 678 eller svar på denne e-posten.

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "trial_expired":
      return {
        subject: "Prøveperioden er utløpt - Oppgrader JobbLogg nå",
        message: `Hei ${userName},

Din gratis prøveperiode på JobbLogg er nå utløpt.

Du har 3 dager med begrenset tilgang (kun lesing av eksisterende prosjekter) før kontoen suspenderes.

Oppgrader nå for å fortsette: ${upgradeUrl}

Alle dine prosjekter og data er trygt lagret og vil være tilgjengelig umiddelbart etter oppgradering.

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "payment_failed":
      return {
        subject: "Betalingsfeil - Oppdater betalingsmetode - JobbLogg",
        message: `Hei ${userName},

Vi kunne ikke belaste din betalingsmetode for JobbLogg-abonnementet.

Oppdater betalingsmetoden din for å unngå avbrudd i tjenesten:
${dashboardUrl}

Hvis problemet ikke løses innen 3 dager, vil kontoen din bli suspendert.

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet`
      };

    case "subscription_activated":
      return {
        subject: "Velkommen til JobbLogg! Abonnementet ditt er aktivt",
        message: `Hei ${userName},

Takk for at du valgte JobbLogg! Ditt ${metadata?.planName || ''} abonnement er nå aktivt.

Du har nå full tilgang til alle funksjoner:
• Ubegrenset prosjekter
• Team samarbeid
• Kundeportaler
• Alle rapporter og eksporter

Kom i gang: ${dashboardUrl}

Trenger du hjelp med å komme i gang? Se vår guide eller kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet`
      };

    case "subscription_canceled":
      return {
        subject: "Abonnement kansellert - JobbLogg",
        message: `Hei ${userName},

Ditt JobbLogg-abonnement er nå kansellert.

Du har tilgang til kontoen din til ${metadata?.planName || 'slutten av faktureringsperioden'}.

Alle dine data vil bli bevart i 90 dager etter kansellering, så du kan reaktivere abonnementet når som helst.

Reaktiver abonnement: ${dashboardUrl}

Vi savner deg allerede! Hvis det er noe vi kan gjøre bedre, hør gjerne fra deg på <EMAIL>

Mvh,
JobbLogg teamet`
      };

    default:
      return null;
  }
}
