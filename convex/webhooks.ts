import { httpAction, internalQuery, internalMutation } from './_generated/server';
// import { internal } from './_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { v } from "convex/values";

// Webhook handler for Resend email delivery status updates
export const resendWebhook = httpAction(async (_ctx, request) => {
  try {
    console.log('📧 Received Resend webhook');
    
    // Verify the request is from Resend (in production, verify webhook signature)
    const body = await request.json();
    console.log('Webhook payload:', body);

    // Extract event data
    const { type, data } = body;
    
    if (!data || !data.email_id) {
      console.error('❌ Invalid webhook payload: missing email_id');
      return new Response('Invalid payload', { status: 400 });
    }

    const emailId = data.email_id;
    console.log(`📧 Processing webhook: ${type} for emailId: ${emailId}`);

    let status: string;
    // Variables temporarily removed due to type instantiation issues
    // let errorMessage: string | undefined;
    // let bounceReason: string | undefined;

    // Map Resend event types to our status values
    switch (type) {
      case 'email.sent':
        status = 'sent';
        break;
      case 'email.delivered':
        status = 'delivered';
        break;
      case 'email.bounced':
        status = 'bounced';
        // bounceReason = data.bounce_reason || 'Unknown bounce reason'; // Temporarily disabled
        break;
      case 'email.complained':
        status = 'complained';
        break;
      case 'email.opened':
        // Email open tracking removed - unreliable and replaced with project access tracking
        console.log(`👁️ Email opened event received (ignored): ${emailId}`);
        return new Response('OK', { status: 200 });
      case 'email.clicked':
        // Email click tracking removed - replaced with project access tracking
        console.log(`🔗 Email clicked event received (ignored): ${emailId}`);
        return new Response('OK', { status: 200 });
      case 'email.delivery_delayed':
        // Don't update status for delayed delivery, just log it
        console.log(`📧 Email delivery delayed for ${emailId}`);
        return new Response('OK', { status: 200 });
      default:
        console.log(`📧 Unhandled webhook event type: ${type}`);
        return new Response('OK', { status: 200 });
    }

    // Update email tracking status
    // TODO: Re-enable email status update when type instantiation issue is resolved
    // const updateResult = await ctx.runMutation(internal.emailTracking.updateEmailStatus, {
    //   emailId,
    //   status: status as any,
    //   errorMessage,
    //   bounceReason,
    // });
    const updateResult = { success: true }; // Temporarily mock result

    if (updateResult.success) {
      console.log(`✅ Email status updated: ${emailId} -> ${status}`);
    } else {
      console.error(`❌ Failed to update email status: ${(updateResult as any).error}`);
    }

    return new Response('OK', { status: 200 });
  } catch (error) {
    console.error('❌ Webhook processing error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
});

// Health check endpoint for webhook testing
export const webhookHealth = httpAction(async () => {
  return new Response(JSON.stringify({ 
    status: 'healthy', 
    timestamp: Date.now(),
    service: 'JobbLogg Email Webhooks'
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
});

// Email tracking pixel endpoint
export const trackEmailOpen = httpAction(async (_ctx, request) => {
  try {
    const url = new URL(request.url);
    const emailId = url.searchParams.get('emailId');

    if (emailId) {
      console.log(`👁️ Email tracking pixel accessed for: ${emailId}`);

      // Email open tracking removed - replaced with project access tracking
      console.log(`Email open tracking disabled for: ${emailId}`);
    }

    // Return a 1x1 transparent pixel
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    return new Response(pixel, {
      status: 200,
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  } catch (error) {
    console.error('❌ Email tracking pixel error:', error);

    // Still return a pixel even if tracking fails
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    return new Response(pixel, {
      status: 200,
      headers: {
        'Content-Type': 'image/png',
      },
    });
  }
});

// Link click tracking endpoint
export const trackEmailClick = httpAction(async (_ctx, request) => {
  try {
    const url = new URL(request.url);
    const emailId = url.searchParams.get('emailId');
    const targetUrl = url.searchParams.get('url');

    if (emailId) {
      console.log(`🔗 Email link clicked for: ${emailId}`);

      // Email click tracking removed - replaced with project access tracking
      console.log(`Email click tracking disabled for: ${emailId}`);
    }

    // Redirect to the target URL
    if (targetUrl) {
      return new Response(null, {
        status: 302,
        headers: {
          'Location': decodeURIComponent(targetUrl),
        },
      });
    }

    // If no target URL, return a simple success message
    return new Response('Link tracked successfully', { status: 200 });
  } catch (error) {
    console.error('❌ Email click tracking error:', error);

    // Still redirect if we have a target URL
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');

    if (targetUrl) {
      return new Response(null, {
        status: 302,
        headers: {
          'Location': decodeURIComponent(targetUrl),
        },
      });
    }

    return new Response('Error tracking click', { status: 500 });
  }
});

// Test webhook endpoint for development
export const testWebhook = httpAction(async (_ctx, request) => {
  try {
    const body = await request.json();
    console.log('🧪 Test webhook received:', body);

    // Simulate processing a test email status update
    if (body.emailId && body.status) {
      // TODO: Re-enable email status update when type instantiation issue is resolved
      // const updateResult = await ctx.runMutation(internal.emailTracking.updateEmailStatus, {
      //   emailId: body.emailId,
      //   status: body.status,
      //   errorMessage: body.errorMessage,
      //   bounceReason: body.bounceReason,
      // });
      const updateResult = { success: true }; // Temporarily mock result

      return new Response(JSON.stringify({
        success: true,
        message: 'Test webhook processed',
        updateResult
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    return new Response(JSON.stringify({
      success: false,
      message: 'Missing required fields: emailId and status'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('❌ Test webhook error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
});

// Simple Stripe webhook handler for testing
export const stripeWebhook = httpAction(async (_ctx, request) => {
  console.log('🎯 Stripe webhook called from root webhooks.ts!');

  try {
    const body = await request.text();
    console.log('📦 Webhook body length:', body.length);

    return new Response('Stripe webhook received successfully!', {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  } catch (error) {
    console.error('❌ Stripe webhook error:', error);
    return new Response('Error processing webhook', { status: 500 });
  }
});

// ===== STRIPE WEBHOOK UTILITIES =====

// Check if webhook event has already been processed (idempotency)
export const checkEventProcessed = internalQuery({
  args: { eventId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("webhookEvents")
      .withIndex("by_event_id", (q) => q.eq("eventId", args.eventId))
      .first();
  },
});

// Mark webhook event as processed
export const markEventProcessed = internalMutation({
  args: {
    eventId: v.string(),
    eventType: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("webhookEvents", {
      eventId: args.eventId,
      eventType: args.eventType,
      processedAt: Date.now(),
    });
  },
});

// Clean up old webhook events (older than 30 days)
export const cleanupOldWebhookEvents = internalMutation({
  handler: async (ctx) => {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    const oldEvents = await ctx.db
      .query("webhookEvents")
      .filter((q) => q.lt(q.field("processedAt"), thirtyDaysAgo))
      .collect();

    for (const event of oldEvents) {
      await ctx.db.delete(event._id);
    }

    console.log(`Cleaned up ${oldEvents.length} old webhook events`);
  },
});
