import { v } from "convex/values";
import { mutation } from "../_generated/server";

// Migration script to move existing project.jobData.personalNotes to user-specific notes
export const migrateAllPersonalNotes = mutation({
  args: {
    adminUserId: v.string(), // Admin performing the migration
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.adminUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get all projects that have personal notes in jobData
    const projects = await ctx.db
      .query("projects")
      .collect();

    let migratedCount = 0;
    let skippedCount = 0;
    const errors: string[] = [];

    for (const project of projects) {
      try {
        // Check if project has personal notes to migrate (using any to handle legacy data)
        const legacyJobData = project.jobData as any;
        if (legacyJobData?.personalNotes && legacyJobData.personalNotes.trim()) {
          // Check if user already has notes for this project
          const existingUserNotes = await ctx.db
            .query("userProjectNotes")
            .withIndex("by_user_and_project", (q) => 
              q.eq("userId", project.userId).eq("projectId", project._id)
            )
            .first();

          if (!existingUserNotes) {
            // Migrate to project owner's user-specific notes
            const now = Date.now();
            await ctx.db.insert("userProjectNotes", {
              projectId: project._id,
              userId: project.userId, // Assign to project owner
              personalNotes: legacyJobData.personalNotes,
              createdAt: now,
              updatedAt: now,
            });

            // Remove personal notes from project.jobData (cast to any to handle legacy data)
            const updatedJobData = {
              jobDescription: project.jobData?.jobDescription || '',
              photos: project.jobData?.photos || [],
              accessNotes: project.jobData?.accessNotes || '',
              equipmentNeeds: project.jobData?.equipmentNeeds || '',
              unresolvedQuestions: project.jobData?.unresolvedQuestions || '',
              // personalNotes removed - migrated to userProjectNotes table
            };

            await ctx.db.patch(project._id, {
              jobData: updatedJobData,
            });

            migratedCount++;
            console.log(`Migrated personal notes for project ${project._id} (${project.name})`);
          } else {
            skippedCount++;
            console.log(`Skipped project ${project._id} - user already has notes`);
          }
        } else {
          skippedCount++;
        }
      } catch (error) {
        const errorMsg = `Failed to migrate project ${project._id}: ${error}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    return {
      success: true,
      migratedCount,
      skippedCount,
      totalProjects: projects.length,
      errors,
      message: `Migration completed: ${migratedCount} projects migrated, ${skippedCount} skipped, ${errors.length} errors`
    };
  },
});

// Helper function to check migration status
export const checkMigrationStatus = mutation({
  args: {
    adminUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.adminUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Count projects with personal notes in jobData
    const projects = await ctx.db
      .query("projects")
      .collect();

    let projectsWithPersonalNotes = 0;
    let projectsWithUserNotes = 0;

    for (const project of projects) {
      const legacyJobData = project.jobData as any;
      if (legacyJobData?.personalNotes && legacyJobData.personalNotes.trim()) {
        projectsWithPersonalNotes++;
      }

      const userNotes = await ctx.db
        .query("userProjectNotes")
        .withIndex("by_project", (q) => q.eq("projectId", project._id))
        .collect();

      if (userNotes.length > 0) {
        projectsWithUserNotes++;
      }
    }

    return {
      totalProjects: projects.length,
      projectsWithPersonalNotes,
      projectsWithUserNotes,
      migrationNeeded: projectsWithPersonalNotes > 0,
    };
  },
});
