import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Get user's personal notes for a specific project
export const getUserProjectNotes = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string(), // Clerk ID
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure user can only access their own notes
    if (identity.subject !== args.userId) {
      throw new Error("Du kan kun se dine egne notater");
    }

    // Verify user has access to the project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      // Return empty notes instead of throwing error to prevent UI crashes
      return {
        personalNotes: "",
        createdAt: undefined,
        updatedAt: undefined,
      };
    }

    // Get user's notes for this project
    const notes = await ctx.db
      .query("userProjectNotes")
      .withIndex("by_user_and_project", (q) => 
        q.eq("userId", args.userId).eq("projectId", args.projectId)
      )
      .first();

    return {
      personalNotes: notes?.personalNotes || "",
      createdAt: notes?.createdAt,
      updatedAt: notes?.updatedAt,
    };
  },
});

// Update or create user's personal notes for a project
export const updateUserProjectNotes = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(), // Clerk ID
    personalNotes: v.string(),
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure user can only update their own notes
    if (identity.subject !== args.userId) {
      throw new Error("Du kan kun redigere dine egne notater");
    }

    // Verify user has access to the project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      // Silently fail if user no longer has access (e.g., after withdrawal)
      return { success: false, reason: "no_access" };
    }

    // Check if notes already exist for this user and project
    const existingNotes = await ctx.db
      .query("userProjectNotes")
      .withIndex("by_user_and_project", (q) => 
        q.eq("userId", args.userId).eq("projectId", args.projectId)
      )
      .first();

    const now = Date.now();

    if (existingNotes) {
      // Update existing notes
      await ctx.db.patch(existingNotes._id, {
        personalNotes: args.personalNotes,
        updatedAt: now,
      });
    } else {
      // Create new notes
      await ctx.db.insert("userProjectNotes", {
        projectId: args.projectId,
        userId: args.userId,
        personalNotes: args.personalNotes,
        createdAt: now,
        updatedAt: now,
      });
    }

    return { success: true };
  },
});

// Delete user's personal notes for a project
export const deleteUserProjectNotes = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(), // Clerk ID
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure user can only delete their own notes
    if (identity.subject !== args.userId) {
      throw new Error("Du kan kun slette dine egne notater");
    }

    // Find and delete user's notes for this project
    const notes = await ctx.db
      .query("userProjectNotes")
      .withIndex("by_user_and_project", (q) => 
        q.eq("userId", args.userId).eq("projectId", args.projectId)
      )
      .first();

    if (notes) {
      await ctx.db.delete(notes._id);
    }

    return { success: true };
  },
});

// Migration function to move existing project.jobData.personalNotes to user-specific notes
export const migratePersonalNotesToUserSpecific = mutation({
  args: {
    projectId: v.id("projects"),
    adminUserId: v.string(), // Admin performing the migration
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.adminUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Verify user is project owner or administrator
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.adminUserId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess || (userAccess.accessLevel !== "owner" && userAccess.accessLevel !== "administrator")) {
      throw new Error("Du har ikke tilgang til å migrere notater for dette prosjektet");
    }

    // Check if project has personal notes to migrate (using any to handle legacy data)
    const legacyJobData = project.jobData as any;
    if (legacyJobData?.personalNotes) {
      const existingPersonalNotes = legacyJobData.personalNotes;

      // Check if user already has notes for this project
      const existingUserNotes = await ctx.db
        .query("userProjectNotes")
        .withIndex("by_user_and_project", (q) => 
          q.eq("userId", project.userId).eq("projectId", args.projectId)
        )
        .first();

      if (!existingUserNotes && existingPersonalNotes.trim()) {
        // Migrate to project owner's user-specific notes
        const now = Date.now();
        await ctx.db.insert("userProjectNotes", {
          projectId: args.projectId,
          userId: project.userId, // Assign to project owner
          personalNotes: existingPersonalNotes,
          createdAt: now,
          updatedAt: now,
        });
      }

      // Remove personal notes from project.jobData
      if (project.jobData) {
        const updatedJobData = {
          ...project.jobData,
          personalNotes: "", // Clear the shared personal notes
        };

        await ctx.db.patch(args.projectId, {
          jobData: updatedJobData,
        });
      }
    }

    return { success: true, migrated: !!legacyJobData?.personalNotes };
  },
});
