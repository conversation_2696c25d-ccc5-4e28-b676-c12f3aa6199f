// Email templates for JobbLogg notifications

export interface TeamInvitationEmailData {
  invitedByName: string;
  inviterEmail: string;
  companyName: string;
  invitationLink: string;
  expiresAt: number;
  role: 'administrator' | 'utfoerende';
}

export interface JobbLoggInvitationEmailData {
  firstName: string;
  lastName: string;
  invitedByName: string;
  inviterEmail: string;
  companyName: string;
  invitationLink: string;
  expiresAt: number;
  role: 'administrator' | 'utfoerende';
}

export interface CustomerNotificationEmailData {
  customerName: string;
  customerType: 'privat' | 'bedrift';
  contactPerson?: string; // For bedrift customers
  projectName: string;
  projectDescription: string;
  contractorCompanyName: string;
  contractorContactPerson: string;
  contractorPhone?: string;
  contractorEmail?: string;
  sharedProjectUrl: string;
}

export function generateTeamInvitationEmail(data: TeamInvitationEmailData): {
  subject: string;
  html: string;
  text: string;
} {
  const expiryDate = new Date(data.expiresAt).toLocaleDateString('nb-NO', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const roleText = data.role === 'administrator' ? 'administrator' : 'utførende';

  const subject = `Invitasjon til JobbLogg team - ${data.companyName}`;

  const html = `
<!DOCTYPE html>
<html lang="no">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invitasjon til JobbLogg team</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #1F2937;
      background-color: #F8FAFC;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #FFFFFF;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
      color: white;
      padding: 32px 24px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 700;
    }
    .header p {
      margin: 8px 0 0 0;
      font-size: 16px;
      opacity: 0.9;
    }
    .content {
      padding: 32px 24px;
    }
    .greeting {
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;
      margin-bottom: 16px;
    }
    .message {
      font-size: 16px;
      color: #4B5563;
      margin-bottom: 24px;
      line-height: 1.7;
    }
    .invitation-details {
      background-color: #F8FAFC;
      border: 1px solid #E5E7EB;
      border-radius: 8px;
      padding: 20px;
      margin: 24px 0;
    }
    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
    }
    .detail-label {
      color: #6B7280;
      font-weight: 500;
    }
    .detail-value {
      color: #1F2937;
      font-weight: 600;
    }
    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
      color: #FFFFFF !important;
      text-decoration: none;
      padding: 16px 32px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 16px;
      text-align: center;
      margin: 24px 0;
      box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
      transition: all 0.2s ease;
    }
    .cta-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 12px -1px rgba(37, 99, 235, 0.4);
    }
    .footer {
      background-color: #F8FAFC;
      padding: 24px;
      text-align: center;
      border-top: 1px solid #E5E7EB;
    }
    .footer p {
      margin: 0;
      font-size: 14px;
      color: #6B7280;
    }
    .footer a {
      color: #2563EB;
      text-decoration: none;
    }
    .expiry-notice {
      background-color: #FEF3C7;
      border: 1px solid #F59E0B;
      border-radius: 6px;
      padding: 12px 16px;
      margin: 16px 0;
      font-size: 14px;
      color: #92400E;
    }
    @media (max-width: 600px) {
      .container {
        margin: 0;
        border-radius: 0;
      }
      .header, .content, .footer {
        padding: 24px 16px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔨 JobbLogg</h1>
      <p>Invitasjon til team</p>
    </div>
    
    <div class="content">
      <div class="greeting">Hei!</div>
      
      <div class="message">
        Du har blitt invitert av <strong>${data.invitedByName}</strong> til å bli med i teamet for 
        <strong>${data.companyName}</strong> på JobbLogg.
      </div>
      
      <div class="message">
        Som <strong>${roleText}</strong> vil du få tilgang til å administrere prosjekter, 
        kommunisere med kunder og holde oversikt over alle arbeidsoppgaver.
      </div>
      
      <div class="invitation-details">
        <div class="detail-row">
          <span class="detail-label">Bedrift:</span>
          <span class="detail-value">${data.companyName}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Rolle:</span>
          <span class="detail-value">${roleText}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Invitert av:</span>
          <span class="detail-value">${data.invitedByName}</span>
        </div>
      </div>
      
      <div style="text-align: center;">
        <a href="${data.invitationLink}" class="cta-button">
          Bli med i teamet
        </a>
      </div>
      
      <div class="expiry-notice">
        ⏰ <strong>Viktig:</strong> Denne invitasjonen utløper ${expiryDate}. 
        Klikk på lenken ovenfor for å komme i gang.
      </div>
      
      <div class="message">
        Hvis du har spørsmål om JobbLogg eller denne invitasjonen, kan du kontakte
        <strong>${data.invitedByName}</strong> direkte på
        <a href="mailto:${data.inviterEmail}" style="color: #2563EB; text-decoration: none;">${data.inviterEmail}</a>.
      </div>
    </div>
    
    <div class="footer">
      <p>
        Denne e-posten ble sendt fra JobbLogg.
      </p>
    </div>
  </div>
</body>
</html>`;

  const text = `
Hei!

Du har blitt invitert av ${data.invitedByName} til å bli med i teamet for ${data.companyName} på JobbLogg.

Som ${roleText} vil du få tilgang til å administrere prosjekter, kommunisere med kunder og holde oversikt over alle arbeidsoppgaver.

Detaljer:
- Bedrift: ${data.companyName}
- Rolle: ${roleText}
- Invitert av: ${data.invitedByName}

Klikk på denne lenken for å bli med i teamet:
${data.invitationLink}

VIKTIG: Denne invitasjonen utløper ${expiryDate}.

Hvis du har spørsmål om JobbLogg eller denne invitasjonen, kan du kontakte ${data.invitedByName} direkte på ${data.inviterEmail}.

Mvh,
JobbLogg teamet

---
Denne e-posten ble sendt fra JobbLogg.
`;

  return { subject, html, text };
}

export function generateJobbLoggInvitationEmail(data: JobbLoggInvitationEmailData): {
  subject: string;
  html: string;
  text: string;
} {
  const expiryDate = new Date(data.expiresAt).toLocaleDateString('nb-NO', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  const roleText = data.role === 'administrator' ? 'administrator' : 'utførende';

  const subject = `🚀 Invitasjon til ${data.companyName} på JobbLogg`;

  const html = `
<!DOCTYPE html>
<html lang="no">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invitasjon til JobbLogg</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #1F2937;
      background-color: #F8FAFC;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #FFFFFF;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
      color: white;
      padding: 32px 24px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 700;
    }
    .content {
      padding: 32px 24px;
    }
    .invitation-button {
      display: inline-block;
      background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
      color: white !important;
      text-decoration: none;
      padding: 16px 32px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 18px;
      text-align: center;
      margin: 24px 0;
      box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.3);
      transition: all 0.2s;
    }
    .invitation-button:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
    }
    .info-box {
      background: #EFF6FF;
      border: 2px solid #BFDBFE;
      border-radius: 8px;
      padding: 16px;
      margin: 20px 0;
    }
    .footer {
      background: #F8FAFC;
      padding: 24px;
      text-align: center;
      color: #6B7280;
      font-size: 14px;
    }
    .highlight {
      color: #2563EB;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚀 Velkommen til teamet!</h1>
      <p>Du har blitt invitert til å bli med i ${data.companyName}</p>
    </div>

    <div class="content">
      <p>Hei <strong>${data.firstName}</strong>!</p>

      <p><strong>${data.invitedByName}</strong> har invitert deg til å bli med i teamet på JobbLogg. Klikk på lenken nedenfor for å fullføre registreringen.</p>

      <div style="text-align: center;">
        <a href="${data.invitationLink}" class="invitation-button">
          🚀 Bli med i teamet nå
        </a>
      </div>

      <div class="info-box">
        <h3 style="margin-top: 0; color: #1D4ED8;">📋 Invitasjonsdetaljer:</h3>
        <ul style="margin: 0; padding-left: 20px;">
          <li><strong>Bedrift:</strong> ${data.companyName}</li>
          <li><strong>Rolle:</strong> <span class="highlight">${roleText === 'administrator' ? 'Administrator' : 'Utførende'}</span></li>
          <li><strong>Invitert av:</strong> ${data.invitedByName}</li>
        </ul>
      </div>

      <h3>🎯 Hva skjer når du klikker?</h3>
      <ol>
        <li>Du kommer til en pre-utfylt registreringsside</li>
        <li>Velg et passord og rediger informasjonen hvis ønskelig</li>
        <li>Klikk "Registrer og logg inn" - ferdig!</li>
      </ol>

      <p><strong>⏰ Viktig:</strong> Denne invitasjonen utløper <span class="highlight">${expiryDate}</span>.</p>

      <p>Hvis du har spørsmål, kan du kontakte ${data.invitedByName} direkte på <a href="mailto:${data.inviterEmail}">${data.inviterEmail}</a>.</p>
    </div>

    <div class="footer">
      <p>Denne e-posten ble sendt fra JobbLogg.<br>
      Hvis du ikke forventet denne invitasjonen, kan du trygt ignorere denne e-posten.</p>
    </div>
  </div>
</body>
</html>
`;

  const text = `
Hei ${data.firstName}!

Du har blitt invitert til å bli med i ${data.companyName} på JobbLogg!

${data.invitedByName} har sendt deg en invitasjon. Klikk på lenken nedenfor for å fullføre registreringen:

${data.invitationLink}

Invitasjonsdetaljer:
- Bedrift: ${data.companyName}
- Rolle: ${roleText === 'administrator' ? 'Administrator' : 'Utførende'}
- Invitert av: ${data.invitedByName}

Hva skjer når du klikker:
1. Du kommer til en pre-utfylt registreringsside
2. Velg et passord og rediger informasjonen hvis ønskelig
3. Klikk "Registrer og logg inn" - ferdig!

VIKTIG: Denne invitasjonen utløper ${expiryDate}.

Hvis du har spørsmål om JobbLogg eller denne invitasjonen, kan du kontakte ${data.invitedByName} direkte på ${data.inviterEmail}.

Mvh,
JobbLogg teamet

---
Denne e-posten ble sendt fra JobbLogg.
Hvis du ikke forventet denne invitasjonen, kan du trygt ignorere denne e-posten.
`;

  return { subject, html, text };
}

export function generateCustomerNotificationEmail(data: CustomerNotificationEmailData & { emailId?: string }): {
  subject: string;
  html: string;
  text: string;
} {
  // Determine greeting and content based on customer type
  const isBusinessCustomer = data.customerType === 'bedrift';
  const customerDisplayName = isBusinessCustomer && data.contactPerson
    ? data.contactPerson
    : data.customerName;

  const greeting = isBusinessCustomer
    ? `Hei ${customerDisplayName}`
    : `Hei ${customerDisplayName}`;

  const customerTypeText = isBusinessCustomer ? 'bedrift' : 'privatkunde';

  const subject = `Nytt prosjekt fra ${data.contractorCompanyName} - ${data.projectName}`;

  const html = `
<!DOCTYPE html>
<html lang="no">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Nytt prosjekt - ${data.projectName}</title>
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #1F2937;
      background-color: #F8FAFC;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #FFFFFF;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
      color: white;
      padding: 32px 24px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 700;
    }
    .header p {
      margin: 8px 0 0 0;
      font-size: 16px;
      opacity: 0.9;
    }
    .content {
      padding: 32px 24px;
    }
    .greeting {
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;
      margin-bottom: 16px;
    }
    .message {
      font-size: 16px;
      color: #4B5563;
      margin-bottom: 24px;
      line-height: 1.7;
    }
    .project-details {
      background-color: #F8FAFC;
      border: 1px solid #E5E7EB;
      border-radius: 8px;
      padding: 20px;
      margin: 24px 0;
    }
    .project-details h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1F2937;
    }
    .project-details p {
      margin: 8px 0;
      color: #4B5563;
    }
    .project-details strong {
      color: #1F2937;
    }
    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
      color: white !important;
      text-decoration: none !important;
      padding: 16px 32px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 16px;
      text-align: center;
      margin: 24px 0;
      transition: transform 0.2s ease;
    }
    .cta-button:hover {
      transform: translateY(-1px);
    }
    .contractor-info {
      background-color: #F0F9FF;
      border: 1px solid #BAE6FD;
      border-radius: 8px;
      padding: 20px;
      margin: 24px 0;
    }
    .contractor-info h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1F2937;
    }
    .contractor-info p {
      margin: 4px 0;
      color: #4B5563;
    }
    .footer {
      background-color: #F8FAFC;
      padding: 24px;
      text-align: center;
      border-top: 1px solid #E5E7EB;
    }
    .footer p {
      margin: 4px 0;
      font-size: 14px;
      color: #6B7280;
    }
    .features-list {
      margin: 16px 0;
      padding-left: 0;
      list-style: none;
    }
    .features-list li {
      margin: 8px 0;
      padding-left: 24px;
      position: relative;
      color: #4B5563;
    }
    .features-list li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #10B981;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏗️ Nytt prosjekt</h1>
      <p>Fra ${data.contractorCompanyName}</p>
    </div>

    <div class="content">
      <div class="greeting">${greeting}!</div>

      <div class="message">
        ${data.contractorCompanyName} har opprettet et nytt prosjekt for deg${isBusinessCustomer ? ' som ' + customerTypeText : ''}.
        Du kan nå følge prosjektets fremdrift i sanntid gjennom vår prosjektportal.
      </div>

      <div class="project-details">
        <h3>📋 Prosjektdetaljer</h3>
        <p><strong>Prosjektnavn:</strong> ${data.projectName}</p>
        ${data.projectDescription ? `<p><strong>Beskrivelse:</strong> ${data.projectDescription}</p>` : ''}
        <p><strong>Leverandør:</strong> ${data.contractorCompanyName}</p>
        <p><strong>Kontaktperson:</strong> ${data.contractorContactPerson}</p>
      </div>

      <div style="text-align: center;">
        <a href="${data.sharedProjectUrl}" class="cta-button">
          🔗 Se prosjektet ditt
        </a>
      </div>

      <div class="message">
        <strong>Hva kan du se i prosjektportalen?</strong>
      </div>

      <ul class="features-list">
        <li>Sanntidsoppdateringer på prosjektets fremdrift</li>
        <li>Bilder og dokumentasjon fra arbeidet</li>
        <li>Kommunikasjon direkte med leverandøren</li>
        <li>Oversikt over prosjektdetaljer og tidsplan</li>
      </ul>

      <div class="contractor-info">
        <h4>📞 Kontaktinformasjon</h4>
        <p><strong>Bedrift:</strong> ${data.contractorCompanyName}</p>
        <p><strong>Kontaktperson:</strong> ${data.contractorContactPerson}</p>
        ${data.contractorPhone ? `<p><strong>Telefon:</strong> ${data.contractorPhone}</p>` : ''}
        ${data.contractorEmail ? `<p><strong>E-post:</strong> ${data.contractorEmail}</p>` : ''}
      </div>

      <div class="message">
        Du trenger ikke å opprette en konto eller logge inn for å se prosjektet.
        Klikk bare på lenken ovenfor for å komme i gang.
      </div>
    </div>

    <div class="footer">
      <p><strong>JobbLogg</strong> - Transparent prosjektoppfølging</p>
      <p>Denne e-posten ble sendt automatisk når prosjektet ditt ble opprettet.</p>
    </div>
  </div>

  <!-- Removed tracking pixel to improve deliverability -->
</body>
</html>
  `;

  const text = `
${greeting}!

${data.contractorCompanyName} har opprettet et nytt prosjekt for deg${isBusinessCustomer ? ' som ' + customerTypeText : ''}.
Du kan nå følge prosjektets fremdrift i sanntid gjennom vår prosjektportal.

PROSJEKTDETALJER:
- Prosjektnavn: ${data.projectName}
${data.projectDescription ? `- Beskrivelse: ${data.projectDescription}` : ''}
- Leverandør: ${data.contractorCompanyName}
- Kontaktperson: ${data.contractorContactPerson}

SE PROSJEKTET DITT:
${data.sharedProjectUrl}

Hva kan du se i prosjektportalen?
✓ Sanntidsoppdateringer på prosjektets fremdrift
✓ Bilder og dokumentasjon fra arbeidet
✓ Kommunikasjon direkte med leverandøren
✓ Oversikt over prosjektdetaljer og tidsplan

KONTAKTINFORMASJON:
- Bedrift: ${data.contractorCompanyName}
- Kontaktperson: ${data.contractorContactPerson}
${data.contractorPhone ? `- Telefon: ${data.contractorPhone}` : ''}
${data.contractorEmail ? `- E-post: ${data.contractorEmail}` : ''}

Du trenger ikke å opprette en konto eller logge inn for å se prosjektet.
Klikk bare på lenken ovenfor for å komme i gang.

---
JobbLogg - Transparent prosjektoppfølging
Denne e-posten ble sendt automatisk når prosjektet ditt ble opprettet.
  `;

  return { subject, html, text };
}
