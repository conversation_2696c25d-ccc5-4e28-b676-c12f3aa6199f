import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

/**
 * Update user's last login timestamp
 * Called when user successfully authenticates
 */
export const updateLastLogin = mutation({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Find user record
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    const now = Date.now();

    // Update login and activity timestamps
    await ctx.db.patch(user._id, {
      lastLoginAt: now,
      lastActivityAt: now,
      updatedAt: now,
    });

    return {
      success: true,
      lastLoginAt: now,
    };
  },
});

/**
 * Update user's last activity timestamp
 * Called when user performs any action (creates project, sends message, etc.)
 */
export const updateLastActivity = mutation({
  args: {
    clerkUserId: v.string(),
    activityType: v.optional(v.string()), // Optional activity type for analytics
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Find user record
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    const now = Date.now();

    // Update activity timestamp
    await ctx.db.patch(user._id, {
      lastActivityAt: now,
      updatedAt: now,
    });

    return {
      success: true,
      lastActivityAt: now,
    };
  },
});

/**
 * Get user activity information
 * Used for displaying "sist logget inn" in team management
 */
export const getUserActivity = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Find user record
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    return {
      clerkUserId: user.clerkUserId,
      lastLoginAt: user.lastLoginAt,
      lastActivityAt: user.lastActivityAt,
    };
  },
});

/**
 * Get activity information for all team members
 * Used by administrators to see team activity overview
 */
export const getTeamActivity = query({
  args: {
    requestedBy: v.string(), // Administrator's Clerk ID
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.requestedBy) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get requesting user to verify they are an administrator
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.requestedBy))
      .first();

    if (!requestingUser) {
      throw new Error("Bruker ikke funnet");
    }

    // Verify user is an administrator
    if (requestingUser.role !== "administrator") {
      throw new Error("Kun administratorer kan se team aktivitet");
    }

    // Get all team members for the same company
    const allMembers = await ctx.db
      .query("users")
      .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", requestingUser.contractorCompanyId))
      .collect();

    // Filter out deleted/inactive members and return activity data
    const activeMembers = allMembers.filter(member =>
      member.isActive !== false &&
      member.invitationStatus !== "expired"
    );

    return activeMembers.map(member => ({
      clerkUserId: member.clerkUserId,
      role: member.role,
      lastLoginAt: member.lastLoginAt,
      lastActivityAt: member.lastActivityAt,
      // Include invitation data for display
      firstName: member.invitationFirstName,
      lastName: member.invitationLastName,
      email: member.invitationEmail,
      phone: member.invitationPhone,
    }));
  },
});
