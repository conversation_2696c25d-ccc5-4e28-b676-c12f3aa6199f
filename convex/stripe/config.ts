// Stripe configuration for JobbLogg
// Norwegian pricing and localization settings

export const STRIPE_CONFIG = {
  // Products and prices (actual Stripe Price IDs)
  products: {
    basic: {
      monthly: "price_1RxQS9RqXwHRnsDwF2ieWSxD",
      yearly: "price_1RxQYVRqXwHRnsDwmQywZuIl"
    },
    professional: {
      monthly: "price_1RxQaBRqXwHRnsDwseTmpmMt",
      yearly: "price_1RxQdJRqXwHRnsDwBKcL70dh"
    },
    enterprise: {
      monthly: "price_1RxQhBRqXwHRnsDwjLWNB0Ah",
      yearly: "price_1RxQiXRqXwHRnsDwCnfPA474"
    }
  },
  
  // Customer Portal configuration
  portalConfiguration: {
    business_profile: {
      headline: "Administrer ditt JobbLogg-abonnement",
    },
    features: {
      payment_method_update: { enabled: true },
      invoice_history: { enabled: true },
      subscription_update: {
        enabled: true,
        default_allowed_updates: ["price", "promotion_code"],
        proration_behavior: "create_prorations"
      },
      subscription_cancel: {
        enabled: true,
        mode: "at_period_end",
        proration_behavior: "none"
      }
    },
    default_return_url: process.env.CONVEX_SITE_URL + "/dashboard"
  },
  
  // Checkout configuration
  checkoutDefaults: {
    mode: "subscription" as const,
    payment_method_types: ["card"] as const,
    allow_promotion_codes: true,
    billing_address_collection: "required" as const,
    tax_id_collection: { enabled: true },
    locale: "no" as const,
    subscription_data: {
      trial_period_days: 7,
    }
  }
} as const;

// Plan limits and pricing (Hard Limit Approach) - Updated with actual Stripe prices
export const PLAN_LIMITS = {
  basic: {
    name: "Liten bedrift",
    maxSeats: 9,
    employeeRange: "1–9 ansatte",
    monthlyPrice: 299,
    yearlyPrice: 2868
  },
  professional: {
    name: "Mellomstor bedrift",
    maxSeats: 49,
    employeeRange: "10–49 ansatte",
    monthlyPrice: 999,
    yearlyPrice: 9588
  },
  enterprise: {
    name: "Stor bedrift",
    maxSeats: 249,
    employeeRange: "50–249 ansatte",
    monthlyPrice: 2999,
    yearlyPrice: 28788
  }
} as const;

// Hard Limit Enforcement
export const SEAT_ENFORCEMENT = {
  ENFORCEMENT_MODE: "hard_limit" as const, // Block at limit
  WARNING_THRESHOLD: 0.8, // Warn at 80% capacity (7/9 seats)
  CRITICAL_THRESHOLD: 0.9, // Critical warning at 90% capacity (8/9 seats)
} as const;

// Stripe webhook events we need to handle
export const REQUIRED_WEBHOOK_EVENTS = [
  "customer.created",
  "checkout.session.completed",
  "customer.subscription.created",
  "customer.subscription.updated",
  "customer.subscription.deleted",
  "customer.subscription.trial_will_end",
  "invoice.paid",
  "invoice.payment_failed",
  "payment_intent.succeeded",
  "payment_intent.payment_failed"
] as const;

// Norwegian localization strings
export const NORWEGIAN_STRINGS = {
  trialReminders: {
    day3: {
      subject: "⏰ 4 dager igjen av gratis prøveperioden",
      message: "Du har 4 dager igjen av din gratis prøveperiode på JobbLogg."
    },
    day5: {
      subject: "⚠️ 2 dager igjen av gratis prøveperioden", 
      message: "Du har kun 2 dager igjen av din gratis prøveperiode på JobbLogg."
    },
    day24h: {
      subject: "🚨 24 timer igjen av gratis prøveperioden",
      message: "Din gratis prøveperiode utløper i morgen. Oppgrader nå for å fortsette."
    }
  },
  seatNotifications: {
    approaching: "Du nærmer deg plangrensen",
    critical: "Kun få plasser igjen på din plan",
    reached: "Plangrense nådd - oppgrader for å legge til flere"
  }
} as const;
