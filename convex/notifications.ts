import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Get comprehensive notification log for a user
export const getNotificationLog = query({
  args: {
    userId: v.string(),
    limit: v.optional(v.number()),
    includeRead: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const includeRead = args.includeRead ?? false;

    // Get the requesting user
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return [];
    }

    // Build base query
    let notificationQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId));

    // Filter by read status if needed
    if (!includeRead) {
      notificationQuery = notificationQuery.filter((q) => q.eq(q.field("isRead"), false));
    }

    // Get notifications ordered by creation time (newest first)
    const notifications = await notificationQuery
      .order("desc")
      .take(limit);

    // Enrich notifications with additional data
    const enrichedNotifications = await Promise.all(
      notifications.map(async (notification) => {
        let projectInfo = null;
        let invitationInfo = null;

        // Get project information if available
        if (notification.data?.projectId) {
          projectInfo = await ctx.db.get(notification.data.projectId);
        }

        // Get invitation information if available
        if (notification.data?.invitationId) {
          invitationInfo = await ctx.db.get(notification.data.invitationId);
        }

        return {
          ...notification,
          projectInfo,
          invitationInfo,
          // Add helper properties for UI
          timeAgo: Date.now() - notification.createdAt,
        };
      })
    );

    return enrichedNotifications;
  },
});

// Get notification statistics
export const getNotificationStats = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the requesting user
    const requestingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!requestingUser) {
      return {
        unreadCount: 0,
        totalCount: 0,
        byType: {},
      };
    }

    // Get all notifications for the user
    const allNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Get unread notifications
    const unreadNotifications = allNotifications.filter(n => !n.isRead);

    // Count by type
    const byType = allNotifications.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      unreadCount: unreadNotifications.length,
      totalCount: allNotifications.length,
      byType,
    };
  },
});

// Mark notifications as read
export const markNotificationsAsRead = mutation({
  args: {
    userId: v.string(),
    notificationIds: v.optional(v.array(v.id("notifications"))),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    if (args.notificationIds && args.notificationIds.length > 0) {
      // Mark specific notifications as read
      await Promise.all(
        args.notificationIds.map(async (notificationId) => {
          const notification = await ctx.db.get(notificationId);
          if (notification && notification.userId === args.userId && !notification.isRead) {
            await ctx.db.patch(notificationId, {
              isRead: true,
              readAt: now,
            });
          }
        })
      );
    } else {
      // Mark all unread notifications as read
      const unreadNotifications = await ctx.db
        .query("notifications")
        .withIndex("by_user_and_read", (q) => q.eq("userId", args.userId).eq("isRead", false))
        .collect();

      await Promise.all(
        unreadNotifications.map(async (notification) => {
          await ctx.db.patch(notification._id, {
            isRead: true,
            readAt: now,
          });
        })
      );
    }

    return { success: true };
  },
});

// Create a new notification (for system use)
export const createNotification = mutation({
  args: {
    userId: v.string(),
    type: v.union(
      v.literal("invitation_declined"),
      v.literal("invitation_accepted"),
      v.literal("project_update"),
      v.literal("team_invitation"),
      v.literal("system"),
      v.literal("chat_message"),
      v.literal("project_status_change"),
      v.literal("role_changed")
    ),
    title: v.string(),
    message: v.string(),
    data: v.optional(v.object({
      invitationId: v.optional(v.id("projectAssignments")),
      projectId: v.optional(v.id("projects")),
      projectName: v.optional(v.string()),
      subcontractorName: v.optional(v.string()),
      responseMessage: v.optional(v.string()),
      responseType: v.optional(v.union(v.literal("accept"), v.literal("decline"))),
      messageId: v.optional(v.id("messages")),
      senderName: v.optional(v.string()),
      oldRole: v.optional(v.string()),
      newRole: v.optional(v.string()),
      changedBy: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const notificationId = await ctx.db.insert("notifications", {
      userId: args.userId,
      type: args.type,
      title: args.title,
      message: args.message,
      data: args.data,
      isRead: false,
      createdAt: Date.now(),
    });

    return notificationId;
  },
});
