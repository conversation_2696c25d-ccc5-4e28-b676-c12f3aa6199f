/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as clearAllProjectData from "../clearAllProjectData.js";
import type * as collaborationHistory from "../collaborationHistory.js";
import type * as contractorCompany from "../contractorCompany.js";
import type * as contractorOnboarding from "../contractorOnboarding.js";
import type * as contractorOnboardingSafe from "../contractorOnboardingSafe.js";
import type * as customers from "../customers.js";
import type * as emailTemplates from "../emailTemplates.js";
import type * as emailTracking from "../emailTracking.js";
import type * as emails_subscriptionEmails from "../emails/subscriptionEmails.js";
import type * as emails from "../emails.js";
import type * as http from "../http.js";
import type * as imageLikes from "../imageLikes.js";
import type * as linkPreviews from "../linkPreviews.js";
import type * as logEntries from "../logEntries.js";
import type * as messages from "../messages.js";
import type * as migrations_migrateCustomerTypes from "../migrations/migrateCustomerTypes.js";
import type * as migrations_migratePersonalNotes from "../migrations/migratePersonalNotes.js";
import type * as migrations from "../migrations.js";
import type * as notifications from "../notifications.js";
import type * as projects from "../projects.js";
import type * as projectsTeam from "../projectsTeam.js";
import type * as resetDatabase from "../resetDatabase.js";
import type * as seatManagement from "../seatManagement.js";
import type * as stripe_config from "../stripe/config.js";
import type * as stripe_webhooks from "../stripe/webhooks.js";
import type * as subcontractorInvitations from "../subcontractorInvitations.js";
import type * as subscriptions from "../subscriptions.js";
import type * as teamManagement from "../teamManagement.js";
import type * as testDataUtilities from "../testDataUtilities.js";
import type * as trialManagement from "../trialManagement.js";
import type * as userActivity from "../userActivity.js";
import type * as userProjectNotes from "../userProjectNotes.js";
import type * as webhooks from "../webhooks.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  clearAllProjectData: typeof clearAllProjectData;
  collaborationHistory: typeof collaborationHistory;
  contractorCompany: typeof contractorCompany;
  contractorOnboarding: typeof contractorOnboarding;
  contractorOnboardingSafe: typeof contractorOnboardingSafe;
  customers: typeof customers;
  emailTemplates: typeof emailTemplates;
  emailTracking: typeof emailTracking;
  "emails/subscriptionEmails": typeof emails_subscriptionEmails;
  emails: typeof emails;
  http: typeof http;
  imageLikes: typeof imageLikes;
  linkPreviews: typeof linkPreviews;
  logEntries: typeof logEntries;
  messages: typeof messages;
  "migrations/migrateCustomerTypes": typeof migrations_migrateCustomerTypes;
  "migrations/migratePersonalNotes": typeof migrations_migratePersonalNotes;
  migrations: typeof migrations;
  notifications: typeof notifications;
  projects: typeof projects;
  projectsTeam: typeof projectsTeam;
  resetDatabase: typeof resetDatabase;
  seatManagement: typeof seatManagement;
  "stripe/config": typeof stripe_config;
  "stripe/webhooks": typeof stripe_webhooks;
  subcontractorInvitations: typeof subcontractorInvitations;
  subscriptions: typeof subscriptions;
  teamManagement: typeof teamManagement;
  testDataUtilities: typeof testDataUtilities;
  trialManagement: typeof trialManagement;
  userActivity: typeof userActivity;
  userProjectNotes: typeof userProjectNotes;
  webhooks: typeof webhooks;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
