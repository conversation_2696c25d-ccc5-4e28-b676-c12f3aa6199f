import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Helper functions for data filtering based on access level
//
// Access Level Permissions:
// - Owner/Administrator: Full read/write access to all data
// - Collaborator: Full read/write access to shared project data (personal notes are user-specific)
// - Subcontractor: Read-only access to essential work info (job description, photos, access notes, equipment needs, unresolved questions, location, contact info)
// - Viewer: Basic project info only (no access notes, contact details, or location)
//
// NOTE: Personal notes are now handled separately via userProjectNotes table
function filterProjectDataByAccessLevel(project: any, accessLevel: string) {
  // Owner and administrator get full access
  if (accessLevel === 'owner' || accessLevel === 'administrator') {
    return project;
  }

  // Collaborators get full access to shared project data
  // (personal notes are now user-specific and handled separately)
  if (accessLevel === 'collaborator') {
    return project;
  }

  // Subcontractors get read-only access to essential work information
  if (accessLevel === 'subcontractor') {
    const filteredJobData = project.jobData ? {
      jobDescription: project.jobData.jobDescription, // Read-only job description
      photos: project.jobData.photos || [], // Read-only photos for reference
      accessNotes: project.jobData.accessNotes || '', // Read-only access information
      equipmentNeeds: project.jobData.equipmentNeeds || '', // Read-only equipment information
      unresolvedQuestions: project.jobData.unresolvedQuestions || '', // Read-only questions information
      // personalNotes removed - now user-specific via userProjectNotes table
    } : undefined;

    return {
      _id: project._id, // CRITICAL: Include _id for frontend components
      name: project.name,
      description: project.description,
      userId: project.userId,
      customerId: project.customerId,
      sharedId: project.sharedId,
      createdAt: project.createdAt,
      isArchived: project.isArchived,
      archivedAt: project.archivedAt,
      jobData: filteredJobData,
      // Essential contact information for project coordination
      projectContactPersonId: project.projectContactPersonId,
      useProjectSpecificContact: project.useProjectSpecificContact,
      // Hide sensitive business fields
      shareSettings: undefined,
      isPubliclyShared: undefined,
    };
  }

  // Viewers get minimal access
  if (accessLevel === 'viewer') {
    const filteredJobData = project.jobData ? {
      jobDescription: project.jobData.jobDescription, // Basic job description only
      photos: project.jobData.photos || [], // Photos for reference
      accessNotes: '', // Hide access notes from viewers
      equipmentNeeds: '', // Hide equipment needs
      unresolvedQuestions: '', // Hide unresolved questions
      // personalNotes removed - now user-specific via userProjectNotes table
    } : undefined;

    return {
      _id: project._id, // CRITICAL: Include _id for frontend components
      name: project.name,
      description: project.description,
      userId: project.userId,
      customerId: project.customerId,
      sharedId: project.sharedId,
      createdAt: project.createdAt,
      isArchived: project.isArchived,
      archivedAt: project.archivedAt,
      jobData: filteredJobData,
      // Hide sensitive fields
      shareSettings: undefined,
      projectContactPersonId: undefined,
      useProjectSpecificContact: undefined,
      isPubliclyShared: undefined,
    };
  }

  // Default: minimal access
  return {
    _id: project._id, // CRITICAL: Include _id for frontend components
    name: project.name,
    description: project.description,
    createdAt: project.createdAt,
    isArchived: project.isArchived,
  };
}

function filterCustomerDataByAccessLevel(customer: any, accessLevel: string) {
  if (!customer) return null;

  // Owner and administrator get full customer data
  if (accessLevel === 'owner' || accessLevel === 'administrator') {
    return customer;
  }

  // Collaborators get most customer data
  if (accessLevel === 'collaborator') {
    return customer;
  }

  // Subcontractors get essential work-related information
  if (accessLevel === 'subcontractor') {
    return {
      name: customer.name,
      type: customer.type,
      contactPerson: customer.contactPerson,
      // Essential location information for work site access
      address: customer.address,
      streetAddress: customer.streetAddress,
      postalCode: customer.postalCode,
      postalArea: customer.postalArea,
      city: customer.city,
      visitingAddress: customer.visitingAddress,
      entrance: customer.entrance,
      // Essential contact information for project coordination
      phone: customer.phone,
      email: customer.email,
      // Hide sensitive business data
      organizationNumber: undefined,
    };
  }

  // Viewers get minimal customer data
  if (accessLevel === 'viewer') {
    return {
      name: customer.name,
      type: customer.type,
      contactPerson: customer.contactPerson,
      // Hide all contact and location details for viewers
      phone: undefined,
      email: undefined,
      address: undefined,
      postalCode: undefined,
      postalArea: undefined,
      organizationNumber: undefined,
      visitingAddress: undefined,
    };
  }

  // Default: minimal customer data
  return {
    name: customer.name,
    type: customer.type,
  };
}

// Enhanced project queries with team-based access control

// Get job data for a project with access validation - dedicated query for real-time sync
export const getJobDataForUser = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Inline access validation to ensure clean dependency tracking
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    // Check direct project ownership
    let userAccess = { hasAccess: false, accessLevel: 'none' };

    if (project.userId === args.userId) {
      userAccess = { hasAccess: true, accessLevel: 'owner' };
    } else {
      // Check project assignments
      const assignment = await ctx.db
        .query("projectAssignments")
        .withIndex("by_project_and_user", (q) =>
          q.eq("projectId", args.projectId).eq("assignedUserId", args.userId)
        )
        .filter((q) => q.eq(q.field("isActive"), true))
        .first();

      if (assignment) {
        userAccess = { hasAccess: true, accessLevel: assignment.accessLevel };
      }
    }

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Return jobData based on access level
    if (userAccess.accessLevel === 'viewer') {
      // Viewers get minimal job data
      return {
        jobDescription: project.jobData?.jobDescription || '',
        photos: project.jobData?.photos || [],
        accessNotes: '', // Hidden from viewers
        equipmentNeeds: '', // Hidden from viewers
        unresolvedQuestions: '', // Hidden from viewers
        userAccessLevel: userAccess.accessLevel,
        projectUpdatedAt: project.updatedAt || project.createdAt,
      };
    }

    // All other roles (owner, administrator, collaborator, subcontractor) get full job data
    console.log('🔍 getJobDataForUser query executed:', {
      projectId: args.projectId,
      userId: args.userId,
      userAccessLevel: userAccess.accessLevel,
      projectUpdatedAt: project.updatedAt || project.createdAt,
      jobData: {
        jobDescription: project.jobData?.jobDescription?.substring(0, 30) + '...',
        equipmentNeeds: project.jobData?.equipmentNeeds?.substring(0, 30) + '...',
        unresolvedQuestions: project.jobData?.unresolvedQuestions?.substring(0, 30) + '...',
        accessNotes: project.jobData?.accessNotes?.substring(0, 30) + '...',
      },
      timestamp: new Date().toISOString()
    });

    return {
      jobDescription: project.jobData?.jobDescription || '',
      photos: project.jobData?.photos || [],
      accessNotes: project.jobData?.accessNotes || '',
      equipmentNeeds: project.jobData?.equipmentNeeds || '',
      unresolvedQuestions: project.jobData?.unresolvedQuestions || '',
      userAccessLevel: userAccess.accessLevel,
      projectUpdatedAt: project.updatedAt || project.createdAt,
    };
  },
});

// Get projects accessible to a user with team support
export const getAccessibleProjectsWithTeamSupport = query({
  args: {
    userId: v.string(),
    includeArchived: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    const includeArchived = args.includeArchived || false;
    const allProjects: any[] = [];

    // Get projects owned by the user
    const ownedProjects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => includeArchived ? q.eq(q.field("isArchived"), true) : q.neq(q.field("isArchived"), true))
      .collect();

    allProjects.push(...ownedProjects);

    // Get projects assigned to the user through team assignments
    const assignments = await ctx.db
      .query("projectAssignments")
      .withIndex("by_assigned_user", (q) => q.eq("assignedUserId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Fetch assigned projects
    for (const assignment of assignments) {
      const project = await ctx.db.get(assignment.projectId);
      if (project && (includeArchived ? project.isArchived : !project.isArchived)) {
        // Get additional data for subcontractor projects
        let mainContractorCompany = null;
        if (assignment.isSubcontractor && assignment.assignedBy) {
          const assigningUser = await ctx.db
            .query("users")
            .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", assignment.assignedBy))
            .first();

          if (assigningUser?.contractorCompanyId) {
            const company = await ctx.db.get(assigningUser.contractorCompanyId);
            mainContractorCompany = company?.name || null;
          }
        }

        // Add assignment info to project
        const projectWithAssignment = {
          ...project,
          assignmentInfo: {
            accessLevel: assignment.accessLevel,
            assignedAt: assignment._creationTime,
            assignedBy: assignment.assignedBy,
          },
          // Invitation-specific fields for subcontractors
          invitationStatus: assignment.invitationStatus,
          assignmentId: assignment._id,
          subcontractorSpecialization: assignment.subcontractorSpecialization,
          mainContractorCompany,
          assignedAt: assignment.assignedAt || assignment._creationTime,
        };
        allProjects.push(projectWithAssignment);
      }
    }

    // Remove duplicates (in case user owns and is assigned to same project)
    const uniqueProjects = allProjects.filter((project, index, self) => 
      index === self.findIndex(p => p._id === project._id)
    );

    // Fetch customer data for each project and apply access filtering
    const projectsWithCustomers = await Promise.all(
      uniqueProjects.map(async (project) => {
        const customer = project.customerId ? await ctx.db.get(project.customerId) : null;

        // Determine user's access level for this project
        const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
          clerkUserId: args.userId,
          projectId: project._id,
        });

        // Filter data based on access level
        const filteredProject = filterProjectDataByAccessLevel(project, userAccess.accessLevel);
        const filteredCustomer = filterCustomerDataByAccessLevel(customer, userAccess.accessLevel);

        return {
          ...filteredProject,
          customer: filteredCustomer,
          userAccessLevel: userAccess.accessLevel,
        };
      })
    );

    return projectsWithCustomers;
  },
});

// Get project by ID with team access validation
export const getByIdWithTeamAccess = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Check if user has access to this project - inline to avoid nested query issues
    // Get the user first
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    // Check direct project ownership
    let userAccess = { hasAccess: false, accessLevel: 'none' };

    if (project.userId === args.userId) {
      userAccess = { hasAccess: true, accessLevel: 'owner' };
    } else {
      // Check project assignments
      const assignment = await ctx.db
        .query("projectAssignments")
        .withIndex("by_project_and_user", (q) =>
          q.eq("projectId", args.projectId).eq("assignedUserId", args.userId)
        )
        .filter((q) => q.eq(q.field("isActive"), true))
        .first();

      if (assignment) {
        userAccess = { hasAccess: true, accessLevel: assignment.accessLevel };
      }
    }

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Fetch customer data
    const customer = project.customerId ? await ctx.db.get(project.customerId) : null;

    // Filter sensitive data based on access level
    const filteredProject = filterProjectDataByAccessLevel(project, userAccess.accessLevel);
    const filteredCustomer = filterCustomerDataByAccessLevel(customer, userAccess.accessLevel);

    // Force explicit dependency on project data to ensure real-time updates
    // This ensures Convex tracks changes to the project document
    const projectJobData = project.jobData;
    const projectUpdatedAt = project.updatedAt || project.createdAt;

    // Debug logging for real-time sync investigation
    console.log('🔍 getByIdWithTeamAccess query executed:', {
      projectId: args.projectId,
      userId: args.userId,
      userAccessLevel: userAccess.accessLevel,
      projectUpdatedAt,
      jobData: {
        equipmentNeeds: projectJobData?.equipmentNeeds?.substring(0, 30) + '...',
        unresolvedQuestions: projectJobData?.unresolvedQuestions?.substring(0, 30) + '...',
        accessNotes: projectJobData?.accessNotes?.substring(0, 30) + '...',
      },
      timestamp: new Date().toISOString()
    });

    return {
      ...filteredProject,
      customer: filteredCustomer,
      userAccessLevel: userAccess.accessLevel,
    };
  },
});

// Archive project with team access control
export const archiveProjectWithTeamAccess = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Check if user has permission to archive this project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess || (userAccess.accessLevel !== 'owner' && userAccess.accessLevel !== 'administrator')) {
      throw new Error("Ingen tillatelse til å arkivere dette prosjektet");
    }

    // Archive the project
    await ctx.db.patch(args.projectId, {
      isArchived: true,
      archivedAt: Date.now(),
      archivedBy: args.userId,
    });

    return { success: true };
  },
});

// Restore project with team access control
export const restoreProjectWithTeamAccess = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Check if user has permission to restore this project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess || (userAccess.accessLevel !== 'owner' && userAccess.accessLevel !== 'administrator')) {
      throw new Error("Ingen tillatelse til å gjenopprette dette prosjektet");
    }

    // Restore the project
    await ctx.db.patch(args.projectId, {
      isArchived: false,
      archivedAt: undefined,
      archivedBy: undefined,
    });

    return { success: true };
  },
});
