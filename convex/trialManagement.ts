import { v } from "convex/values";
import { cronJobs } from "convex/server";
import { internalMutation, internalQuery } from "./_generated/server";
// import { internal } from "./_generated/api"; // TODO: Re-enable when type instantiation issues are resolved

// Set up cron jobs for trial management
const crons = cronJobs();

// Temporary placeholder function for daily trial check
export const dailyTrialCheckPlaceholder = internalMutation({
  args: {},
  handler: async (_ctx, _args) => {
    console.log("⚠️ Daily trial check temporarily disabled due to type issues");
    // TODO: Re-enable actual trial management logic when type issues are resolved
  },
});

// Daily check at 9 AM Norwegian time (7 AM UTC)
crons.daily(
  "trial and payment management",
  { hourUTC: 7, minuteUTC: 0 },
  // internal.trialManagement.dailyTrialCheck // TODO: Re-enable when type instantiation issue is resolved
  "trialManagement:dailyTrialCheckPlaceholder" as any
);

// Temporary placeholder function for weekly cleanup
export const weeklyCleanupPlaceholder = internalMutation({
  args: {},
  handler: async (_ctx, _args) => {
    console.log("⚠️ Weekly cleanup temporarily disabled due to type issues");
    // TODO: Re-enable actual cleanup logic when type issues are resolved
  },
});

// Weekly cleanup of old webhook events and notifications
crons.weekly(
  "cleanup old data",
  { dayOfWeek: "sunday", hourUTC: 2, minuteUTC: 0 },
  // internal.trialManagement.weeklyCleanup // TODO: Re-enable when type instantiation issue is resolved
  "trialManagement:weeklyCleanupPlaceholder" as any
);

// Daily trial check - runs every day at 9 AM Norwegian time
export const dailyTrialCheck = internalMutation({
  handler: async (ctx) => {
    console.log("🔄 Running daily trial check...");
    const now = Date.now();

    // Day 3 reminders (4 days left)
    await sendTrialReminders(ctx, now + (4 * 24 * 60 * 60 * 1000), "trial_reminder_day_3");

    // Day 5 reminders (2 days left)
    await sendTrialReminders(ctx, now + (2 * 24 * 60 * 60 * 1000), "trial_reminder_day_5");

    // 24h reminders (1 day left)
    await sendTrialReminders(ctx, now + (1 * 24 * 60 * 60 * 1000), "trial_reminder_24h");

    // Handle expired trials
    await handleExpiredTrials(ctx, now);

    // Handle grace period expiration
    await handleGracePeriodExpiration(ctx, now);

    console.log("✅ Daily trial check completed");
  },
});

// Send trial reminders for specific time threshold
async function sendTrialReminders(ctx: any, targetTime: number, notificationType: string) {
  const tolerance = 12 * 60 * 60 * 1000; // 12 hours tolerance
  
  // Find trials ending around the target time
  const subscriptions = await ctx.db
    .query("subscriptions")
    .withIndex("by_trial_end")
    .filter((q: any) => 
      q.and(
        q.eq(q.field("status"), "trialing"),
        q.gte(q.field("trialEnd"), targetTime - tolerance),
        q.lte(q.field("trialEnd"), targetTime + tolerance)
      )
    )
    .collect();

  console.log(`📧 Found ${subscriptions.length} trials for ${notificationType}`);

  for (const subscription of subscriptions) {
    // Check if notification already sent
    const existingNotification = await ctx.db
      .query("trialNotifications")
      .withIndex("by_type_and_user", (q: any) => 
        q.eq("type", notificationType).eq("userId", subscription.userId)
      )
      .first();

    if (existingNotification) {
      continue; // Skip if already sent
    }

    // Send notification
    await sendTrialNotification(ctx, subscription.userId, notificationType);
  }
}

// Handle expired trials
async function handleExpiredTrials(ctx: any, now: number) {
  const expiredTrials = await ctx.db
    .query("subscriptions")
    .withIndex("by_status", (q: any) => q.eq("status", "trialing"))
    .filter((q: any) => q.lt(q.field("trialEnd"), now))
    .collect();

  console.log(`⏰ Found ${expiredTrials.length} expired trials`);

  for (const subscription of expiredTrials) {
    // Update subscription status to grace period
    await ctx.db.patch(subscription._id, {
      status: "past_due",
      updatedAt: now,
    });

    // Update user status
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q: any) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        subscriptionStatus: "grace_period",
        hasCompletedTrial: true,
        updatedAt: now,
      });
    }

    // Send trial expired notification
    await sendTrialNotification(ctx, subscription.userId, "trial_expired");
  }
}

// Handle grace period expiration (3 days after trial)
async function handleGracePeriodExpiration(ctx: any, now: number) {
  const gracePeriodEnd = now - (3 * 24 * 60 * 60 * 1000); // 3 days ago

  const expiredGracePeriods = await ctx.db
    .query("subscriptions")
    .withIndex("by_status", (q: any) => q.eq("status", "past_due"))
    .filter((q: any) => q.lt(q.field("trialEnd"), gracePeriodEnd))
    .collect();

  console.log(`🚫 Found ${expiredGracePeriods.length} expired grace periods`);

  for (const subscription of expiredGracePeriods) {
    // Update subscription status to canceled
    await ctx.db.patch(subscription._id, {
      status: "canceled",
      canceledAt: now,
      updatedAt: now,
    });

    // Update user status
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q: any) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        subscriptionStatus: "canceled",
        updatedAt: now,
      });
    }
  }
}

// Send trial notification
async function sendTrialNotification(ctx: any, userId: string, notificationType: string) {
  try {
    // Insert notification record
    await ctx.db.insert("trialNotifications", {
      userId,
      type: notificationType,
      sentAt: Date.now(),
      emailSent: true, // Assume email sending succeeds for now
    });

    // TODO: Send actual email notification
    console.log(`📧 Trial notification sent: ${notificationType} to user ${userId}`);
  } catch (error) {
    console.error(`❌ Failed to send trial notification: ${notificationType} to user ${userId}`, error);
  }
}

// Send trial ending notification (called from webhook)
export const sendTrialEndingNotification = internalMutation({
  args: { stripeSubscriptionId: v.string() },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();

    if (!subscription) {
      console.error("Subscription not found for trial ending notification:", args.stripeSubscriptionId);
      return;
    }

    await sendTrialNotification(ctx, subscription.userId, "trial_reminder_24h");
  },
});

// Weekly cleanup of old data
export const weeklyCleanup = internalMutation({
  handler: async (ctx) => {
    console.log("🧹 Running weekly cleanup...");

    // Clean up old webhook events
    // TODO: Re-enable webhook cleanup when type instantiation issue is resolved
    // await ctx.runMutation(internal.webhooks.cleanupOldWebhookEvents);
    console.log("⚠️ Webhook cleanup temporarily disabled due to type issues");

    // Clean up old trial notifications (older than 90 days)
    const ninetyDaysAgo = Date.now() - (90 * 24 * 60 * 60 * 1000);
    
    const oldNotifications = await ctx.db
      .query("trialNotifications")
      .filter((q: any) => q.lt(q.field("sentAt"), ninetyDaysAgo))
      .collect();

    for (const notification of oldNotifications) {
      await ctx.db.delete(notification._id);
    }

    console.log(`🧹 Cleaned up ${oldNotifications.length} old trial notifications`);
    console.log("✅ Weekly cleanup completed");
  },
});

// Get trial statistics (for admin dashboard)
export const getTrialStatistics = internalQuery({
  handler: async (ctx) => {
    const now = Date.now();
    // const _oneDayAgo = now - (24 * 60 * 60 * 1000); // TODO: Use for daily trial checks
    const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);
    // const _thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000); // TODO: Use for monthly trial checks

    // Active trials
    const activeTrials = await ctx.db
      .query("subscriptions")
      .withIndex("by_status", (q: any) => q.eq("status", "trialing"))
      .collect();

    // Trials expiring in 24 hours
    const expiringTrials = activeTrials.filter(
      (sub: any) => sub.trialEnd && sub.trialEnd <= now + (24 * 60 * 60 * 1000)
    );

    // New trials in last 7 days
    const newTrials = activeTrials.filter(
      (sub: any) => sub.trialStart && sub.trialStart >= sevenDaysAgo
    );

    // Active subscriptions
    const activeSubscriptions = await ctx.db
      .query("subscriptions")
      .withIndex("by_status", (q: any) => q.eq("status", "active"))
      .collect();

    return {
      activeTrials: activeTrials.length,
      expiringTrials: expiringTrials.length,
      newTrials: newTrials.length,
      activeSubscriptions: activeSubscriptions.length,
      conversionRate: activeTrials.length > 0 
        ? Math.round((activeSubscriptions.length / (activeTrials.length + activeSubscriptions.length)) * 100)
        : 0,
    };
  },
});
