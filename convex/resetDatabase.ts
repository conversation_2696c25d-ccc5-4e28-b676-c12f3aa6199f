import { mutation } from './_generated/server';

/**
 * DEVELOPMENT UTILITY: Contractor Company Analysis for Database Reset
 *
 * This module provides specialized functions for analyzing and managing
 * contractor company profiles (bedriftsprofiler) during database reset operations.
 *
 * ⚠️  WARNING: These functions are for development/testing environments only!
 * ⚠️  They provide detailed business data analysis before destructive operations!
 *
 * SAFETY FEATURES:
 * - Read-only analysis functions (no data modification)
 * - Detailed business impact reporting
 * - Contractor company relationship mapping
 * - Data integrity validation
 *
 * USAGE:
 * These functions are called by the reset-database.sh script to provide
 * comprehensive contractor company analysis before database reset operations.
 */

/**
 * Analyze contractor company profiles in the database
 * 
 * Provides a comprehensive overview of all contractor companies,
 * their relationships, and business data without modifying anything.
 */
export const analyzeContractorCompanies = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      console.log("🏢 CONTRACTOR COMPANY ANALYSIS");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      // Get all customers and filter contractor companies
      const allCustomers = await ctx.db.query("customers").collect();
      const contractorCompanies = allCustomers.filter(customer => customer.contractorUserId);
      const regularCustomers = allCustomers.filter(customer => !customer.contractorUserId);

      console.log(`📊 CUSTOMER BREAKDOWN:`);
      console.log(`   • Total customers: ${allCustomers.length}`);
      console.log(`   • Contractor companies: ${contractorCompanies.length}`);
      console.log(`   • Regular customers: ${regularCustomers.length}`);
      console.log("");

      if (contractorCompanies.length === 0) {
        console.log("✅ No contractor companies found in database");
        console.log("ℹ️  Database reset will not affect any business profiles");
        return {
          success: true,
          contractorCompanies: [],
          totalCount: 0,
          message: "No contractor companies found"
        };
      }

      // Get all users to map relationships
      const allUsers = await ctx.db.query("users").collect();
      const userMap = new Map(allUsers.map(user => [user.clerkUserId, user]));

      console.log(`🏢 CONTRACTOR COMPANY DETAILS:`);
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      const companyAnalysis = [];

      for (const company of contractorCompanies) {
        const user = userMap.get(company.contractorUserId || '');
        const onboardingStatus = user?.contractorCompleted ? 'Completed' : 'Pending';
        
        console.log(`📋 Company: ${company.name}`);
        console.log(`   • Org Number: ${company.orgNumber || 'Not set'}`);
        console.log(`   • Contact Person: ${company.contactPerson || 'Not set'}`);
        console.log(`   • Phone: ${company.phone || 'Not set'}`);
        console.log(`   • Email: ${company.email || 'Not set'}`);
        console.log(`   • Address: ${company.streetAddress ? `${company.streetAddress}, ${company.postalCode} ${company.city}` : 'Not set'}`);
        console.log(`   • Contractor User ID: ${company.contractorUserId}`);
        console.log(`   • Onboarding Status: ${onboardingStatus}`);
        console.log(`   • Created: ${company.createdAt ? new Date(company.createdAt).toLocaleString('nb-NO') : 'Unknown'}`);
        console.log(`   • Last Updated: ${new Date(company._creationTime).toLocaleString('nb-NO')}`);
        
        // Check for Brønnøysundregisteret data
        if (company.brregData) {
          console.log(`   • Brreg Data: Available (last fetched: ${company.brregFetchedAt ? new Date(company.brregFetchedAt).toLocaleString('nb-NO') : 'Unknown'})`);
        } else {
          console.log(`   • Brreg Data: Not available`);
        }
        
        console.log("");

        companyAnalysis.push({
          id: company._id,
          name: company.name,
          orgNumber: company.orgNumber,
          contractorUserId: company.contractorUserId,
          onboardingStatus,
          hasBreregData: !!company.brregData,
          createdAt: company.createdAt,
          lastModified: company._creationTime
        });
      }

      // Check for projects associated with contractor companies
      const allProjects = await ctx.db.query("projects").collect();
      const contractorProjects = allProjects.filter(project => 
        contractorCompanies.some(company => company._id === project.customerId)
      );

      console.log(`📋 PROJECT RELATIONSHIPS:`);
      console.log(`   • Total projects: ${allProjects.length}`);
      console.log(`   • Projects linked to contractor companies: ${contractorProjects.length}`);
      console.log(`   • Regular customer projects: ${allProjects.length - contractorProjects.length}`);
      console.log("");

      // Data integrity checks
      console.log(`🔍 DATA INTEGRITY CHECKS:`);
      
      // Check for orphaned contractor user references
      const orphanedCompanies = contractorCompanies.filter(company => 
        !userMap.has(company.contractorUserId || '')
      );
      
      if (orphanedCompanies.length > 0) {
        console.log(`⚠️  Found ${orphanedCompanies.length} contractor companies with missing user records:`);
        orphanedCompanies.forEach(company => {
          console.log(`   • ${company.name} (User ID: ${company.contractorUserId})`);
        });
      } else {
        console.log(`✅ All contractor companies have valid user references`);
      }

      // Check for users with missing contractor companies
      const usersWithContractorCompanyId = allUsers.filter(user => user.contractorCompanyId);
      const orphanedUsers = usersWithContractorCompanyId.filter(user => 
        !contractorCompanies.some(company => company._id === user.contractorCompanyId)
      );

      if (orphanedUsers.length > 0) {
        console.log(`⚠️  Found ${orphanedUsers.length} users with missing contractor company references:`);
        orphanedUsers.forEach(user => {
          console.log(`   • User ID: ${user.clerkUserId} (Company ID: ${user.contractorCompanyId})`);
        });
      } else {
        console.log(`✅ All users have valid contractor company references`);
      }

      console.log("");
      console.log(`⚠️  DELETION IMPACT:`);
      console.log(`   • ${contractorCompanies.length} contractor company profiles will be permanently deleted`);
      console.log(`   • ${contractorProjects.length} projects linked to contractor companies will be deleted`);
      console.log(`   • ${allUsers.length} user records (including contractor onboarding data) will be deleted`);
      console.log(`   • Users will need to complete contractor onboarding again after reset`);
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      return {
        success: true,
        contractorCompanies: companyAnalysis,
        totalCount: contractorCompanies.length,
        regularCustomers: regularCustomers.length,
        linkedProjects: contractorProjects.length,
        orphanedCompanies: orphanedCompanies.length,
        orphanedUsers: orphanedUsers.length,
        message: `Found ${contractorCompanies.length} contractor companies`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ ERROR ANALYZING CONTRACTOR COMPANIES:", errorMessage);
      throw new Error(`Failed to analyze contractor companies: ${errorMessage}`);
    }
  }
});

/**
 * Get detailed contractor company information
 * 
 * Provides comprehensive business details for all contractor companies,
 * including full contact information, addresses, and Brønnøysundregisteret data.
 */
export const getContractorCompanyDetails = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      console.log("🔍 DETAILED CONTRACTOR COMPANY INFORMATION");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      // Get all contractor companies
      const allCustomers = await ctx.db.query("customers").collect();
      const contractorCompanies = allCustomers.filter(customer => customer.contractorUserId);

      if (contractorCompanies.length === 0) {
        console.log("ℹ️  No contractor companies found in database");
        return {
          success: true,
          companies: [],
          message: "No contractor companies found"
        };
      }

      // Get all users for relationship mapping
      const allUsers = await ctx.db.query("users").collect();
      const userMap = new Map(allUsers.map(user => [user.clerkUserId, user]));

      const detailedCompanies = [];

      for (let i = 0; i < contractorCompanies.length; i++) {
        const company = contractorCompanies[i];
        const user = userMap.get(company.contractorUserId || '');

        console.log(`🏢 COMPANY ${i + 1}/${contractorCompanies.length}: ${company.name}`);
        console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        
        // Basic company information
        console.log("📋 BASIC INFORMATION:");
        console.log(`   • Company Name: ${company.name}`);
        console.log(`   • Organization Number: ${company.orgNumber || 'Not set'}`);
        console.log(`   • Contact Person: ${company.contactPerson || 'Not set'}`);
        console.log(`   • Phone: ${company.phone || 'Not set'}`);
        console.log(`   • Email: ${company.email || 'Not set'}`);
        console.log("");

        // Address information
        console.log("📍 ADDRESS INFORMATION:");
        if (company.streetAddress || company.postalCode || company.city) {
          console.log(`   • Street Address: ${company.streetAddress || 'Not set'}`);
          console.log(`   • Postal Code: ${company.postalCode || 'Not set'}`);
          console.log(`   • City: ${company.city || 'Not set'}`);
          console.log(`   • Entrance/Floor: ${company.entrance || 'Not set'}`);
          console.log(`   • Custom Address: ${company.useCustomAddress ? 'Yes' : 'No'}`);
        } else {
          console.log("   • No address information available");
        }
        console.log("");

        // User relationship
        console.log("👤 USER RELATIONSHIP:");
        console.log(`   • Contractor User ID: ${company.contractorUserId}`);
        if (user) {
          console.log(`   • Onboarding Completed: ${user.contractorCompleted ? 'Yes' : 'No'}`);
          console.log(`   • User Created: ${user.createdAt ? new Date(user.createdAt).toLocaleString('nb-NO') : 'Unknown'}`);
        } else {
          console.log("   • ⚠️  User record not found (orphaned company)");
        }
        console.log("");

        // Brønnøysundregisteret data
        console.log("🏛️  BRØNNØYSUNDREGISTERET DATA:");
        if (company.brregData) {
          console.log(`   • Data Available: Yes`);
          console.log(`   • Last Fetched: ${company.brregFetchedAt ? new Date(company.brregFetchedAt).toLocaleString('nb-NO') : 'Unknown'}`);
          console.log(`   • Organization Form: ${company.brregData.organizationForm || 'Not available'}`);
          console.log(`   • Industry Code: ${company.brregData.naeringskode1 || 'Not available'}`);
          console.log(`   • Establishment Date: ${company.brregData.establishmentDate || 'Not available'}`);
          console.log(`   • Number of Employees: ${company.brregData.numberOfEmployees || 'Not available'}`);
          
          if (company.brregData.managingDirector) {
            const director = typeof company.brregData.managingDirector === 'string' 
              ? company.brregData.managingDirector 
              : company.brregData.managingDirector.fullName || 'Not available';
            console.log(`   • Managing Director: ${director}`);
          }
        } else {
          console.log("   • No Brønnøysundregisteret data available");
        }
        console.log("");

        // Timestamps
        console.log("⏰ TIMESTAMPS:");
        console.log(`   • Created: ${company.createdAt ? new Date(company.createdAt).toLocaleString('nb-NO') : 'Unknown'}`);
        console.log(`   • Last Modified: ${new Date(company._creationTime).toLocaleString('nb-NO')}`);
        console.log("");

        // Notes
        if (company.notes) {
          console.log("📝 NOTES:");
          console.log(`   ${company.notes}`);
          console.log("");
        }

        detailedCompanies.push({
          id: company._id,
          name: company.name,
          orgNumber: company.orgNumber,
          contactPerson: company.contactPerson,
          phone: company.phone,
          email: company.email,
          address: {
            street: company.streetAddress,
            postalCode: company.postalCode,
            city: company.city,
            entrance: company.entrance,
            useCustom: company.useCustomAddress
          },
          contractorUserId: company.contractorUserId,
          userExists: !!user,
          onboardingCompleted: user?.contractorCompleted || false,
          hasBreregData: !!company.brregData,
          brregLastFetched: company.brregFetchedAt,
          createdAt: company.createdAt,
          lastModified: company._creationTime,
          notes: company.notes
        });

        if (i < contractorCompanies.length - 1) {
          console.log(""); // Add spacing between companies
        }
      }

      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log(`📊 SUMMARY: ${contractorCompanies.length} contractor companies analyzed`);

      return {
        success: true,
        companies: detailedCompanies,
        totalCount: contractorCompanies.length,
        message: `Detailed analysis of ${contractorCompanies.length} contractor companies completed`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ ERROR GETTING CONTRACTOR COMPANY DETAILS:", errorMessage);
      throw new Error(`Failed to get contractor company details: ${errorMessage}`);
    }
  }
});

/**
 * Validate contractor company data integrity
 * 
 * Checks for data consistency issues, orphaned records, and relationship problems
 * that could affect database reset operations.
 */
export const validateContractorCompanyIntegrity = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      console.log("🔍 CONTRACTOR COMPANY DATA INTEGRITY VALIDATION");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      const issues = [];
      const warnings = [];

      // Get all relevant data
      const allCustomers = await ctx.db.query("customers").collect();
      const contractorCompanies = allCustomers.filter(customer => customer.contractorUserId);
      const allUsers = await ctx.db.query("users").collect();
      const allProjects = await ctx.db.query("projects").collect();

      console.log("🔍 Checking contractor company relationships...");

      // Check 1: Contractor companies without user records
      const orphanedCompanies = contractorCompanies.filter(company => 
        !allUsers.some(user => user.clerkUserId === company.contractorUserId)
      );

      if (orphanedCompanies.length > 0) {
        issues.push(`Found ${orphanedCompanies.length} contractor companies without user records`);
        orphanedCompanies.forEach(company => {
          console.log(`❌ Orphaned company: ${company.name} (User ID: ${company.contractorUserId})`);
        });
      }

      // Check 2: Users with contractor company references that don't exist
      const usersWithCompanyRefs = allUsers.filter(user => user.contractorCompanyId);
      const orphanedUsers = usersWithCompanyRefs.filter(user => 
        !contractorCompanies.some(company => company._id === user.contractorCompanyId)
      );

      if (orphanedUsers.length > 0) {
        issues.push(`Found ${orphanedUsers.length} users with invalid contractor company references`);
        orphanedUsers.forEach(user => {
          console.log(`❌ Orphaned user: ${user.clerkUserId} (Company ID: ${user.contractorCompanyId})`);
        });
      }

      // Check 3: Duplicate organization numbers
      const orgNumbers = contractorCompanies
        .filter(company => company.orgNumber)
        .map(company => company.orgNumber);
      const duplicateOrgNumbers = orgNumbers.filter((num, index) => orgNumbers.indexOf(num) !== index);

      if (duplicateOrgNumbers.length > 0) {
        issues.push(`Found duplicate organization numbers: ${[...new Set(duplicateOrgNumbers)].join(', ')}`);
      }

      // Check 4: Missing required fields
      const companiesWithMissingData = contractorCompanies.filter(company => 
        !company.name || !company.contactPerson || !company.orgNumber
      );

      if (companiesWithMissingData.length > 0) {
        warnings.push(`Found ${companiesWithMissingData.length} companies with missing required fields`);
      }

      // Check 5: Projects linked to contractor companies
      const contractorProjects = allProjects.filter(project => 
        contractorCompanies.some(company => company._id === project.customerId)
      );

      console.log("");
      console.log("📊 VALIDATION RESULTS:");
      console.log(`   • Total contractor companies: ${contractorCompanies.length}`);
      console.log(`   • Orphaned companies: ${orphanedCompanies.length}`);
      console.log(`   • Orphaned users: ${orphanedUsers.length}`);
      console.log(`   • Companies with missing data: ${companiesWithMissingData.length}`);
      console.log(`   • Projects linked to contractors: ${contractorProjects.length}`);
      console.log(`   • Duplicate org numbers: ${duplicateOrgNumbers.length}`);

      if (issues.length === 0 && warnings.length === 0) {
        console.log("✅ No data integrity issues found");
      } else {
        if (issues.length > 0) {
          console.log("");
          console.log("❌ CRITICAL ISSUES:");
          issues.forEach(issue => console.log(`   • ${issue}`));
        }
        if (warnings.length > 0) {
          console.log("");
          console.log("⚠️  WARNINGS:");
          warnings.forEach(warning => console.log(`   • ${warning}`));
        }
      }

      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      return {
        success: true,
        validation: {
          totalCompanies: contractorCompanies.length,
          orphanedCompanies: orphanedCompanies.length,
          orphanedUsers: orphanedUsers.length,
          companiesWithMissingData: companiesWithMissingData.length,
          linkedProjects: contractorProjects.length,
          duplicateOrgNumbers: duplicateOrgNumbers.length,
          issues,
          warnings
        },
        isHealthy: issues.length === 0,
        message: issues.length === 0 
          ? "Data integrity validation passed" 
          : `Found ${issues.length} critical issues and ${warnings.length} warnings`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ ERROR VALIDATING DATA INTEGRITY:", errorMessage);
      throw new Error(`Failed to validate contractor company integrity: ${errorMessage}`);
    }
  }
});
