# JobbLogg Staging-Only Compose
# This file contains ONLY the services needed for staging deployment
# Use: docker-compose -f docker-compose.staging-only.yml up -d

version: '3.8'

networks:
  jobblogg-staging-network:
    driver: bridge

services:
  # Frontend Service - Staging Production Build
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
      target: production
      args:
        - VITE_CONVEX_URL=${VITE_CONVEX_URL}
        - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
        - VITE_GOOGLE_MAPS_API_KEY=${VITE_GOOGLE_MAPS_API_KEY}
        - VITE_STRIPE_PUBLISHABLE_KEY=${VITE_STRIPE_PUBLISHABLE_KEY}
    container_name: jobblogg-frontend-staging
    environment:
      - NODE_ENV=staging
      - COMPOSE_ENV=staging
      - BUILD_TARGET=production
      - VITE_ALLOW_INDEXING=false
    ports:
      - "5175:5173"  # Staging frontend port
    env_file:
      - .env.staging
    networks:
      - jobblogg-staging-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sh", "-c", "wget --quiet --tries=1 --spider http://localhost:5173/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Health Check Service
  healthcheck:
    image: curlimages/curl:latest
    container_name: jobblogg-healthcheck-staging
    command: >
      sh -c "
        echo 'Starting health check service for staging...';
        while true; do
          echo 'Checking frontend health...';
          if curl -f http://frontend:5173/ >/dev/null 2>&1; then
            echo 'Frontend is healthy';
          else
            echo 'Frontend is not responding';
          fi;
          sleep 30;
        done
      "
    depends_on:
      frontend:
        condition: service_healthy
    networks:
      - jobblogg-staging-network
    restart: unless-stopped
