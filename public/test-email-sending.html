<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email Sending - JobbLogg</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
        }
        button {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            transform: translateY(-1px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #bfdbfe;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Email Sending</h1>
        <p>Test om Resend email-service fungerer med JobbLogg.</p>
        
        <div class="form-group">
            <label for="email">Test Email:</label>
            <input type="email" id="email" placeholder="<EMAIL>" value="<EMAIL>">
        </div>
        
        <button onclick="sendTestEmail()" id="testBtn">Send Test Email</button>
        <button onclick="sendInvitationEmail()" id="inviteBtn">Send Invitation Email</button>
        
        <div id="result"></div>
    </div>

    <script type="module">
        import { ConvexHttpClient } from "https://esm.sh/convex@1.16.4/browser";

        const convex = new ConvexHttpClient("https://enchanted-quail-174.convex.cloud");

        window.sendTestEmail = async function() {
            const email = document.getElementById('email').value;
            const resultDiv = document.getElementById('result');
            const btn = document.getElementById('testBtn');
            
            if (!email) {
                showResult('Vennligst fyll inn en email-adresse', 'error');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Sender...';
            showResult('Sender test-email...', 'info');
            
            try {
                const result = await convex.action("emails:sendTestEmail", { to: email });
                
                if (result.success) {
                    showResult(`✅ Test-email sendt til ${email}! Email ID: ${result.emailId}`, 'success');
                } else {
                    showResult(`❌ Feil: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Feil: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Send Test Email';
            }
        };

        window.sendInvitationEmail = async function() {
            const email = document.getElementById('email').value;
            const resultDiv = document.getElementById('result');
            const btn = document.getElementById('inviteBtn');
            
            if (!email) {
                showResult('Vennligst fyll inn en email-adresse', 'error');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Sender...';
            showResult('Sender invitasjons-email...', 'info');
            
            try {
                const result = await convex.action("emails:sendTeamInvitationEmail", {
                    to: email,
                    invitedByName: "Test Administrator",
                    inviterEmail: "<EMAIL>",
                    companyName: "Test Bedrift AS",
                    invitationLink: "http://localhost:5173/team-onboarding?token=test123",
                    expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000),
                    role: "utfoerende"
                });
                
                if (result.success) {
                    showResult(`✅ Invitasjons-email sendt til ${email}! Email ID: ${result.emailId}`, 'success');
                } else {
                    showResult(`❌ Feil: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Feil: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Send Invitation Email';
            }
        };

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>
