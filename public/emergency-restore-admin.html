<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 Emergency: <PERSON>je<PERSON><PERSON>rett Administrator</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
            color: white;
            padding: 32px 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .content {
            padding: 32px 24px;
        }
        .alert {
            background: #FEF2F2;
            border: 2px solid #FECACA;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        .alert-title {
            font-weight: 700;
            color: #DC2626;
            margin-bottom: 8px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #DC2626;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }
        .btn {
            background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 15px rgba(220, 38, 38, 0.3);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.success {
            background: #F0FDF4;
            color: #166534;
            border: 2px solid #BBF7D0;
        }
        .result.error {
            background: #FEF2F2;
            color: #DC2626;
            border: 2px solid #FECACA;
        }
        .result.info {
            background: #EFF6FF;
            color: #1D4ED8;
            border: 2px solid #BFDBFE;
        }
        .steps {
            background: #F9FAFB;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }
        .steps h3 {
            margin-top: 0;
            color: #374151;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin-bottom: 8px;
            color: #6B7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 Emergency Fix</h1>
            <p>Gjenopprett Administrator-rolle</p>
        </div>
        
        <div class="content">
            <div class="alert">
                <div class="alert-title">⚠️ Kritisk Problem Oppdaget</div>
                <p>Din administrator-rolle ble overskrevet med "Utførende" rolle når du aksepterte team-invitasjonen. Dette må fikses umiddelbart.</p>
            </div>

            <div class="steps">
                <h3>Hva skjedde:</h3>
                <ol>
                    <li>Team-invitasjon ble sendt til feil email (<EMAIL>)</li>
                    <li>Du aksepterte invitasjonen mens du var logget <NAME_EMAIL></li>
                    <li>Systemet overskrev din administrator-rolle med "Utførende"</li>
                    <li>Nå må vi gjenopprette din administrator-rolle</li>
                </ol>
            </div>
            
            <div class="form-group">
                <label for="clerkUserId">Din Clerk User ID:</label>
                <input type="text" id="clerkUserId" placeholder="Clerk User ID vil bli fylt ut automatisk" readonly>
            </div>
            
            <button onclick="restoreAdminRole()" id="restoreBtn" class="btn">
                🔧 Gjenopprett Administrator-rolle
            </button>
            
            <div id="result"></div>
        </div>
    </div>

    <script type="module">
        import { ConvexHttpClient } from "https://esm.sh/convex@1.16.4/browser";

        const convex = new ConvexHttpClient("https://enchanted-quail-174.convex.cloud");

        // Auto-detect Clerk User ID from localStorage or prompt user
        window.addEventListener('load', function() {
            // Try to get Clerk user ID from various sources
            let clerkUserId = null;
            
            // Method 1: Check if Clerk is available
            if (window.Clerk && window.Clerk.user) {
                clerkUserId = window.Clerk.user.id;
            }
            
            // Method 2: Check localStorage for Clerk session
            try {
                const clerkSession = localStorage.getItem('__clerk_session');
                if (clerkSession) {
                    // This is a simplified approach - in reality Clerk stores complex session data
                    console.log('Found Clerk session data');
                }
            } catch (e) {
                console.log('Could not access localStorage');
            }
            
            // Method 3: Prompt user if we can't auto-detect
            if (!clerkUserId) {
                clerkUserId = prompt('Vennligst oppgi din Clerk User ID (finner du i browser console ved å skrive: window.Clerk?.user?.id)');
            }
            
            if (clerkUserId) {
                document.getElementById('clerkUserId').value = clerkUserId;
                showResult('Clerk User ID funnet: ' + clerkUserId, 'info');
            } else {
                showResult('⚠️ Kunne ikke finne Clerk User ID automatisk. Vennligst oppgi den manuelt.', 'error');
                document.getElementById('clerkUserId').readOnly = false;
                document.getElementById('clerkUserId').placeholder = 'Skriv inn din Clerk User ID her';
            }
        });

        window.restoreAdminRole = async function() {
            const clerkUserId = document.getElementById('clerkUserId').value;
            const btn = document.getElementById('restoreBtn');
            
            if (!clerkUserId) {
                showResult('❌ Vennligst oppgi Clerk User ID', 'error');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '🔧 Gjenoppretter...';
            showResult('Gjenoppretter administrator-rolle...', 'info');
            
            try {
                const result = await convex.mutation("teamManagement:restoreAdministratorRole", {
                    clerkUserId: clerkUserId
                });
                
                if (result.success) {
                    showResult(`✅ Administrator-rolle gjenopprettet! ${result.message}`, 'success');
                    setTimeout(() => {
                        showResult('🎉 Du kan nå gå tilbake til JobbLogg. Din administrator-rolle er gjenopprettet!', 'success');
                    }, 2000);
                } else {
                    showResult(`❌ Feil: ${result.message || 'Ukjent feil'}`, 'error');
                }
            } catch (error) {
                console.error('Error restoring admin role:', error);
                showResult(`❌ Feil: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔧 Gjenopprett Administrator-rolle';
            }
        };

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>
