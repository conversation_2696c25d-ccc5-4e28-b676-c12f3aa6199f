<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Invitation Flow - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        .test-section {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .success-box {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .error-box {
            background: #fee2e2;
            border: 1px solid #dc2626;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
        button.danger {
            background: #dc2626;
        }
        button.danger:hover {
            background: #b91c1c;
        }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #e5e7eb;
            background: #f9fafb;
        }
        .step.active {
            border-left-color: #2563eb;
            background: #eff6ff;
        }
        .step.completed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }
        .step.error {
            border-left-color: #dc2626;
            background: #fef2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Invitation Flow</h1>
        
        <div class="info-box">
            <strong>Hva tester vi?</strong><br>
            Denne siden hjelper med å teste invitation flow og finne ut hvorfor 
            inviterte teammedlemmer blir rutet til contractor onboarding.
        </div>

        <div class="warning-box">
            <strong>⚠️ Viktig:</strong><br>
            • Åpne Developer Tools (F12) og gå til Console tab<br>
            • Se etter debug meldinger fra onboarding guard<br>
            • Test med en ekte invitation link
        </div>

        <div class="test-section">
            <h3>🔍 Debug Current User Status</h3>
            <button onclick="checkCurrentUser()">Sjekk nåværende bruker</button>
            <button onclick="checkOnboardingStatus()">Sjekk onboarding status</button>
            <button onclick="checkLocalStorage()">Sjekk localStorage</button>
            <div id="userStatus"></div>
        </div>

        <div class="test-section">
            <h3>🧹 Reset User State</h3>
            <p>Bruk dette for å teste invitation flow på nytt:</p>
            <button onclick="clearLocalStorage()" class="secondary">Clear localStorage</button>
            <button onclick="simulateInvitedUser()" class="secondary">Simuler invitert bruker</button>
            <button onclick="logOut()" class="danger">Logg ut</button>
        </div>

        <div class="test-section">
            <h3>📋 Console Output</h3>
            <button onclick="clearConsole()" class="secondary">Clear Console</button>
            <div id="console" class="console-output">Console output vil vises her...</div>
        </div>

        <div class="test-section">
            <h3>🔗 Test Links</h3>
            <button onclick="goToMain()">Gå til hovedside (/)</button>
            <button onclick="goToTeam()">Gå til team management</button>
            <button onclick="goToOnboarding()">Gå til contractor onboarding</button>
        </div>

        <div class="info-box" style="margin-top: 30px;">
            <strong>💡 Debugging Tips:</strong><br>
            1. Se etter "🔍 Onboarding Guard" meldinger i console<br>
            2. Sjekk om contractorCompleted er true i database<br>
            3. Sjekk om localStorage har riktig verdi<br>
            4. Test med en ekte invitation link
        </div>
    </div>

    <script>
        // Console capture
        const consoleElement = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('no-NO');
            const colors = {
                log: '#10b981',
                error: '#dc2626', 
                warn: '#f59e0b',
                info: '#3b82f6'
            };
            const color = colors[type] || colors.log;
            
            consoleElement.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            consoleElement.innerHTML = 'Console cleared...\n';
        }

        async function checkCurrentUser() {
            try {
                if (window.Clerk && window.Clerk.user) {
                    const user = window.Clerk.user;
                    console.log('👤 Current user:', {
                        id: user.id,
                        email: user.emailAddresses[0]?.emailAddress,
                        firstName: user.firstName,
                        lastName: user.lastName
                    });
                    
                    document.getElementById('userStatus').innerHTML = `
                        <div class="success-box">
                            <strong>Bruker logget inn:</strong><br>
                            ID: ${user.id}<br>
                            Email: ${user.emailAddresses[0]?.emailAddress}<br>
                            Navn: ${user.firstName} ${user.lastName}
                        </div>
                    `;
                } else {
                    console.log('❌ Ingen bruker logget inn');
                    document.getElementById('userStatus').innerHTML = `
                        <div class="error-box">Ingen bruker logget inn</div>
                    `;
                }
            } catch (error) {
                console.error('Feil ved sjekking av bruker:', error);
            }
        }

        function checkOnboardingStatus() {
            console.log('🔍 Sjekker onboarding status...');
            console.log('Se etter "🔍 Onboarding Guard" meldinger i console når du navigerer til hovedsiden');
        }

        function checkLocalStorage() {
            if (window.Clerk && window.Clerk.user) {
                const userId = window.Clerk.user.id;
                const storageKey = `jobblogg-contractor-completed-${userId}`;
                const value = localStorage.getItem(storageKey);
                
                console.log('💾 localStorage check:', {
                    key: storageKey,
                    value: value,
                    isCompleted: value === 'true'
                });
                
                document.getElementById('userStatus').innerHTML = `
                    <div class="${value === 'true' ? 'success' : 'error'}-box">
                        <strong>localStorage Status:</strong><br>
                        Key: ${storageKey}<br>
                        Value: ${value || 'null'}<br>
                        Is Completed: ${value === 'true'}
                    </div>
                `;
            } else {
                console.log('❌ Ingen bruker logget inn for localStorage sjekk');
            }
        }

        function clearLocalStorage() {
            if (window.Clerk && window.Clerk.user) {
                const userId = window.Clerk.user.id;
                const storageKey = `jobblogg-contractor-completed-${userId}`;
                localStorage.removeItem(storageKey);
                console.log('🧹 localStorage cleared for:', storageKey);
            }
            
            // Also clear invitation token
            localStorage.removeItem('jobblogg-invitation-token');
            console.log('🧹 Invitation token cleared');
        }

        function simulateInvitedUser() {
            if (window.Clerk && window.Clerk.user) {
                const userId = window.Clerk.user.id;
                const storageKey = `jobblogg-contractor-completed-${userId}`;
                localStorage.setItem(storageKey, 'true');
                console.log('✅ Simulert invitert bruker - localStorage satt til true');
            }
        }

        function logOut() {
            if (window.Clerk) {
                window.Clerk.signOut();
                console.log('👋 Bruker logget ut');
            }
        }

        function goToMain() {
            console.log('🏠 Navigerer til hovedside - se etter onboarding guard meldinger');
            window.location.href = '/';
        }

        function goToTeam() {
            console.log('👥 Navigerer til team management');
            window.location.href = '/team';
        }

        function goToOnboarding() {
            console.log('🚀 Navigerer til contractor onboarding');
            window.location.href = '/contractor-onboarding';
        }

        // Initialize
        window.addEventListener('load', () => {
            console.log('🧪 Test Invitation Flow page loaded');
            console.log('💡 Bruk knappene for å teste invitation flow');
        });
    </script>
</body>
</html>
