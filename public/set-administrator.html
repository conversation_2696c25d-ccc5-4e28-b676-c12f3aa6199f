<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Sett Administrator-rolle</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
            color: white;
            padding: 32px 24px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .content {
            padding: 32px 24px;
        }
        .info-box {
            background: #EFF6FF;
            border: 2px solid #BFDBFE;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        .info-title {
            font-weight: 700;
            color: #1D4ED8;
            margin-bottom: 8px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #2563EB;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .btn {
            background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 15px rgba(37, 99, 235, 0.3);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.success {
            background: #F0FDF4;
            color: #166534;
            border: 2px solid #BBF7D0;
        }
        .result.error {
            background: #FEF2F2;
            color: #DC2626;
            border: 2px solid #FECACA;
        }
        .result.info {
            background: #EFF6FF;
            color: #1D4ED8;
            border: 2px solid #BFDBFE;
        }
        .steps {
            background: #F9FAFB;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }
        .steps h3 {
            margin-top: 0;
            color: #374151;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin-bottom: 8px;
            color: #6B7280;
        }
        .current-user {
            background: #F0FDF4;
            border: 2px solid #BBF7D0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        .current-user h4 {
            margin: 0 0 8px 0;
            color: #166534;
        }
        .user-info {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Administrator Setup</h1>
            <p>Sett deg som administrator for å få tilgang til team-funksjoner</p>
        </div>
        
        <div class="content">
            <div class="info-box">
                <div class="info-title">ℹ️ Hvorfor trenger du dette?</div>
                <p>Etter at du slettet databasen og registrerte deg på nytt, mangler du administrator-rollen. Denne siden setter deg som administrator så du kan se "Team administrasjon" menyen.</p>
            </div>

            <div id="currentUser" class="current-user" style="display: none;">
                <h4>✅ Innlogget bruker:</h4>
                <div class="user-info" id="userInfo"></div>
            </div>
            
            <div class="form-group">
                <label for="email">Din email-adresse:</label>
                <input type="email" id="email" placeholder="<EMAIL>" value="<EMAIL>">
            </div>

            <button onclick="setAsAdministrator()" id="adminBtn" class="btn">
                👑 Sett meg som Administrator
            </button>
            
            <div id="result"></div>

            <div class="steps">
                <h3>Etter at du er satt som administrator:</h3>
                <ol>
                    <li>Gå tilbake til JobbLogg dashboard</li>
                    <li>Du vil nå se "Team administrasjon" i menyen</li>
                    <li>Du kan invitere teammedlemmer</li>
                    <li>Du kan administrere roller og tilganger</li>
                </ol>
            </div>
        </div>
    </div>

    <script type="module">
        import { ConvexHttpClient } from "https://esm.sh/convex@1.16.4/browser";

        const convex = new ConvexHttpClient("https://enchanted-quail-174.convex.cloud");

        // Auto-detect current user
        window.addEventListener('load', function() {
            // Try to get user info from parent window (if opened from JobbLogg)
            try {
                if (window.opener && window.opener.Clerk && window.opener.Clerk.user) {
                    const user = window.opener.Clerk.user;
                    displayCurrentUser(user);
                    document.getElementById('clerkUserId').value = user.id;
                    return;
                }
            } catch (e) {
                console.log('Could not access parent window Clerk');
            }

            // Try to get from current window
            if (window.Clerk && window.Clerk.user) {
                const user = window.Clerk.user;
                displayCurrentUser(user);
                document.getElementById('clerkUserId').value = user.id;
                return;
            }

            // If no Clerk found, show instructions
            showResult('⚠️ Kunne ikke finne Clerk bruker automatisk. Åpne denne siden fra JobbLogg dashboard eller oppgi Clerk User ID manuelt.', 'error');
            document.getElementById('clerkUserId').readOnly = false;
            document.getElementById('clerkUserId').placeholder = 'Skriv inn din Clerk User ID';
        });

        function displayCurrentUser(user) {
            const currentUserDiv = document.getElementById('currentUser');
            const userInfoDiv = document.getElementById('userInfo');
            
            userInfoDiv.innerHTML = `
                <strong>Email:</strong> ${user.primaryEmailAddress?.emailAddress || 'Ikke tilgjengelig'}<br>
                <strong>Navn:</strong> ${user.fullName || user.firstName || 'Ikke satt'}<br>
                <strong>User ID:</strong> ${user.id}
            `;
            
            currentUserDiv.style.display = 'block';
        }

        window.setAsAdministrator = async function() {
            const email = document.getElementById('email').value;
            const btn = document.getElementById('adminBtn');

            if (!email) {
                showResult('❌ Vennligst oppgi email-adresse', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '👑 Setter administrator-rolle...';
            showResult('Setter deg som administrator...', 'info');

            try {
                const result = await convex.mutation("teamManagement:setUserAsAdministrator", {
                    email: email
                });

                if (result.success) {
                    showResult(`✅ Suksess! ${result.message}`, 'success');
                    setTimeout(() => {
                        showResult('🎉 Du er nå administrator! Gå tilbake til JobbLogg dashboard for å se "Team administrasjon" menyen.', 'success');
                    }, 2000);
                } else {
                    showResult(`❌ Feil: ${result.message || 'Ukjent feil'}`, 'error');
                }
            } catch (error) {
                console.error('Error setting administrator:', error);
                showResult(`❌ Feil: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '👑 Sett meg som Administrator';
            }
        };

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
    </script>
</body>
</html>
