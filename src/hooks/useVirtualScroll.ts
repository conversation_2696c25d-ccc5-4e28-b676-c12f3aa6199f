import { useState, useEffect, useCallback, useRef } from 'react';

export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // Number of items to render outside visible area
}

export interface VirtualScrollResult {
  startIndex: number;
  endIndex: number;
  totalHeight: number;
  offsetY: number;
  visibleItems: number;
}

/**
 * Custom hook for virtual scrolling to improve performance with large lists
 */
export function useVirtualScroll<T>(
  items: T[],
  options: VirtualScrollOptions
): VirtualScrollResult & {
  scrollElementRef: React.RefObject<HTMLDivElement | null>;
  handleScroll: (event: React.UIEvent<HTMLDivElement>) => void;
} {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement | null>(null);

  const totalHeight = items.length * itemHeight;
  const visibleItems = Math.ceil(containerHeight / itemHeight);
  
  // Calculate visible range
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  // Auto-scroll to bottom when new items are added (chat behavior)
  const prevItemsLength = useRef(items.length);
  useEffect(() => {
    if (items.length > prevItemsLength.current && scrollElementRef.current) {
      const element = scrollElementRef.current;
      const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - 100;
      
      if (isNearBottom) {
        // Scroll to bottom when new messages arrive
        setTimeout(() => {
          element.scrollTop = element.scrollHeight;
        }, 0);
      }
    }
    prevItemsLength.current = items.length;
  }, [items.length]);

  return {
    startIndex,
    endIndex,
    totalHeight,
    offsetY,
    visibleItems,
    scrollElementRef,
    handleScroll
  };
}

/**
 * Intersection Observer hook for lazy loading and performance optimization
 */
export function useIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options?: IntersectionObserverInit
) {
  const targetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const target = targetRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(callback, {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    });

    observer.observe(target);

    return () => {
      observer.disconnect();
    };
  }, [callback, options]);

  return targetRef;
}

/**
 * Hook for measuring element dimensions dynamically
 */
export function useElementSize() {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setSize({ width, height });
      }
    });

    resizeObserver.observe(element);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { size, elementRef };
}
