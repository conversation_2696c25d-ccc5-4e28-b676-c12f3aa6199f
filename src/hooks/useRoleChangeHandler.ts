import { useEffect, useRef } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useUserRole } from './useUserRole';

/**
 * Hook to handle role change effects and ensure proper UI updates
 * This hook detects when the current user's role changes and can trigger
 * additional updates to ensure the UI properly reflects the new role
 */
export const useRoleChangeHandler = () => {
  const { user } = useUser();
  const { role, isAdministrator, isLoading } = useUserRole();
  const previousRole = useRef<string | null>(null);
  const previousIsAdmin = useRef<boolean>(false);

  useEffect(() => {
    // Skip if still loading or no user
    if (isLoading || !user?.id || !role) {
      return;
    }

    // Check if role has changed
    const roleChanged = previousRole.current !== null && previousRole.current !== role;
    const becameAdmin = !previousIsAdmin.current && isAdministrator;
    const lostAdmin = previousIsAdmin.current && !isAdministrator;

    if (roleChanged) {
      console.log(`Role changed from ${previousRole.current} to ${role}`);
      
      if (becameAdmin) {
        console.log('User became administrator - subscription UI should update');
        
        // Dispatch a custom event that subscription components can listen to
        window.dispatchEvent(new CustomEvent('roleChanged', {
          detail: {
            oldRole: previousRole.current,
            newRole: role,
            becameAdmin: true,
            userId: user.id
          }
        }));
      } else if (lostAdmin) {
        console.log('User lost administrator privileges');
        
        window.dispatchEvent(new CustomEvent('roleChanged', {
          detail: {
            oldRole: previousRole.current,
            newRole: role,
            lostAdmin: true,
            userId: user.id
          }
        }));
      } else {
        // Other role changes (e.g., utfoerende to prosjektleder)
        window.dispatchEvent(new CustomEvent('roleChanged', {
          detail: {
            oldRole: previousRole.current,
            newRole: role,
            userId: user.id
          }
        }));
      }
    }

    // Update refs for next comparison
    previousRole.current = role;
    previousIsAdmin.current = isAdministrator;
  }, [role, isAdministrator, isLoading, user?.id]);

  return {
    currentRole: role,
    isAdministrator,
    isLoading
  };
};

/**
 * Hook to listen for role change events
 * Subscription components can use this to react to role changes
 */
export const useRoleChangeListener = (callback: (event: CustomEvent) => void) => {
  useEffect(() => {
    const handleRoleChange = (event: CustomEvent) => {
      callback(event);
    };

    window.addEventListener('roleChanged', handleRoleChange as EventListener);
    
    return () => {
      window.removeEventListener('roleChanged', handleRoleChange as EventListener);
    };
  }, [callback]);
};
