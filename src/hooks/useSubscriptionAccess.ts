// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

/**
 * Hook to manage subscription access control
 * Returns subscription status and access permissions
 */
export const useSubscriptionAccess = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  // const shouldQuery = isClerkLoaded && user?.id; // TODO: Re-enable when needed

  // TODO: Re-enable when type instantiation issue is resolved
  // const subscriptionStatus = useQuery(
  //   api.subscriptions.getSubscriptionStatus,
  //   shouldQuery ? { userId: user.id } : "skip"
  // );
  const subscriptionStatus = {
    isActive: false,
    plan: 'basic',
    trialDaysRemaining: 0,
    isTrialActive: false,
    hasSubscription: false,
    hasActiveSubscription: false,
    isInTrial: false,
    isTrialExpired: false,
    isInGracePeriod: false,
    canCreateProjects: false,
    canAccessProjects: false,
    hasFullAccess: false,
    isReadOnly: true,
    needsUpgrade: true
  }; // Temporarily provide fallback structure due to type instantiation issues

  // TODO: Re-enable when type instantiation issue is resolved
  // const subscription = useQuery(
  //   api.subscriptions.getUserSubscription,
  //   shouldQuery ? { userId: user.id } : "skip"
  // );
  const subscription = {
    _id: 'temp-id',
    userId: user?.id || 'temp-user',
    stripeCustomerId: 'temp-customer',
    status: 'inactive',
    planLevel: 'basic',
    billingInterval: 'month',
    trialStart: Date.now(),
    trialEnd: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days from now
  }; // Temporarily provide fallback structure due to type instantiation issues

  // Loading state
  const isLoading = !isClerkLoaded || subscriptionStatus === undefined;

  // If no subscription data, return default values
  if (!subscriptionStatus || !subscriptionStatus.hasSubscription) {
    return {
      isLoading,
      subscription: null,
      hasActiveSubscription: false,
      isInTrial: false,
      isTrialExpired: false,
      isInGracePeriod: false,
      canCreateProjects: false,
      canAccessProjects: false,
      hasFullAccess: false,
      isReadOnly: false,
      needsUpgrade: true,
      needsTrialSetup: true,
    };
  }

  // Derive access permissions from subscription status
  const {
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade,
  } = subscriptionStatus;

  return {
    isLoading,
    subscription,
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isInGracePeriod,
    canCreateProjects,
    canAccessProjects,
    hasFullAccess,
    isReadOnly,
    needsUpgrade,
    needsTrialSetup: false,
  };
};

/**
 * Hook to get seat management information
 */
export const useSeatManagement = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  // const shouldQuery = isClerkLoaded && user?.id; // TODO: Re-enable when needed

  // TODO: Re-enable when type instantiation issue is resolved
  // const seatCheck = useQuery(
  //   api.seatManagement.canInviteTeamMember,
  //   shouldQuery ? { userId: user.id } : "skip"
  // );
  const seatCheck = {
    canInvite: false,
    reason: 'Temporarily disabled',
    maxSeats: 0,
    warning: null,
    warningMessage: '',
    suggestedPlan: 'basic',
    message: 'Temporarily disabled'
  }; // Temporarily provide fallback due to type instantiation issues

  // TODO: Re-enable when type instantiation issue is resolved
  // const subscription = useQuery(
  //   api.subscriptions.getUserSubscription,
  //   shouldQuery ? { userId: user.id } : "skip"
  // );
  const subscription = {
    _id: 'temp-id',
    userId: user?.id || 'temp-user',
    stripeCustomerId: 'temp-customer',
    status: 'inactive',
    planLevel: 'basic',
    billingInterval: 'month',
    trialStart: Date.now(),
    trialEnd: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
    seats: 0
  }; // Temporarily provide fallback structure due to type instantiation issues

  const isLoading = !isClerkLoaded || seatCheck === undefined || subscription === undefined;

  if (!seatCheck || !subscription) {
    return {
      isLoading,
      canInvite: false,
      currentSeats: 0,
      maxSeats: 0,
      remainingSeats: 0,
      warning: null,
      warningMessage: null,
      isNearLimit: false,
      isCritical: false,
      isAtLimit: false,
    };
  }

  const currentSeats = subscription.seats || 0;
  const maxSeats = seatCheck.maxSeats || 0;
  const remainingSeats = maxSeats - currentSeats;
  const isAtLimit = currentSeats >= maxSeats;
  const isCritical = seatCheck.warning === "critical";
  const isNearLimit = seatCheck.warning === "approaching" || isCritical;

  return {
    isLoading,
    canInvite: seatCheck.canInvite,
    currentSeats,
    maxSeats,
    remainingSeats,
    warning: seatCheck.warning,
    warningMessage: seatCheck.warningMessage,
    isNearLimit,
    isCritical,
    isAtLimit,
    suggestedPlan: seatCheck.suggestedPlan,
    blockedReason: seatCheck.reason,
    blockedMessage: seatCheck.message,
  };
};
