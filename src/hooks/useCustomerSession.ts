import { useState, useEffect } from 'react';
// import { useQuery, useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

// Hook to generate and manage customer session ID with recovery mechanism
export const useCustomerSession = (projectSharedId?: string, projectId?: string) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  // TODO: Re-enable when type instantiation issue is resolved
  // const generateSessionId = useMutation(api.imageLikes.generateCustomerSessionId);
  const generateSessionId = async (args: any) => {
    console.log("⚠️ Generate session ID temporarily disabled due to type issues", args);
    return { sessionId: 'temp-session-id' };
  }; // Temporarily mock mutation
  // TODO: Re-enable when type instantiation issue is resolved
  // const findCustomerSession = useQuery(
  //   api.messages.findCustomerSessionForProject,
  //   projectId && sessionId ? { projectId: projectId as any, currentSessionId: sessionId } : 'skip'
  // );
  const findCustomerSession = null; // Temporarily provide fallback due to type instantiation issues

  // Initialize session ID
  useEffect(() => {
    if (isInitialized) return;

    const initializeSession = async () => {
      try {
        // First, try to find existing session for this project
        if (findCustomerSession && (findCustomerSession as any).sessionId) {
          console.log('🔍 Found existing customer session:', (findCustomerSession as any).sessionId);
          setSessionId((findCustomerSession as any).sessionId);
          setIsInitialized(true);
          return;
        }

        // Check localStorage for existing session
        const storageKey = projectSharedId 
          ? `customer-session-${projectSharedId}` 
          : projectId 
          ? `customer-session-${projectId}` 
          : 'customer-session-default';
        
        const storedSessionId = localStorage.getItem(storageKey);
        
        if (storedSessionId) {
          console.log('📱 Using stored customer session:', storedSessionId);
          setSessionId(storedSessionId);
          setIsInitialized(true);
          return;
        }

        // Generate new session ID
        console.log('🆕 Generating new customer session...');
        const newSessionId = await generateSessionId({});
        
        if (newSessionId?.sessionId) {
          console.log('✅ Generated customer session:', newSessionId.sessionId);
          setSessionId(newSessionId.sessionId);
          localStorage.setItem(storageKey, newSessionId.sessionId);
        } else {
          console.error('❌ Failed to generate customer session ID');
        }
      } catch (error) {
        console.error('❌ Error initializing customer session:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeSession();
  }, [generateSessionId, projectSharedId, projectId, isInitialized, findCustomerSession]);

  // Update localStorage when sessionId changes
  useEffect(() => {
    if (!sessionId) return;

    const storageKey = projectSharedId 
      ? `customer-session-${projectSharedId}` 
      : projectId 
      ? `customer-session-${projectId}` 
      : 'customer-session-default';
    
    localStorage.setItem(storageKey, sessionId);
  }, [sessionId, projectSharedId, projectId]);

  // Recovery mechanism: if we have a session from database but not in state
  useEffect(() => {
    if ((findCustomerSession as any)?.sessionId && !sessionId && isInitialized) {
      console.log('🔄 Recovering customer session from database:', (findCustomerSession as any).sessionId);
      setSessionId((findCustomerSession as any).sessionId);
    }
  }, [findCustomerSession, sessionId, projectSharedId]);

  return sessionId;
};
