import { useEffect } from 'react';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
import { useNavigate, useLocation } from 'react-router-dom';
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

/**
 * Hook to check if the current user is blocked and redirect them to the blocked page
 * Should be used in protected routes and main app components
 */
export const useBlockedUserCheck = () => {
  const { user, isLoaded } = useUser();
  const navigate = useNavigate();
  const location = useLocation();
  
  // TODO: Re-enable when type instantiation issue is resolved
  // const blockStatus = useQuery(
  //   api.teamManagement.checkUserBlocked,
  //   user?.id ? { clerkUserId: user.id } : "skip"
  // );
  const blockStatus = {
    isBlocked: false,
    blockedBy: null,
    blockedAt: null
  }; // Temporarily provide fallback structure due to type instantiation issues

  useEffect(() => {
    // Only check if user is loaded and we have block status
    if (!isLoaded || !user || blockStatus === undefined) {
      return;
    }

    // If user is blocked and not already on the blocked page
    if (blockStatus.isBlocked && location.pathname !== '/blocked') {
      navigate('/blocked', { replace: true });
      return;
    }

    // If user is not blocked but is on the blocked page, redirect to home
    if (!blockStatus.isBlocked && location.pathname === '/blocked') {
      navigate('/', { replace: true });
      return;
    }
  }, [isLoaded, user, blockStatus, location.pathname, navigate]);

  return {
    isBlocked: blockStatus?.isBlocked || false,
    blockStatus,
    isLoading: !isLoaded || blockStatus === undefined,
  };
};

/**
 * Hook specifically for the blocked user page to ensure only blocked users can access it
 */
export const useBlockedUserPageAccess = () => {
  const { user, isLoaded } = useUser();
  const navigate = useNavigate();
  
  // TODO: Re-enable when type instantiation issue is resolved
  // const blockStatus = useQuery(
  //   api.teamManagement.checkUserBlocked,
  //   user?.id ? { clerkUserId: user.id } : "skip"
  // );
  const blockStatus = {
    isBlocked: false,
    blockedBy: null,
    blockedAt: null
  }; // Temporarily provide fallback structure due to type instantiation issues

  useEffect(() => {
    // Only check if user is loaded and we have block status
    if (!isLoaded || !user || blockStatus === undefined) {
      return;
    }

    // If user is not blocked, redirect to home
    if (!blockStatus.isBlocked) {
      navigate('/', { replace: true });
      return;
    }
  }, [isLoaded, user, blockStatus, navigate]);

  return {
    isBlocked: blockStatus?.isBlocked || false,
    blockStatus,
    isLoading: !isLoaded || blockStatus === undefined,
  };
};
