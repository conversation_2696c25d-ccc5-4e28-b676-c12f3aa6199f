import { useState, useEffect, useCallback } from 'react';
import type { FilterOptions, SortOption } from '../components/ui/Search';

interface UseSearchFiltersProps {
  /** Initial search query */
  initialSearchQuery?: string;
  /** Initial filters */
  initialFilters?: Partial<FilterOptions>;
  /** Initial sort option */
  initialSort?: SortOption;
  /** Whether to persist state in localStorage */
  persistState?: boolean;
  /** Storage key for localStorage */
  storageKey?: string;
  /** Whether to persist state in URL */
  persistInUrl?: boolean;
}

const DEFAULT_FILTERS: FilterOptions = {
  projectStatus: 'all',
  customerType: 'all',
  accessLevel: 'all',
  invitationStatus: 'all',
  dateRange: {
    type: 'all',
    period: 'all',
  },
  location: {
    city: '',
  },
};

const DEFAULT_SORT: SortOption = {
  field: 'createdAt',
  direction: 'desc',
  label: 'Nyeste først',
};

/**
 * Custom hook for managing search, filter, and sort state
 * Provides state persistence via localStorage and URL parameters
 * 
 * @example
 * ```tsx
 * const {
 *   searchQuery,
 *   setSearchQuery,
 *   filters,
 *   setFilters,
 *   sortOption,
 *   setSortOption,
 *   clearAllFilters,
 *   activeFilterCount
 * } = useSearchFilters({
 *   persistState: true,
 *   storageKey: 'project-search-filters'
 * });
 * ```
 */
export const useSearchFilters = ({
  initialSearchQuery = '',
  initialFilters = {},
  initialSort = DEFAULT_SORT,
  persistState = true,
  storageKey = 'jobblogg-project-search-filters',
  persistInUrl = false,
}: UseSearchFiltersProps = {}) => {
  // Initialize state from localStorage or defaults
  const [searchQuery, setSearchQueryState] = useState<string>(() => {
    if (persistState && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(`${storageKey}-search`);
        return saved || initialSearchQuery;
      } catch (error) {
        console.warn('Failed to load search query from localStorage:', error);
      }
    }
    return initialSearchQuery;
  });

  const [filters, setFiltersState] = useState<FilterOptions>(() => {
    if (persistState && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(`${storageKey}-filters`);
        if (saved) {
          const parsedFilters = JSON.parse(saved);
          return { ...DEFAULT_FILTERS, ...parsedFilters };
        }
      } catch (error) {
        console.warn('Failed to load filters from localStorage:', error);
      }
    }
    return { ...DEFAULT_FILTERS, ...initialFilters };
  });

  const [sortOption, setSortOptionState] = useState<SortOption>(() => {
    if (persistState && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(`${storageKey}-sort`);
        if (saved) {
          return JSON.parse(saved);
        }
      } catch (error) {
        console.warn('Failed to load sort option from localStorage:', error);
      }
    }
    return initialSort;
  });

  // Persist search query to localStorage
  const setSearchQuery = useCallback((query: string) => {
    setSearchQueryState(query);
    if (persistState && typeof window !== 'undefined') {
      try {
        if (query.trim()) {
          localStorage.setItem(`${storageKey}-search`, query);
        } else {
          localStorage.removeItem(`${storageKey}-search`);
        }
      } catch (error) {
        console.warn('Failed to save search query to localStorage:', error);
      }
    }
  }, [persistState, storageKey]);

  // Persist filters to localStorage
  const setFilters = useCallback((newFilters: FilterOptions) => {
    setFiltersState(newFilters);
    if (persistState && typeof window !== 'undefined') {
      try {
        localStorage.setItem(`${storageKey}-filters`, JSON.stringify(newFilters));
      } catch (error) {
        console.warn('Failed to save filters to localStorage:', error);
      }
    }
  }, [persistState, storageKey]);

  // Persist sort option to localStorage
  const setSortOption = useCallback((newSort: SortOption) => {
    setSortOptionState(newSort);
    if (persistState && typeof window !== 'undefined') {
      try {
        localStorage.setItem(`${storageKey}-sort`, JSON.stringify(newSort));
      } catch (error) {
        console.warn('Failed to save sort option to localStorage:', error);
      }
    }
  }, [persistState, storageKey]);

  // Clear all filters and search
  const clearAllFilters = useCallback(() => {
    setSearchQuery('');
    setFilters(DEFAULT_FILTERS);
    setSortOption(DEFAULT_SORT);
    
    if (persistState && typeof window !== 'undefined') {
      try {
        localStorage.removeItem(`${storageKey}-search`);
        localStorage.removeItem(`${storageKey}-filters`);
        localStorage.removeItem(`${storageKey}-sort`);
      } catch (error) {
        console.warn('Failed to clear localStorage:', error);
      }
    }
  }, [setSearchQuery, setFilters, setSortOption, persistState, storageKey]);

  // Calculate active filter count
  const activeFilterCount = useCallback(() => {
    let count = 0;
    
    if (filters.projectStatus !== 'all') count++;
    if (filters.customerType !== 'all') count++;
    if (filters.accessLevel !== 'all') count++;
    if (filters.invitationStatus !== 'all') count++;
    if (filters.dateRange.type !== 'all') count++;
    if (filters.location.city.trim()) count++;
    
    return count;
  }, [filters]);

  // URL persistence (optional)
  useEffect(() => {
    if (!persistInUrl || typeof window === 'undefined') return;

    const params = new URLSearchParams();
    
    if (searchQuery.trim()) {
      params.set('search', searchQuery);
    }
    
    if (filters.projectStatus !== 'all') {
      params.set('status', filters.projectStatus);
    }
    
    if (filters.customerType !== 'all') {
      params.set('customerType', filters.customerType);
    }
    
    if (filters.accessLevel !== 'all') {
      params.set('accessLevel', filters.accessLevel);
    }
    
    if (sortOption.field !== 'createdAt' || sortOption.direction !== 'desc') {
      params.set('sort', `${sortOption.field}-${sortOption.direction}`);
    }

    const newUrl = params.toString() 
      ? `${window.location.pathname}?${params.toString()}`
      : window.location.pathname;
    
    window.history.replaceState({}, '', newUrl);
  }, [searchQuery, filters, sortOption, persistInUrl]);

  return {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortOption,
    setSortOption,
    clearAllFilters,
    activeFilterCount: activeFilterCount(),
  };
};

export default useSearchFilters;
