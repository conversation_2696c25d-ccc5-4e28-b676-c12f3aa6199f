import { usePWA } from './usePWA';

type OfflineFeature = 'create-project' | 'upload-image' | 'edit-project' | 'view-logs' | 'view-project' | 'chat';

/**
 * Hook for checking feature availability
 */
export function useFeatureAvailability() {
  const { isOnline, canAccessOfflineData } = usePWA();

  const isFeatureAvailable = (feature: OfflineFeature): boolean => {
    const offlineFeatures = {
      'create-project': false, // Requires Brønnøysundregisteret API
      'upload-image': true,    // Can cache images locally
      'edit-project': false,   // Requires online sync
      'view-logs': true,       // Can view cached logs
      'view-project': true,    // Can view cached project data
      'chat': false           // Requires real-time connection
    };

    if (isOnline) return true;
    return canAccessOfflineData && offlineFeatures[feature];
  };

  const getFeatureStatus = (feature: OfflineFeature) => {
    if (isOnline) {
      return { available: true, reason: 'online' };
    }

    if (!canAccessOfflineData) {
      return { available: false, reason: 'no-offline-access' };
    }

    const offlineFeatures = {
      'create-project': true,
      'upload-image': true,
      'edit-project': false,
      'view-logs': true,
      'view-project': true,
      'chat': false
    };

    return {
      available: offlineFeatures[feature],
      reason: offlineFeatures[feature] ? 'offline-available' : 'offline-unavailable'
    };
  };

  return {
    isOnline,
    canAccessOfflineData,
    isFeatureAvailable,
    getFeatureStatus
  };
}
