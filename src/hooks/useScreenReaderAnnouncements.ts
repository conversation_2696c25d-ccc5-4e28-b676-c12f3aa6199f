import { useState } from 'react';

/**
 * Hook for managing screen reader announcements
 */
export function useScreenReaderAnnouncements() {
  const [announcement, setAnnouncement] = useState<{
    message: string;
    priority: 'polite' | 'assertive';
    timestamp: number;
  } | null>(null);

  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    setAnnouncement({
      message,
      priority,
      timestamp: Date.now()
    });
  };

  const announcePolite = (message: string) => announce(message, 'polite');
  const announceAssertive = (message: string) => announce(message, 'assertive');

  return {
    announcement,
    announce,
    announcePolite,
    announceAssertive
  };
}
