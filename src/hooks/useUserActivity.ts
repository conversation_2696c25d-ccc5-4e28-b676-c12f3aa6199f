import { useEffect, useCallback } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

/**
 * Hook for tracking user activity
 * Automatically updates login and activity timestamps
 */
export const useUserActivity = () => {
  const { user, isLoaded } = useUser();
  // TODO: Re-enable when type instantiation issue is resolved
  // const updateLastLogin = useMutation(api.userActivity.updateLastLogin);
  const updateLastLogin = async (args: any) => {
    console.log("⚠️ Update last login temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation
  // TODO: Re-enable when type instantiation issue is resolved
  // const updateLastActivity = useMutation(api.userActivity.updateLastActivity);
  const updateLastActivity = async (args: any) => {
    console.log("⚠️ Update last activity temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Track login when user is first loaded
  useEffect(() => {
    if (isLoaded && user?.id) {
      // Update login timestamp when user is authenticated
      updateLastLogin({ clerkUserId: user.id }).catch(error => {
        console.warn('Failed to update login timestamp:', error);
      });
    }
  }, [isLoaded, user?.id, updateLastLogin]);

  // Function to manually track activity
  const trackActivity = useCallback(async (activityType?: string) => {
    if (!user?.id) return;

    try {
      await updateLastActivity({ 
        clerkUserId: user.id,
        activityType 
      });
    } catch (error) {
      console.warn('Failed to update activity timestamp:', error);
    }
  }, [user?.id, updateLastActivity]);

  return {
    trackActivity,
    isReady: isLoaded && !!user?.id,
  };
};

/**
 * Hook that automatically tracks activity on component mount
 * Use this in components where user performs important actions
 */
export const useAutoTrackActivity = (activityType?: string) => {
  const { trackActivity, isReady } = useUserActivity();

  useEffect(() => {
    if (isReady) {
      trackActivity(activityType);
    }
  }, [isReady, trackActivity, activityType]);

  return { trackActivity };
};
