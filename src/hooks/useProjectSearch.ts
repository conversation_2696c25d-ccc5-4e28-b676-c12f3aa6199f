import { useMemo } from 'react';
import type { FilterOptions, SortOption } from '../components/ui/Search';

// Project type based on the schema analysis
interface Project {
  _id: string;
  name: string;
  description: string;
  createdAt: number;
  updatedAt?: number;
  isArchived?: boolean;
  customer?: {
    name: string;
    type: 'privat' | 'bedrift';
    contactPerson?: string;
    streetAddress?: string;
    postalCode?: string;
    city?: string;
    address?: string;
  } | null;
  // Subcontractor-specific fields
  userAccessLevel?: string;
  invitationStatus?: 'pending' | 'accepted' | 'declined' | 'expired';
  assignedAt?: number;
  subcontractorSpecialization?: string;
  mainContractorCompany?: string;
}

interface UseProjectSearchProps {
  projects: Project[];
  searchQuery: string;
  filters: FilterOptions;
  sortOption: SortOption;
}

/**
 * Custom hook for searching, filtering, and sorting projects
 * Provides optimized search across project names, customer names, and addresses
 * 
 * @example
 * ```tsx
 * const { filteredProjects, searchResults } = useProjectSearch({
 *   projects,
 *   searchQuery,
 *   filters,
 *   sortOption
 * });
 * ```
 */
export const useProjectSearch = ({
  projects,
  searchQuery,
  filters,
  sortOption,
}: UseProjectSearchProps) => {
  const filteredAndSortedProjects = useMemo(() => {
    let result = [...projects];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      result = result.filter((project) => {
        // Search in project name and description
        const projectMatch = 
          project.name.toLowerCase().includes(query) ||
          project.description.toLowerCase().includes(query);

        // Search in customer data
        const customerMatch = project.customer ? (
          project.customer.name.toLowerCase().includes(query) ||
          (project.customer.contactPerson?.toLowerCase().includes(query) || false) ||
          (project.customer.streetAddress?.toLowerCase().includes(query) || false) ||
          (project.customer.city?.toLowerCase().includes(query) || false) ||
          (project.customer.address?.toLowerCase().includes(query) || false)
        ) : false;

        // Search in subcontractor-specific fields
        const subcontractorMatch = 
          (project.subcontractorSpecialization?.toLowerCase().includes(query) || false) ||
          (project.mainContractorCompany?.toLowerCase().includes(query) || false);

        return projectMatch || customerMatch || subcontractorMatch;
      });
    }

    // Apply filters
    result = result.filter((project) => {
      // Project status filter
      if (filters.projectStatus !== 'all') {
        const isArchived = project.isArchived || false;
        if (filters.projectStatus === 'active' && isArchived) return false;
        if (filters.projectStatus === 'archived' && !isArchived) return false;
      }

      // Customer type filter
      if (filters.customerType !== 'all' && project.customer) {
        if (filters.customerType !== project.customer.type) return false;
      }

      // Access level filter
      if (filters.accessLevel !== 'all') {
        const accessLevel = project.userAccessLevel || 'owner';
        if (filters.accessLevel !== accessLevel) return false;
      }

      // Invitation status filter
      if (filters.invitationStatus !== 'all') {
        if (filters.invitationStatus !== project.invitationStatus) return false;
      }

      // Date range filter
      if (filters.dateRange.type !== 'all' && filters.dateRange.period !== 'all') {
        const targetDate = filters.dateRange.type === 'created' 
          ? project.createdAt 
          : (project.updatedAt || project.createdAt);
        
        const now = new Date();
        const projectDate = new Date(targetDate);
        
        switch (filters.dateRange.period) {
          case 'today':
            if (!isSameDay(projectDate, now)) return false;
            break;
          case 'week':
            if (!isWithinDays(projectDate, now, 7)) return false;
            break;
          case 'month':
            if (!isSameMonth(projectDate, now)) return false;
            break;
          case 'quarter':
            if (!isSameQuarter(projectDate, now)) return false;
            break;
          case 'year':
            if (!isSameYear(projectDate, now)) return false;
            break;
        }
      }

      // Location filter
      if (filters.location.city.trim()) {
        const cityQuery = filters.location.city.toLowerCase().trim();
        const projectCity = project.customer?.city?.toLowerCase() || '';
        if (!projectCity.includes(cityQuery)) return false;
      }

      return true;
    });

    // Apply sorting
    result.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortOption.field) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'customerName':
          aValue = (a.customer?.name || '').toLowerCase();
          bValue = (b.customer?.name || '').toLowerCase();
          break;
        case 'createdAt':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'updatedAt':
          aValue = a.updatedAt || a.createdAt;
          bValue = b.updatedAt || b.createdAt;
          break;
        case 'invitationStatus': {
          // Sort by invitation status priority: pending > accepted > declined > expired
          const statusPriority = { pending: 4, accepted: 3, declined: 2, expired: 1 };
          aValue = statusPriority[a.invitationStatus as keyof typeof statusPriority] || 0;
          bValue = statusPriority[b.invitationStatus as keyof typeof statusPriority] || 0;
          break;
        }
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOption.direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return result;
  }, [projects, searchQuery, filters, sortOption]);

  // Calculate search statistics
  const searchResults = useMemo(() => {
    const totalProjects = projects.length;
    const filteredCount = filteredAndSortedProjects.length;
    const hasActiveFilters = 
      filters.projectStatus !== 'all' ||
      filters.customerType !== 'all' ||
      filters.accessLevel !== 'all' ||
      filters.invitationStatus !== 'all' ||
      filters.dateRange.type !== 'all' ||
      filters.location.city.trim() !== '';

    return {
      totalProjects,
      filteredCount,
      hasActiveFilters,
      hasSearchQuery: searchQuery.trim() !== '',
    };
  }, [projects.length, filteredAndSortedProjects.length, filters, searchQuery]);

  return {
    filteredProjects: filteredAndSortedProjects,
    searchResults,
  };
};

// Date utility functions
const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.toDateString() === date2.toDateString();
};

const isWithinDays = (date: Date, referenceDate: Date, days: number): boolean => {
  const diffTime = Math.abs(referenceDate.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= days;
};

const isSameMonth = (date1: Date, date2: Date): boolean => {
  return date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();
};

const isSameQuarter = (date1: Date, date2: Date): boolean => {
  const quarter1 = Math.floor(date1.getMonth() / 3);
  const quarter2 = Math.floor(date2.getMonth() / 3);
  return quarter1 === quarter2 && date1.getFullYear() === date2.getFullYear();
};

const isSameYear = (date1: Date, date2: Date): boolean => {
  return date1.getFullYear() === date2.getFullYear();
};

export default useProjectSearch;
