import { useUser, useAuth } from '@clerk/clerk-react';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

/**
 * Hook to get contractor onboarding status
 * Useful for components that need to conditionally render based on onboarding status
 */
export const useContractorOnboardingStatus = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  // Only query when both Clerk and Convex auth are ready
  const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  // TODO: Re-enable when type instantiation issue is resolved
  // const onboardingStatusResult = useQuery(
  //   api.contractorOnboardingSafe.getContractorOnboardingStatusSafe,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  const onboardingStatusResult = {
    exists: false,
    contractorCompleted: false,
    authError: false,
    contractorCompanyId: null,
    error: null
  }; // Temporarily provide fallback object due to type instantiation issues

  return {
    isLoading: !shouldQuery || onboardingStatusResult === undefined,
    exists: onboardingStatusResult?.exists || false,
    contractorCompleted: onboardingStatusResult?.contractorCompleted || false,
    contractorCompanyId: onboardingStatusResult?.contractorCompanyId || null,
    authError: onboardingStatusResult?.authError || false,
    error: onboardingStatusResult?.error || null,
  };
};

/**
 * Hook to get contractor company information
 * Returns the contractor's company data if available
 */
export const useContractorCompany = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  // Only query when both Clerk and Convex auth are ready
  const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  // TODO: Re-enable when type instantiation issue is resolved
  // const contractorCompanyResult = useQuery(
  //   api.contractorOnboardingSafe.getContractorCompanyByUserIdSafe,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  const contractorCompanyResult = {
    company: null,
    authError: false,
    error: null
  }; // Temporarily provide fallback object due to type instantiation issues

  return {
    isLoading: !shouldQuery || contractorCompanyResult === undefined,
    company: contractorCompanyResult?.company || null,
    authError: contractorCompanyResult?.authError || false,
    error: contractorCompanyResult?.error || null,
  };
};
