import { useState, useEffect, useCallback } from 'react';

export interface CookieConsent {
  necessary: boolean;
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
  version: string;
}

const COOKIE_CONSENT_KEY = 'jobblogg-cookie-consent';
const CONSENT_VERSION = '1.0';
const CONSENT_EXPIRY_DAYS = 365;

const defaultConsent: CookieConsent = {
  necessary: true, // Always true, cannot be disabled
  functional: false,
  analytics: false,
  marketing: false,
  timestamp: 0,
  version: CONSENT_VERSION,
};

export const useCookieConsent = () => {
  const [consent, setConsent] = useState<CookieConsent | null>(null);
  const [showBanner, setShowBanner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load consent from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(COOKIE_CONSENT_KEY);
      if (stored) {
        const parsedConsent: CookieConsent = JSON.parse(stored);
        
        // Check if consent is still valid (not expired and correct version)
        const isExpired = Date.now() - parsedConsent.timestamp > CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000;
        const isOutdated = parsedConsent.version !== CONSENT_VERSION;
        
        if (!isExpired && !isOutdated) {
          setConsent(parsedConsent);
          setShowBanner(false);
        } else {
          // Consent expired or outdated, show banner again
          setConsent(null);
          setShowBanner(true);
        }
      } else {
        // No consent found, show banner
        setShowBanner(true);
      }
    } catch (error) {
      console.error('Error loading cookie consent:', error);
      setShowBanner(true);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save consent to localStorage
  const saveConsent = useCallback((newConsent: Partial<CookieConsent>) => {
    const fullConsent: CookieConsent = {
      ...defaultConsent,
      ...newConsent,
      necessary: true, // Always true
      timestamp: Date.now(),
      version: CONSENT_VERSION,
    };

    try {
      localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(fullConsent));
      setConsent(fullConsent);
      setShowBanner(false);
      
      // Trigger custom event for other parts of the app to listen to
      window.dispatchEvent(new CustomEvent('cookieConsentChanged', { 
        detail: fullConsent 
      }));
    } catch (error) {
      console.error('Error saving cookie consent:', error);
    }
  }, []);

  // Accept all cookies
  const acceptAll = useCallback(() => {
    saveConsent({
      functional: true,
      analytics: true,
      marketing: true,
    });
  }, [saveConsent]);

  // Accept only necessary cookies
  const acceptNecessary = useCallback(() => {
    saveConsent({
      functional: false,
      analytics: false,
      marketing: false,
    });
  }, [saveConsent]);

  // Update specific consent preferences
  const updateConsent = useCallback((updates: Partial<CookieConsent>) => {
    if (consent) {
      saveConsent({
        ...consent,
        ...updates,
      });
    }
  }, [consent, saveConsent]);

  // Reset consent (for testing or user request)
  const resetConsent = useCallback(() => {
    try {
      localStorage.removeItem(COOKIE_CONSENT_KEY);
      setConsent(null);
      setShowBanner(true);
      
      window.dispatchEvent(new CustomEvent('cookieConsentChanged', { 
        detail: null 
      }));
    } catch (error) {
      console.error('Error resetting cookie consent:', error);
    }
  }, []);

  // Show consent modal (for settings page)
  const showConsentModal = useCallback(() => {
    setShowBanner(true);
  }, []);

  // Hide banner without saving (for modal close)
  const hideBanner = useCallback(() => {
    setShowBanner(false);
  }, []);

  // Check if specific cookie type is allowed
  const isAllowed = useCallback((type: keyof CookieConsent) => {
    if (type === 'necessary') return true;
    if (type === 'timestamp' || type === 'version') return false;
    return consent?.[type] || false;
  }, [consent]);

  // Check if consent has been given (any response)
  const hasConsent = consent !== null;

  return {
    consent,
    showBanner,
    isLoading,
    hasConsent,
    acceptAll,
    acceptNecessary,
    updateConsent,
    resetConsent,
    showConsentModal,
    hideBanner,
    isAllowed,
  };
};
