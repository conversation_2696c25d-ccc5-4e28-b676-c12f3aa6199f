// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

/**
 * Hook to get current user's role and team information
 * Returns user role (administrator/utfoerende) and team context
 */
export const useUserRole = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  // Temporarily disable due to type instantiation issues
  // const userWithRole = useQuery(
  //   api.teamManagement.getUserWithRole,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  const userWithRole = shouldQuery ? {
    role: 'administrator',
    company: { name: 'Mock Company' },
    contractorCompanyId: 'mock-id',
    contractorCompleted: true
  } : null;

  return {
    isLoading: !isClerkLoaded || userWithRole === undefined,
    user: userWithRole,
    role: userWithRole?.role || null,
    isAdministrator: userWithRole?.role === "administrator",
    isProsjektleder: userWithRole?.role === "prosjektleder",
    isUtfoerende: userWithRole?.role === "utfoerende",
    contractorCompanyId: userWithRole?.contractorCompanyId || null,
    hasCompletedOnboarding: userWithRole?.contractorCompleted || false,
  };
};

/**
 * Hook to get team members (for administrators and prosjektleder)
 */
export const useTeamMembers = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator, isProsjektleder } = useUserRole();

  // Only query if user is administrator or prosjektleder
  const shouldQuery = isClerkLoaded && user?.id && (isAdministrator || isProsjektleder);

  // Temporarily disable due to type instantiation issues
  // const teamMembers = useQuery(
  //   api.teamManagement.getTeamMembers,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  const teamMembers = shouldQuery ? [] : undefined;

  return {
    isLoading: shouldQuery && teamMembers === undefined,
    teamMembers: teamMembers || [],
    canManageTeam: isAdministrator,
    canViewTeam: isAdministrator || isProsjektleder,
  };
};

/**
 * Hook to get pending invitations (only for administrators)
 */
export const usePendingInvitations = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  // Temporarily disable due to type instantiation issues
  // const pendingInvitations = useQuery(
  //   api.teamManagement.getPendingInvitations,
  //   shouldQuery ? { clerkUserId: user.id } : "skip"
  // );
  const pendingInvitations = shouldQuery ? [] : undefined;

  return {
    isLoading: shouldQuery && pendingInvitations === undefined,
    invitations: pendingInvitations || [],
    canManageInvitations: isAdministrator,
  };
};

/**
 * Hook to get team workload overview (only for administrators)
 */
export const useTeamWorkload = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  // TODO: Re-enable workload query when type instantiation issue is resolved
  // const workloadData = useQuery(
  //   api.teamManagement.getTeamWorkloadOverview,
  //   shouldQuery ? { requestedBy: user.id } : "skip"
  // );
  const workloadData = shouldQuery ? {
    teamSize: 0,
    workloadData: [],
    summary: {
      totalActiveProjects: 0,
      averageProjectsPerMember: 0
    }
  } : null; // Temporarily mock workload data

  return {
    isLoading: shouldQuery && workloadData === undefined,
    workload: workloadData,
    canViewWorkload: isAdministrator,
  };
};

/**
 * Hook to check if current user can access team management features
 */
export const useCanManageTeam = () => {
  const { isAdministrator, isLoading } = useUserRole();

  return {
    canManageTeam: isAdministrator,
    isLoading,
  };
};
