/**
 * Contractor Specializations/Fagområder System
 * 
 * Comprehensive list of construction trades and professional services
 * for subcontractor matching and identification
 */

export interface Specialization {
  id: string;
  name: string;
  category: 'construction' | 'professional' | 'technical' | 'maintenance';
  description?: string;
  commonNaceCodes?: string[]; // NACE codes that typically map to this specialization
}

// Construction Trades (Håndverksfag)
export const CONSTRUCTION_SPECIALIZATIONS: Specialization[] = [
  {
    id: 'elektriker',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    category: 'construction',
    description: 'Elektriske installasjoner og vedlikehold',
    commonNaceCodes: ['43210', '43220']
  },
  {
    id: 'rorlegger',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    category: 'construction', 
    description: 'VVS-installasjoner og sanitærteknikk',
    commonNaceCodes: ['43220', '43290']
  },
  {
    id: 'maler',
    name: 'Maler',
    category: 'construction',
    description: 'Maling og overflatebehandling',
    commonNaceCodes: ['43340', '43390']
  },
  {
    id: 'tommer',
    name: 'Tømrer',
    category: 'construction',
    description: 'Trearbeid og snekkerarbeid',
    commonNaceCodes: ['43320', '43330']
  },
  {
    id: 'murer',
    name: 'Murer',
    category: 'construction',
    description: 'Murarbeid og steinarbeid',
    commonNaceCodes: ['43320', '43390']
  },
  {
    id: 'fliselegger',
    name: 'Fliselegger',
    category: 'construction',
    description: 'Flislegging og gulvarbeid',
    commonNaceCodes: ['43330', '43390']
  },
  {
    id: 'taktekkjer',
    name: 'Taktekker',
    category: 'construction',
    description: 'Takarbeid og taktekking',
    commonNaceCodes: ['43910', '43320']
  },
  {
    id: 'isolator',
    name: 'Isolatør',
    category: 'construction',
    description: 'Isolering og tetting',
    commonNaceCodes: ['43290', '43390']
  },
  {
    id: 'glassmaker',
    name: 'Glassmaker',
    category: 'construction',
    description: 'Glassarbeid og vinduer',
    commonNaceCodes: ['43320', '43390']
  }
];

// Professional Services (Profesjonelle tjenester)
export const PROFESSIONAL_SPECIALIZATIONS: Specialization[] = [
  {
    id: 'arkitekt',
    name: 'Arkitekt',
    category: 'professional',
    description: 'Arkitekttjenester og byggedesign',
    commonNaceCodes: ['71110', '71120']
  },
  {
    id: 'radgiver',
    name: 'Rådgiver',
    category: 'professional',
    description: 'Teknisk rådgivning og konsulentvirksomhet',
    commonNaceCodes: ['71120', '71200']
  },
  {
    id: 'konsulent',
    name: 'Konsulent',
    category: 'professional',
    description: 'Spesialisert konsulentvirksomhet',
    commonNaceCodes: ['70220', '71200']
  },
  {
    id: 'ingeniør',
    name: 'Ingeniør',
    category: 'professional',
    description: 'Ingeniørtjenester og teknisk design',
    commonNaceCodes: ['71120', '71200']
  },
  {
    id: 'prosjektleder',
    name: 'Prosjektleder',
    category: 'professional',
    description: 'Prosjektledelse og byggeledelse',
    commonNaceCodes: ['70220', '71120']
  },
  {
    id: 'byggeleder',
    name: 'Byggeleder',
    category: 'professional',
    description: 'Byggeledelse og koordinering',
    commonNaceCodes: ['43110', '71120']
  }
];

// Technical Services (Tekniske tjenester)
export const TECHNICAL_SPECIALIZATIONS: Specialization[] = [
  {
    id: 'ventilasjon',
    name: 'Ventilasjon',
    category: 'technical',
    description: 'Ventilasjonsanlegg og luftbehandling',
    commonNaceCodes: ['43220', '43290']
  },
  {
    id: 'varme',
    name: 'Varme',
    category: 'technical',
    description: 'Varmeanlegg og oppvarming',
    commonNaceCodes: ['43220', '43290']
  },
  {
    id: 'kjoling',
    name: 'Kjøling',
    category: 'technical',
    description: 'Kjøleanlegg og klimaanlegg',
    commonNaceCodes: ['43220', '43290']
  },
  {
    id: 'automatisering',
    name: 'Automatisering',
    category: 'technical',
    description: 'Bygningsautomatisering og styringssystemer',
    commonNaceCodes: ['43210', '43290']
  },
  {
    id: 'sikkerhet',
    name: 'Sikkerhet',
    category: 'technical',
    description: 'Sikkerhetssystemer og alarmer',
    commonNaceCodes: ['43210', '80200']
  }
];

// Maintenance Services (Vedlikeholdstjenester)
export const MAINTENANCE_SPECIALIZATIONS: Specialization[] = [
  {
    id: 'renhold',
    name: 'Renhold',
    category: 'maintenance',
    description: 'Rengjøring og vedlikehold',
    commonNaceCodes: ['81210', '81220']
  },
  {
    id: 'vedlikehold',
    name: 'Vedlikehold',
    category: 'maintenance',
    description: 'Generelt vedlikehold og reparasjoner',
    commonNaceCodes: ['43990', '81300']
  },
  {
    id: 'hagearbeid',
    name: 'Hagearbeid',
    category: 'maintenance',
    description: 'Hagearbeid og landskapspleie',
    commonNaceCodes: ['81300', '02100']
  }
];

// Combined list of all specializations
export const ALL_SPECIALIZATIONS: Specialization[] = [
  ...CONSTRUCTION_SPECIALIZATIONS,
  ...PROFESSIONAL_SPECIALIZATIONS,
  ...TECHNICAL_SPECIALIZATIONS,
  ...MAINTENANCE_SPECIALIZATIONS
];

// Category labels for UI
export const SPECIALIZATION_CATEGORIES = {
  construction: 'Håndverksfag',
  professional: 'Profesjonelle tjenester',
  technical: 'Tekniske tjenester',
  maintenance: 'Vedlikeholdstjenester'
} as const;

/**
 * Get specialization by ID
 */
export const getSpecializationById = (id: string): Specialization | undefined => {
  return ALL_SPECIALIZATIONS.find(spec => spec.id === id);
};

/**
 * Get specializations by category
 */
export const getSpecializationsByCategory = (category: Specialization['category']): Specialization[] => {
  return ALL_SPECIALIZATIONS.filter(spec => spec.category === category);
};

/**
 * Suggest specialization based on NACE industry code
 */
export const suggestSpecializationFromNaceCode = (naceCode: string): Specialization | null => {
  // Find specialization that matches the NACE code
  const match = ALL_SPECIALIZATIONS.find(spec => 
    spec.commonNaceCodes?.includes(naceCode)
  );
  
  return match || null;
};

/**
 * Get multiple specialization suggestions from NACE code
 */
export const getSpecializationSuggestionsFromNaceCode = (naceCode: string): Specialization[] => {
  return ALL_SPECIALIZATIONS.filter(spec => 
    spec.commonNaceCodes?.includes(naceCode)
  );
};

/**
 * Format specializations for display
 */
export const formatSpecializations = (
  primary?: string, 
  secondary?: string[]
): string => {
  const primarySpec = primary ? getSpecializationById(primary) : null;
  const secondarySpecs = secondary?.map(id => getSpecializationById(id)).filter(Boolean) || [];
  
  const parts: string[] = [];
  
  if (primarySpec) {
    parts.push(primarySpec.name);
  }
  
  if (secondarySpecs.length > 0) {
    parts.push(...secondarySpecs.map(spec => spec!.name));
  }
  
  return parts.join(', ');
};

/**
 * Search specializations by name
 */
export const searchSpecializations = (query: string): Specialization[] => {
  const lowerQuery = query.toLowerCase();
  return ALL_SPECIALIZATIONS.filter(spec =>
    spec.name.toLowerCase().includes(lowerQuery) ||
    spec.description?.toLowerCase().includes(lowerQuery)
  );
};
