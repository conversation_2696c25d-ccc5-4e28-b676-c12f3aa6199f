/**
 * Project Permission Utilities
 * 
 * Centralized logic for determining what actions users can perform on projects
 * based on their role and relationship to the project.
 */

export interface UserAccess {
  hasAccess: boolean;
  accessLevel: 'owner' | 'administrator' | 'collaborator' | 'subcontractor' | 'viewer' | null;
  reason: string;
}

export interface ProjectPermissions {
  canShare: boolean;
  canArchive: boolean;
  canAssignTeamMembers: boolean;
  canWithdraw: boolean;
  canEditProject: boolean;
  canViewProject: boolean;
  canManageTeam: boolean;
}

/**
 * Determine what permissions a user has for a specific project
 */
export function getProjectPermissions(
  userAccess: UserAccess | null,
  isProjectOwner: boolean,
  userRole: 'administrator' | 'prosjektleder' | 'utførende' | null
): ProjectPermissions {
  // Default permissions (no access)
  const defaultPermissions: ProjectPermissions = {
    canShare: false,
    canArchive: false,
    canAssignTeamMembers: false,
    canWithdraw: false,
    canEditProject: false,
    canViewProject: false,
    canManageTeam: false,
  };

  // No access at all
  if (!userAccess?.hasAccess) {
    return defaultPermissions;
  }

  const accessLevel = userAccess.accessLevel;

  // Project Owner permissions (full access)
  if (accessLevel === 'owner' || isProjectOwner) {
    return {
      canShare: true,
      canArchive: true,
      canAssignTeamMembers: true,
      canWithdraw: false, // Owners can't withdraw from their own projects
      canEditProject: true,
      canViewProject: true,
      canManageTeam: true,
    };
  }

  // Administrator from same company permissions
  if (accessLevel === 'administrator') {
    return {
      canShare: true,
      canArchive: true,
      canAssignTeamMembers: true,
      canWithdraw: false, // Company administrators can't withdraw from company projects
      canEditProject: true,
      canViewProject: true,
      canManageTeam: true,
    };
  }

  // Prosjektleder permissions (project manager)
  if ((accessLevel as any) === 'prosjektleder') {
    return {
      canShare: true,
      canArchive: true,
      canAssignTeamMembers: true, // Can assign team members to projects
      canWithdraw: false, // Project managers can't withdraw from company projects
      canEditProject: true,
      canViewProject: true,
      canManageTeam: false, // Cannot manage overall team (only administrators can)
    };
  }

  // Subcontractor Administrator permissions
  if (accessLevel === 'subcontractor' && userRole === 'administrator') {
    return {
      canShare: false, // CANNOT share projects (main requirement)
      canArchive: true, // CAN archive (local view only)
      canAssignTeamMembers: true, // CAN assign own team members
      canWithdraw: true, // CAN withdraw from subcontractor assignments
      canEditProject: true, // CAN edit project data
      canViewProject: true,
      canManageTeam: true, // CAN manage own team assignments
    };
  }

  // Subcontractor Team Member permissions
  if (accessLevel === 'subcontractor' && userRole === 'utførende') {
    return {
      canShare: false,
      canArchive: false,
      canAssignTeamMembers: false,
      canWithdraw: false,
      canEditProject: true, // CAN edit project data
      canViewProject: true,
      canManageTeam: false,
    };
  }

  // Regular Collaborator permissions
  if (accessLevel === 'collaborator') {
    return {
      canShare: false,
      canArchive: false,
      canAssignTeamMembers: false,
      canWithdraw: false,
      canEditProject: true,
      canViewProject: true,
      canManageTeam: false,
    };
  }

  // Viewer permissions
  if (accessLevel === 'viewer') {
    return {
      canShare: false,
      canArchive: false,
      canAssignTeamMembers: false,
      canWithdraw: false,
      canEditProject: false,
      canViewProject: true,
      canManageTeam: false,
    };
  }

  return defaultPermissions;
}

/**
 * Check if user can perform a specific action on a project
 */
export function canUserPerformAction(
  action: keyof ProjectPermissions,
  userAccess: UserAccess | null,
  isProjectOwner: boolean,
  userRole: 'administrator' | 'prosjektleder' | 'utførende' | null
): boolean {
  const permissions = getProjectPermissions(userAccess, isProjectOwner, userRole);
  return permissions[action];
}

/**
 * Get user-friendly reason why an action is not allowed
 */
export function getPermissionDeniedReason(
  action: keyof ProjectPermissions,
  userAccess: UserAccess | null
): string {
  if (!userAccess?.hasAccess) {
    return "Du har ikke tilgang til dette prosjektet";
  }

  const accessLevel = userAccess.accessLevel;

  switch (action) {
    case 'canShare':
      if (accessLevel === 'subcontractor') {
        return "Underleverandører kan ikke dele prosjekter med kunder";
      }
      return "Du har ikke tillatelse til å dele dette prosjektet";

    case 'canArchive':
      if (accessLevel === 'subcontractor') {
        return "Du kan arkivere prosjektet for din egen oversikt";
      }
      return "Du har ikke tillatelse til å arkivere dette prosjektet";

    case 'canAssignTeamMembers':
      if (accessLevel === 'subcontractor') {
        return "Du kan kun tildele medlemmer fra ditt eget firma";
      }
      return "Du har ikke tillatelse til å tildele teammedlemmer";

    case 'canWithdraw':
      return "Du kan trekke deg fra underleverandør-oppdrag";

    default:
      return "Du har ikke tillatelse til denne handlingen";
  }
}
