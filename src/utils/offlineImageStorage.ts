/**
 * GDPR-Compliant Offline Image Storage with IndexedDB
 * Provides encrypted local image storage and upload queue management
 */

import { offlineEncryption, EncryptedData } from './offlineEncryption';

// Types for offline image storage
export interface OfflineImage {
  id: string;
  projectId: string;
  fileName: string;
  mimeType: string;
  size: number;
  encryptedData: EncryptedData;
  thumbnail?: EncryptedData;
  createdAt: string;
  syncStatus: 'pending' | 'syncing' | 'synced' | 'error';
  uploadAttempts: number;
  lastUploadAttempt?: string;
}

export interface ImageUploadQueueItem {
  id: string;
  projectId: string;
  logId?: string;
  imageData: OfflineImage;
  priority: 'high' | 'normal' | 'low';
  createdAt: string;
}

/**
 * Offline Image Storage Manager using IndexedDB
 */
export class OfflineImageStorageManager {
  private static instance: OfflineImageStorageManager;
  private db: IDBDatabase | null = null;
  private dbName = 'jobblogg-offline-images';
  private dbVersion = 1;
  private currentUserId: string | null = null;

  private constructor() {}

  static getInstance(): OfflineImageStorageManager {
    if (!OfflineImageStorageManager.instance) {
      OfflineImageStorageManager.instance = new OfflineImageStorageManager();
    }
    return OfflineImageStorageManager.instance;
  }

  /**
   * Initialize IndexedDB for offline image storage
   */
  async initializeForUser(userId: string): Promise<boolean> {
    if (!offlineEncryption.isInitialized()) {
      console.error('[OfflineImageStorage] Encryption not initialized');
      return false;
    }

    this.currentUserId = userId;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(`${this.dbName}-${userId}`, this.dbVersion);

      request.onerror = () => {
        console.error('[OfflineImageStorage] Failed to open IndexedDB:', request.error);
        reject(false);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('[OfflineImageStorage] IndexedDB initialized for user:', userId.substring(0, 8) + '...');
        resolve(true);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create images store
        if (!db.objectStoreNames.contains('images')) {
          const imagesStore = db.createObjectStore('images', { keyPath: 'id' });
          imagesStore.createIndex('projectId', 'projectId', { unique: false });
          imagesStore.createIndex('syncStatus', 'syncStatus', { unique: false });
          imagesStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // Create upload queue store
        if (!db.objectStoreNames.contains('uploadQueue')) {
          const queueStore = db.createObjectStore('uploadQueue', { keyPath: 'id' });
          queueStore.createIndex('priority', 'priority', { unique: false });
          queueStore.createIndex('createdAt', 'createdAt', { unique: false });
        }
      };
    });
  }

  /**
   * Store image offline with encryption
   */
  async storeImageOffline(
    projectId: string,
    file: File,
    logId?: string
  ): Promise<string> {
    if (!this.db || !this.currentUserId || !offlineEncryption.isInitialized()) {
      throw new Error('Offline image storage ikke initialisert');
    }

    try {
      // Generate unique ID for the image
      const imageId = `offline-img-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Read file as array buffer
      const arrayBuffer = await file.arrayBuffer();
      
      // Compress image if it's too large (>2MB)
      const compressedData = await this.compressImageIfNeeded(arrayBuffer, file.type);
      
      // Encrypt the image data
      const encryptedData = await offlineEncryption.encryptData({
        data: Array.from(new Uint8Array(compressedData)),
        mimeType: file.type
      });

      // Generate thumbnail
      const thumbnail = await this.generateThumbnail(compressedData, file.type);
      const encryptedThumbnail = thumbnail ? await offlineEncryption.encryptData({
        data: Array.from(new Uint8Array(thumbnail)),
        mimeType: file.type
      }) : undefined;

      // Create offline image record
      const offlineImage: OfflineImage = {
        id: imageId,
        projectId,
        fileName: file.name,
        mimeType: file.type,
        size: compressedData.byteLength,
        encryptedData,
        thumbnail: encryptedThumbnail,
        createdAt: new Date().toISOString(),
        syncStatus: 'pending',
        uploadAttempts: 0
      };

      // Store in IndexedDB
      await this.storeInDB('images', offlineImage);

      // Add to upload queue
      const queueItem: ImageUploadQueueItem = {
        id: `queue-${imageId}`,
        projectId,
        logId,
        imageData: offlineImage,
        priority: 'normal',
        createdAt: new Date().toISOString()
      };

      await this.storeInDB('uploadQueue', queueItem);

      console.log('[OfflineImageStorage] Image stored offline:', imageId);
      return imageId;
    } catch (error) {
      console.error('[OfflineImageStorage] Failed to store image offline:', error);
      throw new Error('Kunne ikke lagre bilde offline');
    }
  }

  /**
   * Retrieve offline image
   */
  async getOfflineImage(imageId: string): Promise<Blob | null> {
    if (!this.db || !offlineEncryption.isInitialized()) {
      return null;
    }

    try {
      const offlineImage = await this.getFromDB<OfflineImage>('images', imageId);
      if (!offlineImage) return null;

      // Decrypt image data
      const decryptedData = await offlineEncryption.decryptData<{
        data: number[];
        mimeType: string;
      }>(offlineImage.encryptedData);

      // Convert back to Blob
      const uint8Array = new Uint8Array(decryptedData.data);
      return new Blob([uint8Array], { type: decryptedData.mimeType });
    } catch (error) {
      console.error('[OfflineImageStorage] Failed to retrieve offline image:', error);
      return null;
    }
  }

  /**
   * Get thumbnail for offline image
   */
  async getOfflineImageThumbnail(imageId: string): Promise<Blob | null> {
    if (!this.db || !offlineEncryption.isInitialized()) {
      return null;
    }

    try {
      const offlineImage = await this.getFromDB<OfflineImage>('images', imageId);
      if (!offlineImage?.thumbnail) return null;

      // Decrypt thumbnail data
      const decryptedData = await offlineEncryption.decryptData<{
        data: number[];
        mimeType: string;
      }>(offlineImage.thumbnail);

      // Convert back to Blob
      const uint8Array = new Uint8Array(decryptedData.data);
      return new Blob([uint8Array], { type: decryptedData.mimeType });
    } catch (error) {
      console.error('[OfflineImageStorage] Failed to retrieve thumbnail:', error);
      return null;
    }
  }

  /**
   * Get all offline images for a project
   */
  async getProjectImages(projectId: string): Promise<OfflineImage[]> {
    if (!this.db) return [];

    return new Promise((resolve, _reject) => {
      const transaction = this.db!.transaction(['images'], 'readonly');
      const store = transaction.objectStore('images');
      const index = store.index('projectId');
      const request = index.getAll(projectId);

      request.onsuccess = () => resolve(request.result || []);
      request.onerror = () => {
        console.error('[OfflineImageStorage] Failed to get project images:', request.error);
        resolve([]);
      };
    });
  }

  /**
   * Get upload queue
   */
  async getUploadQueue(): Promise<ImageUploadQueueItem[]> {
    if (!this.db) return [];

    return new Promise((resolve, _reject) => {
      const transaction = this.db!.transaction(['uploadQueue'], 'readonly');
      const store = transaction.objectStore('uploadQueue');
      const request = store.getAll();

      request.onsuccess = () => {
        const items = request.result || [];
        // Sort by priority and creation date
        items.sort((a, b) => {
          const priorityOrder: { [key: string]: number } = { high: 0, normal: 1, low: 2 };
          const priorityDiff = (priorityOrder[a.priority] || 1) - (priorityOrder[b.priority] || 1);
          if (priorityDiff !== 0) return priorityDiff;
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
        resolve(items);
      };
      request.onerror = () => {
        console.error('[OfflineImageStorage] Failed to get upload queue:', request.error);
        resolve([]);
      };
    });
  }

  /**
   * Update image sync status
   */
  async updateImageSyncStatus(
    imageId: string, 
    status: OfflineImage['syncStatus'],
    uploadAttempts?: number
  ): Promise<void> {
    if (!this.db) return;

    try {
      const image = await this.getFromDB<OfflineImage>('images', imageId);
      if (!image) return;

      image.syncStatus = status;
      if (uploadAttempts !== undefined) {
        image.uploadAttempts = uploadAttempts;
        image.lastUploadAttempt = new Date().toISOString();
      }

      await this.storeInDB('images', image);
    } catch (error) {
      console.error('[OfflineImageStorage] Failed to update sync status:', error);
    }
  }

  /**
   * Remove image from upload queue
   */
  async removeFromUploadQueue(queueItemId: string): Promise<void> {
    if (!this.db) return;

    return new Promise((resolve, _reject) => {
      const transaction = this.db!.transaction(['uploadQueue'], 'readwrite');
      const store = transaction.objectStore('uploadQueue');
      const request = store.delete(queueItemId);

      request.onsuccess = () => resolve();
      request.onerror = () => {
        console.error('[OfflineImageStorage] Failed to remove from upload queue:', request.error);
        resolve();
      };
    });
  }

  /**
   * Clear all offline images (GDPR compliance)
   */
  async clearAllImages(): Promise<void> {
    if (!this.db) return;

    return new Promise((resolve, _reject) => {
      const transaction = this.db!.transaction(['images', 'uploadQueue'], 'readwrite');

      // const imagesStore = transaction.objectStore('images'); // TODO: Use for clearing images
      // const queueStore = transaction.objectStore('uploadQueue'); // TODO: Use for clearing queue

      // const _clearImages = imagesStore.clear(); // TODO: Use for clearing images
      // const _clearQueue = queueStore.clear(); // TODO: Use for clearing queue

      transaction.oncomplete = () => {
        console.log('[OfflineImageStorage] All offline images cleared');
        resolve();
      };
      
      transaction.onerror = () => {
        console.error('[OfflineImageStorage] Failed to clear images:', transaction.error);
        resolve();
      };
    });
  }

  // Private helper methods
  private async storeInDB(storeName: string, data: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private async getFromDB<T>(storeName: string, key: string): Promise<T | null> {
    if (!this.db) return null;

    return new Promise((resolve, _reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => resolve(null);
    });
  }

  private async compressImageIfNeeded(arrayBuffer: ArrayBuffer, _mimeType: string): Promise<ArrayBuffer> {
    // If image is smaller than 2MB, return as-is
    if (arrayBuffer.byteLength < 2 * 1024 * 1024) {
      return arrayBuffer;
    }

    // For larger images, implement compression logic here
    // This is a simplified version - in production, you'd use a proper image compression library
    return arrayBuffer;
  }

  private async generateThumbnail(arrayBuffer: ArrayBuffer, mimeType: string): Promise<ArrayBuffer | null> {
    try {
      // Create a canvas to generate thumbnail
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return null;

      // Create image from array buffer
      const blob = new Blob([arrayBuffer], { type: mimeType });
      const img = new Image();
      
      return new Promise((resolve) => {
        img.onload = () => {
          // Set thumbnail size (max 200x200)
          const maxSize = 200;
          let { width, height } = img;
          
          if (width > height) {
            if (width > maxSize) {
              height = (height * maxSize) / width;
              width = maxSize;
            }
          } else {
            if (height > maxSize) {
              width = (width * maxSize) / height;
              height = maxSize;
            }
          }

          canvas.width = width;
          canvas.height = height;
          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob((thumbnailBlob) => {
            if (thumbnailBlob) {
              thumbnailBlob.arrayBuffer().then(resolve);
            } else {
              resolve(null);
            }
          }, 'image/jpeg', 0.7);
        };

        img.onerror = () => resolve(null);
        img.src = URL.createObjectURL(blob);
      });
    } catch (error) {
      console.error('[OfflineImageStorage] Failed to generate thumbnail:', error);
      return null;
    }
  }
}

// Export singleton instance
export const offlineImageStorage = OfflineImageStorageManager.getInstance();
