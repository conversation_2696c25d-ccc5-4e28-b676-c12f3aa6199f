import { lazy, ComponentType, LazyExoticComponent } from 'react';

// Utility function to create lazy components with error handling
export const createLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): LazyExoticComponent<T> => {
  return lazy(() =>
    importFn().catch((error) => {
      console.error('[LazyComponents] Failed to load component:', error);
      // Return a fallback component that shows an error message
      return import('./LazyLoadErrorFallback').then(module => ({
        default: module.default as unknown as T
      }));
    })
  );
};

// Preload a component without rendering it
export const preloadComponent = (importFn: () => Promise<any>) => {
  importFn().catch((error) => {
    console.warn('[LazyComponents] Failed to preload component:', error);
  });
};

// Preload critical routes on app initialization
export const preloadCriticalRoutes = () => {
  console.log('[LazyComponents] Preloading critical routes for offline support');

  // Preload Dashboard (most likely first page after login)
  preloadComponent(() => import('../pages/Dashboard/Dashboard'));

  // Preload ProjectDetail (common after dashboard)
  preloadComponent(() => import('../pages/ProjectDetail/ProjectDetail'));

  // Preload ProjectLog (common user action)
  preloadComponent(() => import('../pages/ProjectLog/ProjectLog'));

  // Preload CreateProject (common user action)
  preloadComponent(() => import('../pages/CreateProject/CreateProjectWizard'));
};

// Route-based code splitting with preloading
export const RoutePreloader = {
  // Preload route when user hovers over navigation
  onNavigationHover: (routeName: string) => {
    switch (routeName) {
      case 'dashboard':
        preloadComponent(() => import('../pages/Dashboard/Dashboard'));
        break;
      case 'create-project':
        preloadComponent(() => import('../pages/CreateProject/CreateProjectWizard'));
        break;
      case 'conversations':
        preloadComponent(() => import('../pages/Conversations/Conversations'));
        break;
      case 'team':
        preloadComponent(() => import('../pages/TeamManagement/TeamManagement'));
        break;
      case 'archived':
        preloadComponent(() => import('../pages/ArchivedProjects'));
        break;
      case 'subscription':
        preloadComponent(() => import('../pages/Subscription/SubscriptionManagement'));
        break;
      default:
        console.log(`[RoutePreloader] No preloader defined for route: ${routeName}`);
    }
  },

  // Preload route when user starts typing in search/navigation
  onSearchFocus: () => {
    preloadComponent(() => import('../pages/Dashboard/Dashboard'));
    preloadComponent(() => import('../pages/Conversations/Conversations'));
  },

  // Preload routes based on user behavior patterns
  onUserAction: (action: string) => {
    switch (action) {
      case 'project-created':
        preloadComponent(() => import('../pages/ProjectDetail/ProjectDetail'));
        preloadComponent(() => import('../pages/ProjectLog/ProjectLog'));
        break;
      case 'project-viewed':
        preloadComponent(() => import('../pages/ProjectLog/ProjectLog'));
        preloadComponent(() => import('../pages/ProjectEdit/ProjectEdit'));
        break;
      default:
        console.log(`[RoutePreloader] No preloader defined for action: ${action}`);
    }
  }
};
