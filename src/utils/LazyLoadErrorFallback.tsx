// Error fallback component for lazy loading failures
const LazyLoadErrorFallback = () => (
  <div className="min-h-screen bg-white flex items-center justify-center">
    <div className="text-center space-y-4 max-w-md mx-auto px-4">
      <div className="text-6xl">⚠️</div>
      <h2 className="text-xl font-semibold text-jobblogg-text-strong">
        Kunne ikke laste siden
      </h2>
      <p className="text-jobblogg-text-muted">
        Det oppstod en feil ved lasting av siden. Prøv å oppdatere siden eller kontakt support hvis problemet vedvarer.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary-hover transition-colors"
      >
        Oppdater siden
      </button>
    </div>
  </div>
);

export default LazyLoadErrorFallback;
