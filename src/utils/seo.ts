/**
 * SEO utilities for managing search engine indexing
 */

/**
 * Determines if the current environment should be indexed by search engines
 */
export function shouldAllowIndexing(): boolean {
  const hostname = window.location.hostname;
  
  // Only allow indexing on production domain
  return hostname === 'jobblogg.no' || hostname === 'www.jobblogg.no';
}

/**
 * Sets up meta tags to prevent search engine indexing on staging environments
 */
export function setupSEOTags(): void {
  if (!shouldAllowIndexing()) {
    // Add noindex meta tag for staging environments
    const noIndexMeta = document.createElement('meta');
    noIndexMeta.name = 'robots';
    noIndexMeta.content = 'noindex, nofollow, noarchive, nosnippet';
    document.head.appendChild(noIndexMeta);

    // Add staging indicator to title
    const title = document.title;
    if (!title.includes('[STAGING]')) {
      document.title = `[STAGING] ${title}`;
    }
  }
}
