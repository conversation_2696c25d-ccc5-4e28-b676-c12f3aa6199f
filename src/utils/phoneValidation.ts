/**
 * Validates Norwegian phone number
 * @param digits - Raw digits (8 digits expected)
 * @param type - Phone type ('mobile' or 'landline')
 * @returns Validation result with error message if invalid
 */
export const validateNorwegianPhone = (digits: string, type: 'mobile' | 'landline' = 'mobile'): { isValid: boolean; error?: string } => {
  if (!digits) {
    return { isValid: false, error: 'Telefonnummer er påkrevd' };
  }

  if (digits.length < 8) {
    return { isValid: false, error: `Telefonnummer må være 8 siffer (du har skrevet ${digits.length} siffer)` };
  }

  if (digits.length > 8) {
    return { isValid: false, error: 'Telefonnummer kan ikke være mer enn 8 siffer' };
  }

  if (type === 'mobile') {
    // Norwegian mobile numbers typically start with 4, 5, 9
    if (!/^[4-9]/.test(digits)) {
      return { isValid: false, error: 'Ugyldig norsk mobilnummer' };
    }
  } else if (type === 'landline') {
    // Norwegian landline numbers typically start with 2, 3, 5, 6, 7
    if (!/^[2-7]/.test(digits)) {
      return { isValid: false, error: 'Ugyldig norsk telefonnummer' };
    }
  }

  return { isValid: true };
};

/**
 * Formats a Norwegian phone number with progressive formatting
 * @param input - Raw phone number input (digits only)
 * @returns Formatted phone number (XXX XX XXX pattern)
 */
export const formatNorwegianPhone = (input: string): string => {
  // Remove all non-digits
  const digits = input.replace(/\D/g, '');

  // Limit to 8 digits (Norwegian mobile numbers)
  const limitedDigits = digits.slice(0, 8);

  // Apply progressive formatting
  if (limitedDigits.length <= 3) {
    return limitedDigits;
  } else if (limitedDigits.length <= 5) {
    return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
  } else {
    return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 5)} ${limitedDigits.slice(5)}`;
  }
};

/**
 * Extracts raw digits from formatted phone number
 * @param formatted - Formatted phone number
 * @returns Raw digits only
 */
export const extractDigits = (formatted: string): string => {
  return formatted.replace(/\D/g, '');
};
