/**
 * Norwegian Counties (Fylker) and Postal Code Mapping
 * Based on the 2020 administrative reform
 */

export interface NorwegianCounty {
  id: string;
  name: string;
  postalCodeRanges: Array<{ start: number; end: number }>;
}

export const NORWEGIAN_COUNTIES: NorwegianCounty[] = [
  {
    id: 'oslo',
    name: 'Oslo',
    postalCodeRanges: [
      { start: 1, end: 1299 }
    ]
  },
  {
    id: 'viken',
    name: '<PERSON>ike<PERSON>',
    postalCodeRanges: [
      { start: 1300, end: 1999 }, // Akershus
      { start: 3000, end: 3999 }, // Buskerud
      { start: 1400, end: 1499 }, // Follo
      { start: 1500, end: 1799 }, // Romerike
      { start: 1800, end: 1999 }  // Østfold parts
    ]
  },
  {
    id: 'innlandet',
    name: 'Innlandet',
    postalCodeRanges: [
      { start: 2000, end: 2999 }, // Hedmark
      { start: 2600, end: 2699 }  // Oppland parts
    ]
  },
  {
    id: 'vestfold-og-telemark',
    name: 'Vestfold og Telemark',
    postalCodeRanges: [
      { start: 3100, end: 3299 }, // Vestfold
      { start: 3600, end: 3999 }  // Telemark
    ]
  },
  {
    id: 'agder',
    name: '<PERSON><PERSON><PERSON>',
    postalCodeRanges: [
      { start: 4000, end: 4999 }  // Agder (Vest-Agder + Aust-Agder)
    ]
  },
  {
    id: 'rogaland',
    name: 'Rogaland',
    postalCodeRanges: [
      { start: 4000, end: 4299 }, // Stavanger area
      { start: 4300, end: 4399 }, // Sandnes area
      { start: 4400, end: 4699 }  // Rest of Rogaland
    ]
  },
  {
    id: 'vestland',
    name: 'Vestland',
    postalCodeRanges: [
      { start: 5000, end: 6999 }  // Bergen + Sogn og Fjordane + Hordaland
    ]
  },
  {
    id: 'more-og-romsdal',
    name: 'Møre og Romsdal',
    postalCodeRanges: [
      { start: 6000, end: 6499 }  // Møre og Romsdal
    ]
  },
  {
    id: 'trondelag',
    name: 'Trøndelag',
    postalCodeRanges: [
      { start: 7000, end: 7999 }  // Trøndelag (Nord-Trøndelag + Sør-Trøndelag)
    ]
  },
  {
    id: 'nordland',
    name: 'Nordland',
    postalCodeRanges: [
      { start: 8000, end: 8999 }  // Nordland
    ]
  },
  {
    id: 'troms-og-finnmark',
    name: 'Troms og Finnmark',
    postalCodeRanges: [
      { start: 9000, end: 9999 }  // Troms og Finnmark
    ]
  }
];

/**
 * Extract postal code from Norwegian address string
 */
export function extractPostalCodeFromAddress(address: string): number | null {
  if (!address) return null;
  
  // Look for 4-digit postal code pattern
  const postalCodeMatch = address.match(/\b(\d{4})\b/);
  if (postalCodeMatch) {
    return parseInt(postalCodeMatch[1], 10);
  }
  
  return null;
}

/**
 * Map postal code to Norwegian county
 */
export function getCountyFromPostalCode(postalCode: number): string | null {
  for (const county of NORWEGIAN_COUNTIES) {
    for (const range of county.postalCodeRanges) {
      if (postalCode >= range.start && postalCode <= range.end) {
        return county.id;
      }
    }
  }
  return null;
}

/**
 * Extract county from address string
 */
export function extractCountyFromAddress(address: string): string | null {
  const postalCode = extractPostalCodeFromAddress(address);
  if (!postalCode) return null;
  
  return getCountyFromPostalCode(postalCode);
}

/**
 * Get county name by ID
 */
export function getCountyNameById(countyId: string): string | null {
  const county = NORWEGIAN_COUNTIES.find(c => c.id === countyId);
  return county ? county.name : null;
}

/**
 * Extract county from Brønnøysundregisteret address data
 * Handles both forretningsadresse and postadresse formats
 */
export function extractCountyFromBrregAddress(addressData: any): string | null {
  if (!addressData) return null;
  
  // Try postal code first
  const postalCode = addressData.postnummer || addressData.poststed;
  if (postalCode && typeof postalCode === 'string') {
    const numericPostalCode = parseInt(postalCode, 10);
    if (!isNaN(numericPostalCode)) {
      return getCountyFromPostalCode(numericPostalCode);
    }
  }
  
  // Fallback to full address string
  const fullAddress = [
    addressData.adresse?.[0],
    addressData.postnummer,
    addressData.poststed
  ].filter(Boolean).join(' ');
  
  if (fullAddress) {
    return extractCountyFromAddress(fullAddress);
  }
  
  return null;
}
