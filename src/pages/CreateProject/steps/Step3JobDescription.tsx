import React, { useState, useRef, useEffect } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { useUser } from '@clerk/clerk-react'; // TODO: Use for user context
// import { api } from '../../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { TextArea, PrimaryButton, FormError } from '../../../components/ui';
import { useContractorCompanySimple } from '../../../components/ContractorOnboardingGuardSimple';

// Local interface definition to avoid import issues
interface WizardFormData {
  projectName: string;
  description: string;
  customerName: string;
  customerType: 'privat' | 'bedrift';
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  orgNumber: string;
  notes: string;
  jobDescription: string;
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
  selectedImages: string[];
  imageFiles: File[];
  sendCustomerNotification: boolean;
}

interface Step3JobDescriptionProps {
  formData: WizardFormData;
  updateFormData: (updates: Partial<WizardFormData>) => void;
  errors: { [key: string]: string };
  onPrevious: () => void;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
  updateProjectJobData: any;
  generateUploadUrl: any;
  storeJobImage: any;
  createProject: any;
  createCustomer: any;
  sendCustomerNotificationEmail: any;
  updateSharingSettings: any;
  user: any;
  clearSavedData: () => void;
  setShowSuccess: (value: boolean) => void;
  navigate: (path: string) => void;
  createdProjectId: string | null;
  setCustomerNotificationSent: (value: boolean) => void;
  useExistingCustomer: boolean;
  selectedCustomerId: string;
  // Brønnøysundregisteret data tracking
  brregData: any;
  brregFetchedAt: number | null;
  useCustomAddress: boolean;
}

export const Step3JobDescription: React.FC<Step3JobDescriptionProps> = ({
  formData,
  updateFormData,
  errors,
  onPrevious,
  isLoading,
  setIsLoading,
  setErrors,
  updateProjectJobData,
  generateUploadUrl,
  storeJobImage,
  createProject,
  createCustomer,
  sendCustomerNotificationEmail,
  updateSharingSettings,
  user,
  clearSavedData,
  setShowSuccess,
  navigate,
  createdProjectId,
  setCustomerNotificationSent,
  useExistingCustomer,
  selectedCustomerId,
  // Brønnøysundregisteret data tracking
  brregData,
  brregFetchedAt,
  useCustomAddress
}) => {
  // Get contractor company information for email notifications
  const { company: contractorCompany } = useContractorCompanySimple();

  // User-specific personal notes mutation
  // TODO: Re-enable when type instantiation issue is resolved
  // const updateUserProjectNotes = useMutation(api.userProjectNotes.updateUserProjectNotes);
  const updateUserProjectNotes = async (args: any) => {
    console.log("⚠️ Update user project notes temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Image handling refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  // Convert base64 strings back to File objects for display
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  // Initialize image previews from formData on component mount
  useEffect(() => {
    if (formData.selectedImages.length > 0) {
      setImagePreviews(formData.selectedImages);
    }
  }, [formData.selectedImages]);

  // Helper function to convert File to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  // Helper function to convert base64 to File
  const base64ToFile = (base64: string, filename: string): File => {
    try {
      // Validate base64 string
      if (!base64 || !base64.includes(',')) {
        throw new Error('Invalid base64 string');
      }

      const arr = base64.split(',');
      if (arr.length !== 2) {
        throw new Error('Malformed base64 string');
      }

      // Extract MIME type
      const mimeMatch = arr[0].match(/:(.*?);/);
      const mime = mimeMatch?.[1] || 'image/jpeg';

      // Validate MIME type
      if (!mime.startsWith('image/')) {
        throw new Error(`Invalid MIME type: ${mime}`);
      }

      // Decode base64
      const bstr = atob(arr[1]);
      const n = bstr.length;
      const u8arr = new Uint8Array(n);

      for (let i = 0; i < n; i++) {
        u8arr[i] = bstr.charCodeAt(i);
      }

      // Create file with proper extension based on MIME type
      const extension = mime.split('/')[1] || 'jpg';
      const finalFilename = filename.includes('.') ? filename : `${filename}.${extension}`;

      const file = new File([u8arr], finalFilename, { type: mime });

      console.log('🔄 Converted base64 to file:', finalFilename, 'Type:', mime, 'Size:', file.size);

      return file;
    } catch (error) {
      console.error('❌ Error converting base64 to file:', error);
      throw new Error(`Failed to convert base64 to file: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Handle image selection from gallery
  const handleImageSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    try {
      // Convert files to base64 for persistence
      const base64Images = await Promise.all(files.map(file => fileToBase64(file)));

      // Update formData with new images
      updateFormData({
        selectedImages: [...formData.selectedImages, ...base64Images],
        imageFiles: [...formData.imageFiles, ...files]
      });

      // Update previews
      setImagePreviews(prev => [...prev, ...base64Images]);

      // Reset input
      if (event.target) {
        event.target.value = '';
      }
    } catch (error) {
      console.error('Error processing images:', error);
    }
  };

  // Handle camera capture
  const handleCameraCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleImageSelect(event);
  };

  // Remove image
  const removeImage = (index: number) => {
    // Update formData by removing the image at the specified index
    updateFormData({
      selectedImages: formData.selectedImages.filter((_, i) => i !== index),
      imageFiles: formData.imageFiles.filter((_, i) => i !== index)
    });

    // Update previews
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  // Cleanup is not needed for base64 images as they don't create object URLs

  // Create complete project with job data
  const handleCreateCompleteProject = async () => {
    console.log('🚀 Starting project creation process...');
    setIsLoading(true);
    setErrors({});

    try {
      if (!user?.id) {
        console.error('❌ User not authenticated');
        setIsLoading(false);
        return;
      }

      console.log('✅ User authenticated:', user.id);

      let projectId = createdProjectId;

      // If no project was created in step 2, create it now
      if (!projectId) {
        let customerId;

        if (useExistingCustomer) {
          customerId = selectedCustomerId;
        } else {
          // Create new customer
          customerId = await createCustomer({
            name: formData.customerName.trim(),
            type: formData.customerType,
            contactPerson: formData.contactPerson.trim() || undefined,
            phone: formData.phone.trim() || undefined,
            email: formData.email.trim() || undefined,
            // Use new structured address fields
            streetAddress: formData.streetAddress.trim(),
            postalCode: formData.postalCode.trim(),
            city: formData.city.trim(),
            entrance: formData.entrance.trim() || undefined,
            orgNumber: formData.orgNumber.trim() || undefined,
            notes: formData.notes.trim() || undefined,
            // Brønnøysundregisteret data tracking
            brregFetchedAt: brregFetchedAt || undefined,
            brregData: brregData || undefined,
            useCustomAddress: useCustomAddress || undefined,
            userId: user.id
          });
        }

        // Create project with customer reference
        console.log('📝 Creating project with data:', {
          name: formData.projectName.trim(),
          description: formData.description.trim(),
          userId: user.id,
          customerId: customerId
        });

        const projectResult = await createProject({
          name: formData.projectName.trim(),
          description: formData.description.trim(),
          userId: user.id,
          customerId: customerId as any
        });

        // Handle the new return format from createProject
        let sharedId: string | null = null;
        if (typeof projectResult === 'object' && projectResult.projectId) {
          projectId = projectResult.projectId;
          sharedId = projectResult.sharedId; // Store sharedId for customer notification
        } else {
          // Fallback for backward compatibility
          projectId = projectResult;
          sharedId = null;
        }

        console.log('✅ Project created with ID:', projectId);

        // Send customer notification email if enabled and we have the necessary data
        if (formData.sendCustomerNotification && formData.email && contractorCompany && sharedId) {
          try {
            console.log('📧 Sending customer notification email...');

            // First, auto-enable project sharing for customer access
            await updateSharingSettings({
              projectId: projectId as any,
              userId: user.id,
              isPubliclyShared: true,
              shareSettings: {
                showContractorNotes: false, // Keep contractor notes private
                accessCount: 0,
                lastAccessedAt: undefined
              }
            });

            console.log('✅ Project sharing enabled automatically');

            // Construct shared project URL using the actual sharedId
            const sharedProjectUrl = `${window.location.origin}/shared/${sharedId}`;

            await sendCustomerNotificationEmail({
              customerEmail: formData.email.trim(),
              customerName: formData.customerName.trim(),
              customerType: formData.customerType,
              contactPerson: formData.contactPerson.trim() || undefined,
              projectName: formData.projectName.trim(),
              projectDescription: formData.description.trim(),
              contractorCompanyName: contractorCompany.name || 'Ukjent bedrift',
              contractorContactPerson: contractorCompany.contactPerson || user.fullName || 'Kontaktperson',
              contractorPhone: contractorCompany.phone || undefined,
              contractorEmail: contractorCompany.email || user.primaryEmailAddress?.emailAddress || undefined,
              sharedProjectUrl: sharedProjectUrl,
              // Tracking parameters
              userId: user.id,
              projectId: projectId as any,
              customerId: customerId as any,
            });

            console.log('✅ Customer notification email sent successfully');
            setCustomerNotificationSent(true);
          } catch (emailError) {
            console.error('⚠️ Failed to send customer notification email:', emailError);
            // Don't fail the entire project creation if email fails
            // Just log the error and continue
            setCustomerNotificationSent(false);
          }
        } else if (formData.sendCustomerNotification && !sharedId) {
          console.warn('⚠️ Customer notification requested but no sharedId available');
        }
      }

      // Add job data to the project if any job information is provided
      const hasJobData = formData.jobDescription || formData.accessNotes ||
                        formData.equipmentNeeds || formData.unresolvedQuestions ||
                        formData.personalNotes || formData.imageFiles.length > 0;

      if (hasJobData && projectId) {
        // Upload images to Convex storage first
        const uploadedPhotos = [];

        // Convert base64 images back to File objects for upload
        let filesToUpload: File[] = [];

        if (formData.imageFiles.length > 0) {
          // Use existing File objects if available
          filesToUpload = formData.imageFiles;
          console.log('📤 Using existing File objects:', filesToUpload.length);
        } else if (formData.selectedImages.length > 0) {
          // Convert base64 to File objects
          console.log('🔄 Converting base64 images to files:', formData.selectedImages.length);
          try {
            filesToUpload = formData.selectedImages.map((base64, index) =>
              base64ToFile(base64, `image-${index}`)
            );
          } catch (error) {
            console.error('❌ Error converting base64 images:', error);
            // Continue without images rather than failing completely
            filesToUpload = [];
          }
        }

        console.log('📤 Total files to upload:', filesToUpload.length);

        for (const file of filesToUpload) {
          try {
            console.log('📤 Uploading image:', file.name, 'Type:', file.type, 'Size:', file.size);

            // Validate file before upload
            if (!file.type.startsWith('image/')) {
              console.warn('⚠️ Skipping non-image file:', file.name, file.type);
              continue;
            }

            if (file.size === 0) {
              console.warn('⚠️ Skipping empty file:', file.name);
              continue;
            }

            // Get upload URL from Convex
            const uploadUrl = await generateUploadUrl();
            console.log('📤 Got upload URL for:', file.name);

            // Upload the file to Convex storage
            const result = await fetch(uploadUrl, {
              method: 'POST',
              headers: { 'Content-Type': file.type },
              body: file,
            });

            console.log('📤 Upload response status:', result.status, 'for file:', file.name);

            if (!result.ok) {
              const errorText = await result.text();
              console.error('❌ Upload failed:', result.status, errorText);
              throw new Error(`Failed to upload image ${file.name}: ${result.status} ${errorText}`);
            }

            const responseData = await result.json();
            console.log('📤 Upload response data:', responseData);

            const { storageId } = responseData;

            if (!storageId) {
              throw new Error(`No storageId returned for ${file.name}`);
            }

            // Get the actual URL from Convex storage
            const { url } = await storeJobImage({
              projectId: projectId as any,
              userId: user.id,
              storageId
            });

            console.log('✅ Successfully uploaded and stored image:', file.name, 'URL:', url);

            uploadedPhotos.push({
              url,
              note: '', // Empty note for now
              capturedAt: Date.now()
            });
          } catch (error) {
            console.error('❌ Error uploading image:', file.name, error);
            // Continue with other images even if one fails
          }
        }

        await updateProjectJobData({
          projectId: projectId as any,
          userId: user.id,
          jobData: {
            jobDescription: formData.jobDescription,
            photos: uploadedPhotos,
            accessNotes: formData.accessNotes,
            equipmentNeeds: formData.equipmentNeeds,
            unresolvedQuestions: formData.unresolvedQuestions,
            // personalNotes removed - handled separately below
          }
        });

        // Save personal notes separately to user-specific storage
        if (formData.personalNotes.trim()) {
          await updateUserProjectNotes({
            projectId: projectId as any,
            userId: user.id,
            personalNotes: formData.personalNotes,
          });
        }
      }

      // Clear saved data and show success
      console.log('🎉 Project creation completed successfully!');
      clearSavedData();
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate('/');
      }, 6000); // Extended to 6 seconds for better UX

    } catch (error) {
      console.error('❌ Error creating complete project:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        formData: formData,
        userId: user?.id
      });
      setErrors({ general: 'Det oppstod en feil ved opprettelse av prosjektet. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Privacy Warning for Contractor Notes */}
      <div className="bg-jobblogg-accent-soft border border-jobblogg-accent/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 text-jobblogg-accent mt-0.5 flex-shrink-0">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-1">🔒 Prosjektnotater</h4>
            <p className="text-sm text-jobblogg-text-medium">
              Disse notatene er alltid kun synlige for deg som leverandør og deles aldri med kunder.
              Bruk dette for interne arbeidsnotater, tilgangsinformasjon og andre detaljer som skal holdes private.
            </p>
          </div>
        </div>
      </div>

      {/* Job Description */}
      <TextArea
        label="Detaljerte arbeidsinstruksjoner"
        placeholder="Beskriv steg-for-steg hva som skal gjøres, hvilke materialer som trengs, etc..."
        fullWidth
        rows={4}
        value={formData.jobDescription}
        onChange={(e) => updateFormData({ jobDescription: e.target.value })}
        helperText="💡 Tips: Vær så spesifikk som mulig - dette er dine arbeidsinstruksjoner"
      />

      {/* Access Notes */}
      <TextArea
        label="Tilgangsnotater"
        placeholder="Informasjon om tilgang til arbeidsstedet, nøkler, koder, etc..."
        fullWidth
        rows={3}
        value={formData.accessNotes}
        onChange={(e) => updateFormData({ accessNotes: e.target.value })}
        helperText="F.eks. 'Nøkkel under blomsterpotten', 'Ring på dørklokka', 'Kode til port: 1234'"
      />

      {/* Equipment Needs */}
      <TextArea
        label="Utstyrsbehov"
        placeholder="Liste over verktøy, materialer eller utstyr som trengs..."
        fullWidth
        rows={3}
        value={formData.equipmentNeeds}
        onChange={(e) => updateFormData({ equipmentNeeds: e.target.value })}
        helperText="F.eks. 'Borhammer', 'Stige 3m', 'Maling - hvit'"
      />

      {/* Unresolved Questions */}
      <TextArea
        label="Uavklarte spørsmål"
        placeholder="Spørsmål som må avklares med kunden før eller under jobben..."
        fullWidth
        rows={3}
        value={formData.unresolvedQuestions}
        onChange={(e) => updateFormData({ unresolvedQuestions: e.target.value })}
        helperText="F.eks. 'Hvilken farge på flisene?', 'Skal vi male taket også?'"
      />

      {/* Personal Notes */}
      <TextArea
        label="Personlige notater"
        placeholder="Dine egne notater og påminnelser for prosjektet..."
        fullWidth
        rows={3}
        value={formData.personalNotes}
        onChange={(e) => updateFormData({ personalNotes: e.target.value })}
        helperText="🔒 Private notater som kun du ser - ikke synlige for andre teammedlemmer"
      />

      {/* Image Capture Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-jobblogg-text-strong">Bilder fra befaring</h3>
          <div className="flex gap-2">
            {/* Camera Button */}
            <button
              type="button"
              onClick={() => cameraInputRef.current?.click()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-jobblogg-primary bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg hover:bg-jobblogg-primary/10 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Ta bilde
            </button>

            {/* Gallery Button */}
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-jobblogg-accent bg-jobblogg-accent-soft border border-jobblogg-accent/20 rounded-lg hover:bg-jobblogg-accent/10 focus:outline-none focus:ring-2 focus:ring-jobblogg-accent/20 transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Velg bilder
            </button>
          </div>
        </div>

        {/* Hidden File Inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageSelect}
          className="hidden"
        />
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraCapture}
          className="hidden"
        />

        {/* Image Previews */}
        {imagePreviews.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {imagePreviews.map((preview, index) => (
              <div key={index} className="relative group">
                <img
                  src={preview}
                  alt={`Forhåndsvisning ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border border-jobblogg-border"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-jobblogg-error text-white rounded-full flex items-center justify-center text-xs hover:bg-jobblogg-error-dark transition-colors duration-200 opacity-0 group-hover:opacity-100"
                  aria-label="Fjern bilde"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        {imagePreviews.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-jobblogg-border rounded-lg bg-jobblogg-background-soft">
            <svg className="w-12 h-12 mx-auto text-jobblogg-text-muted mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-jobblogg-text-muted text-sm">
              Ingen bilder valgt. Bruk knappene ovenfor for å legge til bilder.
            </p>
          </div>
        )}
      </div>

      {/* General Error */}
      {errors.general && <FormError message={errors.general} />}

      {/* Navigation */}
      <div className="flex justify-between pt-4">
        <PrimaryButton
          variant="secondary"
          onClick={onPrevious}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </PrimaryButton>

        <PrimaryButton
          onClick={handleCreateCompleteProject}
          disabled={isLoading}
          loading={isLoading}
          size="lg"
          className="btn-wizard-lg"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Opprett Prosjekt
        </PrimaryButton>
      </div>

      {/* Completion Summary */}
      <div className="bg-jobblogg-primary-soft rounded-lg p-4 mt-6">
        <h4 className="font-semibold text-jobblogg-text-strong mb-2">📋 Prosjektsammendrag</h4>
        <div className="space-y-1 text-sm text-jobblogg-text-muted">
          <p><strong>Prosjekt:</strong> {formData.projectName}</p>
          <p><strong>Kunde:</strong> {formData.customerName || 'Eksisterende kunde'}</p>
          <p><strong>Type:</strong> {formData.customerType === 'bedrift' ? 'Bedrift' : 'Privat'}</p>
          {formData.description && (
            <p><strong>Sammendrag:</strong> <span className="whitespace-pre-wrap break-words">{formData.description}</span></p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Step3JobDescription;
