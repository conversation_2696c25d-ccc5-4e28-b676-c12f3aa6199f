import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
// import { useQuery, useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { getSpecializationById } from '../../utils/specializations';
import {
  PageLayout,
  Heading2,
  BodyText,
  TextMuted,
  PrimaryButton,
  SecondaryButton,
  DangerButton,
  TextArea,
  FormError,
  Modal
} from '../../components/ui';

const InvitationDetail: React.FC = () => {
  const { invitationId } = useParams<{ invitationId: string }>();
  const { user } = useUser();
  const navigate = useNavigate();

  // State for response
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseType, setResponseType] = useState<'accept' | 'decline'>('accept');
  const [responseMessage, setResponseMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Mutations
  // TODO: Re-enable when type instantiation issue is resolved
  // const respondToInvitation = useMutation(api.subcontractorInvitations.respondToInvitation);
  const respondToInvitation = async (args: any) => {
    console.log("⚠️ Respond to invitation temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Query invitation details
  // TODO: Re-enable when type instantiation issue is resolved
  // const invitations = useQuery(
  //   api.subcontractorInvitations.getSubcontractorInvitations,
  //   user?.id ? {
  //     userId: user.id,
  //     status: 'all',
  //   } : "skip"
  // );
  const invitations: any[] = []; // Temporarily provide fallback array due to type instantiation issues

  const invitation = invitations?.find(inv => inv._id === invitationId);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatUrgency = (urgency?: string) => {
    switch (urgency) {
      case 'high': return { text: 'Høy prioritet', color: 'text-jobblogg-error', bg: 'bg-jobblogg-error/10' };
      case 'medium': return { text: 'Middels prioritet', color: 'text-jobblogg-warning', bg: 'bg-jobblogg-warning/10' };
      case 'low': return { text: 'Lav prioritet', color: 'text-jobblogg-success', bg: 'bg-jobblogg-success/10' };
      default: return { text: 'Ikke oppgitt', color: 'text-jobblogg-text-medium', bg: 'bg-jobblogg-neutral' };
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return { text: 'Venter på svar', color: 'text-jobblogg-warning', bg: 'bg-jobblogg-warning/10' };
      case 'accepted':
        return { text: 'Godtatt', color: 'text-jobblogg-success', bg: 'bg-jobblogg-success/10' };
      case 'declined':
        return { text: 'Avslått', color: 'text-jobblogg-error', bg: 'bg-jobblogg-error/10' };
      case 'expired':
        return { text: 'Utløpt', color: 'text-jobblogg-text-medium', bg: 'bg-jobblogg-text-medium/10' };
      default:
        return { text: 'Ukjent', color: 'text-jobblogg-text-medium', bg: 'bg-jobblogg-neutral' };
    }
  };

  const handleResponse = async (type: 'accept' | 'decline') => {
    setResponseType(type);
    setShowResponseModal(true);
    setError('');
    setResponseMessage('');
  };

  const submitResponse = async () => {
    if (!invitation || !user) return;

    setIsSubmitting(true);
    setError('');

    try {
      await respondToInvitation({
        invitationId: invitation._id as any,
        response: responseType,
        userId: user.id,
        responseMessage: responseMessage.trim() || undefined,
      });

      setShowResponseModal(false);
      
      // Navigate based on response
      if (responseType === 'accept') {
        navigate(`/project/${invitation.projectId}`);
      } else {
        navigate('/invitations');
      }
    } catch (err) {
      console.error('Failed to respond to invitation:', err);
      setError(err instanceof Error ? err.message : 'Kunne ikke sende svar');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!invitation) {
    return (
      <PageLayout containerWidth="narrow" showFooter={false}>
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <Heading2 className="mb-4">Invitasjon ikke funnet</Heading2>
          <BodyText className="mb-6">
            Invitasjonen du leter etter finnes ikke eller du har ikke tilgang til den.
          </BodyText>
          <SecondaryButton onClick={() => navigate('/invitations')}>
            Tilbake til invitasjoner
          </SecondaryButton>
        </div>
      </PageLayout>
    );
  }

  const urgencyInfo = formatUrgency(invitation.urgency);
  const statusInfo = getStatusInfo(invitation.invitationStatus || 'pending');
  const isPending = invitation.invitationStatus === 'pending';
  const isExpired = invitation.expiresAt && Date.now() > invitation.expiresAt;

  return (
    <>
      <PageLayout containerWidth="wide" showFooter={false}>
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex items-start justify-between gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <SecondaryButton
                  onClick={() => navigate('/invitations')}
                  className="flex items-center gap-2"
                  size="sm"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Tilbake
                </SecondaryButton>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusInfo.bg} ${statusInfo.color}`}>
                  {statusInfo.text}
                </span>
              </div>
              <div className="flex items-center gap-3 mb-2">
                <Heading2>
                  {invitation.projectPreview?.name || 'Prosjektinvitasjon'}
                </Heading2>
                {invitation.invitationDirection === 'outgoing' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                    Sendt invitasjon
                  </span>
                )}
              </div>
              <TextMuted>
                {invitation.invitationDirection === 'outgoing'
                  ? `Sendt til ${invitation.subcontractorInfo?.company?.name || 'Ukjent firma'}`
                  : `Invitasjon fra ${invitation.projectPreview?.mainContractorCompany}`
                }
              </TextMuted>
            </div>

            {isPending && !isExpired && (
              <div className="flex items-center gap-3">
                <DangerButton
                  onClick={() => handleResponse('decline')}
                  size="sm"
                >
                  Avslå
                </DangerButton>
                <PrimaryButton
                  onClick={() => handleResponse('accept')}
                  size="sm"
                >
                  Godta invitasjon
                </PrimaryButton>
              </div>
            )}
          </div>

          {/* Expired Warning */}
          {isExpired && isPending && (
            <div className="bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <p className="font-medium text-jobblogg-error">Invitasjonen har utløpt</p>
                  <p className="text-sm text-jobblogg-text-medium">
                    Denne invitasjonen utløp {formatDate(invitation.expiresAt!)}. Kontakt {invitation.projectPreview?.inviterName} for en ny invitasjon.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Project Information */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-8">
            <div className="flex items-center justify-between mb-6">
              <Heading2 className="flex items-center gap-3">
                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                </svg>
                Prosjektinformasjon
              </Heading2>

              {/* Project Navigation Button */}
              {((invitation.invitationDirection === 'outgoing') ||
                (invitation.invitationDirection === 'incoming' && invitation.invitationStatus === 'accepted')) && (
                <PrimaryButton
                  onClick={() => navigate(`/project/${invitation.projectId}`)}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Gå til prosjekt
                </PrimaryButton>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-jobblogg-text-strong mb-2">Prosjektdetaljer</h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <div>
                        <p className="font-medium text-jobblogg-text-strong">Adresse</p>
                        <p className="text-jobblogg-text-medium">{invitation.projectPreview?.address || 'Ikke oppgitt'}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      <div>
                        <p className="font-medium text-jobblogg-text-strong">Kunde</p>
                        <p className="text-jobblogg-text-medium">{invitation.projectPreview?.customerName || 'Ikke oppgitt'}</p>
                      </div>
                    </div>
                    {invitation.projectPreview?.description && (
                      <div className="flex items-start gap-3">
                        <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <div>
                          <p className="font-medium text-jobblogg-text-strong">Prosjektbeskrivelse</p>
                          <p className="text-jobblogg-text-medium whitespace-pre-wrap">{invitation.projectPreview.description}</p>
                        </div>
                      </div>
                    )}
                    {invitation.startDate && (
                      <div className="flex items-start gap-3">
                        <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <div>
                          <p className="font-medium text-jobblogg-text-strong">Ønsket oppstart</p>
                          <p className="text-jobblogg-text-medium">{formatDate(invitation.startDate)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                    {invitation.invitationDirection === 'outgoing'
                      ? 'Underleverandørs rolle'
                      : 'Din rolle'
                    }
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 00-2 2H8a2 2 0 00-2-2V6m8 0h2a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h2" />
                      </svg>
                      <div>
                        <p className="font-medium text-jobblogg-text-strong">Spesialisering</p>
                        <p className="text-jobblogg-text-medium">{invitation.subcontractorSpecialization ? getSpecializationById(invitation.subcontractorSpecialization)?.name || invitation.subcontractorSpecialization : 'Ikke spesifisert'}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <p className="font-medium text-jobblogg-text-strong">Estimert varighet</p>
                        <p className="text-jobblogg-text-medium">{invitation.estimatedDuration || 'Ikke oppgitt'}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-jobblogg-text-medium mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <p className="font-medium text-jobblogg-text-strong">Prioritet</p>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${urgencyInfo.bg} ${urgencyInfo.color}`}>
                          {urgencyInfo.text}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Personal Message */}
            {invitation.invitationMessage && (
              <div className="mt-8 pt-6 border-t border-jobblogg-border">
                <h3 className="font-semibold text-jobblogg-text-strong mb-3">
                  Melding fra {invitation.projectPreview?.inviterName}
                </h3>
                <div className="bg-jobblogg-accent/10 border-l-4 border-jobblogg-accent p-4 rounded-r-lg">
                  <p className="text-jobblogg-text-medium whitespace-pre-wrap">{invitation.invitationMessage}</p>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Project Details */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-8">
            <Heading2 className="mb-6 flex items-center gap-3">
              <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              Detaljert prosjektinformasjon
            </Heading2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Project Scope and Requirements */}
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-jobblogg-text-strong mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Arbeidsomfang og krav
                  </h3>
                  <div className="space-y-4">
                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg">
                      <p className="font-medium text-jobblogg-text-strong mb-2">Ditt fagområde:</p>
                      <p className="text-jobblogg-text-medium">
                        {invitation.subcontractorSpecialization ? getSpecializationById(invitation.subcontractorSpecialization)?.name || invitation.subcontractorSpecialization : 'Ikke spesifisert'}
                      </p>
                    </div>

                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg">
                      <p className="font-medium text-jobblogg-text-strong mb-2">Forventet tidsramme:</p>
                      <p className="text-jobblogg-text-medium">
                        {invitation.estimatedDuration || 'Ikke spesifisert'}
                      </p>
                      {invitation.startDate && (
                        <p className="text-sm text-jobblogg-text-muted mt-1">
                          Ønsket oppstart: {formatDate(invitation.startDate)}
                        </p>
                      )}
                    </div>

                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg">
                      <p className="font-medium text-jobblogg-text-strong mb-2">Prioritetsnivå:</p>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${urgencyInfo.bg} ${urgencyInfo.color}`}>
                        {urgencyInfo.text}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Project Context and Expectations */}
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-jobblogg-text-strong mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Prosjektsammenheng
                  </h3>
                  <div className="space-y-4">
                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg">
                      <p className="font-medium text-jobblogg-text-strong mb-2">Prosjekttype:</p>
                      <p className="text-jobblogg-text-medium">
                        Underleverandøroppdrag for {invitation.projectPreview?.mainContractorCompany}
                      </p>
                    </div>

                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg">
                      <p className="font-medium text-jobblogg-text-strong mb-2">Kunde:</p>
                      <p className="text-jobblogg-text-medium">
                        {invitation.projectPreview?.customerName || 'Ikke oppgitt'}
                      </p>
                    </div>

                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg">
                      <p className="font-medium text-jobblogg-text-strong mb-2">Arbeidssted:</p>
                      <p className="text-jobblogg-text-medium">
                        {invitation.projectPreview?.address || 'Ikke oppgitt'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Important Information Box */}
            <div className="mt-8 bg-jobblogg-accent/5 border border-jobblogg-accent/20 rounded-lg p-6">
              <div className="flex items-start gap-3">
                <svg className="w-6 h-6 text-jobblogg-accent mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="flex-1">
                  <h4 className="font-semibold text-jobblogg-accent mb-2">Viktig informasjon</h4>
                  <ul className="text-sm text-jobblogg-text-medium space-y-2">
                    <li>• Ved å godta denne invitasjonen blir du en del av prosjektteamet</li>
                    <li>• Du får tilgang til prosjektdetaljer, kommunikasjon og dokumenter</li>
                    <li>• Kontraktuelle forhold og betaling avtales direkte med hovedentreprenøren</li>
                    <li>• Du kan trekke deg fra prosjektet ved å kontakte hovedentreprenøren</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-8">
            <Heading2 className="mb-6 flex items-center gap-3">
              <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Kontaktinformasjon
            </Heading2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {invitation.invitationDirection === 'outgoing' ? (
                // For outgoing invitations, show subcontractor information
                <>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong mb-3">Underleverandør</h3>
                    <div className="space-y-2">
                      <p className="text-jobblogg-text-strong">
                        {invitation.subcontractorInfo?.company?.name || 'Ukjent firma'}
                      </p>
                      <p className="text-jobblogg-text-medium">
                        Kontaktperson: {invitation.subcontractorInfo?.contactPersonName || 'Ukjent kontakt'}
                      </p>
                      {invitation.subcontractorInfo?.email && (
                        <p className="text-jobblogg-text-medium">
                          E-post: {invitation.subcontractorInfo.email}
                        </p>
                      )}
                      {invitation.subcontractorInfo?.phone && (
                        <p className="text-jobblogg-text-medium">
                          Telefon: {invitation.subcontractorInfo.phone}
                        </p>
                      )}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong mb-3">Invitasjonsdetaljer</h3>
                    <div className="space-y-2">
                      <p className="text-jobblogg-text-medium">
                        Sendt: {formatDate(invitation.invitedAt || invitation._creationTime)}
                      </p>
                      {invitation.expiresAt && (
                        <p className="text-jobblogg-text-medium">
                          Utløper: {formatDate(invitation.expiresAt)}
                        </p>
                      )}
                      {invitation.respondedAt && (
                        <p className="text-jobblogg-text-medium">
                          Besvart: {formatDate(invitation.respondedAt)}
                        </p>
                      )}
                    </div>
                  </div>
                </>
              ) : (
                // For incoming invitations, show main contractor information
                <>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                      </svg>
                      Hovedentreprenør
                    </h3>
                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg space-y-4">
                      <div>
                        <p className="font-medium text-jobblogg-text-strong text-lg">
                          {invitation.projectPreview?.mainContractorCompany}
                        </p>
                      </div>

                      <div className="pt-2 border-t border-jobblogg-border/50">
                        <p className="text-sm font-medium text-jobblogg-text-strong mb-3">
                          👤 Din kontaktperson:
                        </p>
                        <div className="bg-white p-3 rounded-lg border border-jobblogg-border/30">
                          <p className="font-semibold text-jobblogg-text-strong">
                            {invitation.projectPreview?.inviterName}
                          </p>
                          {invitation.projectPreview?.inviterRole && (
                            <p className="text-sm text-jobblogg-text-muted">
                              {invitation.projectPreview.inviterRole}
                            </p>
                          )}

                          <div className="mt-3 space-y-2">
                            {invitation.projectPreview?.inviterEmail && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <a
                                  href={`mailto:${invitation.projectPreview.inviterEmail}`}
                                  className="text-sm text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors"
                                >
                                  {invitation.projectPreview.inviterEmail}
                                </a>
                              </div>
                            )}

                            {invitation.projectPreview?.inviterPhone && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <a
                                  href={`tel:${invitation.projectPreview.inviterPhone}`}
                                  className="text-sm text-jobblogg-accent hover:text-jobblogg-accent-dark transition-colors"
                                >
                                  {invitation.projectPreview.inviterPhone}
                                </a>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="pt-2 border-t border-jobblogg-border/50">
                        <p className="text-xs font-medium text-jobblogg-text-strong mb-2">
                          💬 Kontaktmuligheter:
                        </p>
                        <div className="space-y-1 text-xs text-jobblogg-text-medium">
                          <p>• Ring eller send e-post direkte til {invitation.projectPreview?.inviterName}</p>
                          <p>• Svar på denne invitasjonen med spørsmål</p>
                          <p>• Bruk kontaktinformasjonen ovenfor for rask kontakt</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Tidsfrister
                    </h3>
                    <div className="bg-jobblogg-neutral/50 p-4 rounded-lg space-y-3">
                      <div>
                        <p className="font-medium text-jobblogg-text-strong">Invitasjon sendt:</p>
                        <p className="text-jobblogg-text-medium">
                          {formatDate(invitation.invitedAt || invitation._creationTime)}
                        </p>
                      </div>

                      {invitation.expiresAt && (
                        <div>
                          <p className="font-medium text-jobblogg-text-strong">Svarfrist:</p>
                          <p className={`text-sm font-medium ${isExpired ? 'text-jobblogg-error' : 'text-jobblogg-warning'}`}>
                            {formatDate(invitation.expiresAt)}
                            {!isExpired && (
                              <span className="block text-xs text-jobblogg-text-muted mt-1">
                                {Math.ceil((invitation.expiresAt - Date.now()) / (1000 * 60 * 60 * 24))} dager igjen
                              </span>
                            )}
                          </p>
                        </div>
                      )}

                      {invitation.respondedAt && (
                        <div>
                          <p className="font-medium text-jobblogg-text-strong">Besvart:</p>
                          <p className="text-jobblogg-text-medium">
                            {formatDate(invitation.respondedAt)}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Response Message */}
          {invitation.responseMessage && (
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-8">
              <Heading2 className="mb-4">
                {invitation.invitationDirection === 'outgoing'
                  ? 'Underleverandørs svar'
                  : 'Ditt svar'
                }
              </Heading2>
              <div className="bg-jobblogg-neutral p-4 rounded-lg">
                <p className="text-jobblogg-text-medium">{invitation.responseMessage}</p>
              </div>
            </div>
          )}
        </div>
      </PageLayout>

      {/* Response Modal */}
      <Modal
        isOpen={showResponseModal}
        onClose={() => setShowResponseModal(false)}
        title={responseType === 'accept' ? 'Godta invitasjon' : 'Avslå invitasjon'}
      >
        <div className="space-y-6">
          <BodyText>
            {responseType === 'accept' 
              ? 'Er du sikker på at du vil godta denne prosjektinvitasjonen? Prosjektet vil bli tilgjengelig i din oversikt.'
              : 'Er du sikker på at du vil avslå denne prosjektinvitasjonen? Du kan ikke angre denne handlingen.'
            }
          </BodyText>

          <div>
            <label htmlFor="responseMessage" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
              Melding til hovedentreprenør (valgfritt)
            </label>
            <TextArea
              id="responseMessage"
              value={responseMessage}
              onChange={(e) => setResponseMessage(e.target.value)}
              placeholder={responseType === 'accept' 
                ? 'F.eks. "Takk for invitasjonen! Jeg ser frem til å jobbe med dere på dette prosjektet."'
                : 'F.eks. "Takk for invitasjonen, men jeg har dessverre ikke kapasitet til dette prosjektet."'
              }
              rows={3}
              disabled={isSubmitting}
            />
          </div>

          {error && <FormError>{error}</FormError>}

          <div className="flex items-center justify-end gap-3">
            <SecondaryButton
              onClick={() => setShowResponseModal(false)}
              disabled={isSubmitting}
            >
              Avbryt
            </SecondaryButton>
            {responseType === 'accept' ? (
              <PrimaryButton
                onClick={submitResponse}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Godtar...' : 'Godta invitasjon'}
              </PrimaryButton>
            ) : (
              <DangerButton
                onClick={submitResponse}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Avslår...' : 'Avslå invitasjon'}
              </DangerButton>
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default InvitationDetail;
