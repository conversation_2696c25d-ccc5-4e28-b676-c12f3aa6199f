import React, { useState } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { useSubscriptionAccess, useSeatManagement } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { PageLayout, Heading2, BodyText, TextMuted, PrimaryButton, SecondaryButton } from '../../components/ui';

const SubscriptionManagementFixed: React.FC = () => {
  console.log('🔥 Frontend: SubscriptionManagement component loaded');
  const { user } = useUser();
  const navigate = useNavigate();
  const { isAdministrator, user: userWithRole, isLoading: roleLoading } = useUserRole();
  const { subscription, isInTrial, isLoading: subLoading } = useSubscriptionAccess();

  // Get accurate team member count
  // TODO: Re-enable when type instantiation issue is resolved
  // const teamMemberCount = useQuery(
  //   api.seatManagement.getTeamMemberCount,
  //   user?.id ? { userId: user.id } : "skip"
  // );
  const teamMemberCount = {
    count: 0,
    actualTeamMemberCount: 0,
    maxSeats: 0
  }; // Temporarily provide fallback object due to type instantiation issues

  const { currentSeats: fallbackCurrentSeats, maxSeats: fallbackMaxSeats, isLoading: seatLoading } = useSeatManagement();
  // TODO: Re-enable when type instantiation issue is resolved
  // const createPortalSession = useMutation(api.subscriptions.createPortalSession);
  const createPortalSession = async (args: any) => {
    console.log("⚠️ Create portal session temporarily disabled due to type issues", args);
    return { url: '' };
  }; // Temporarily mock mutation
  // TODO: Re-enable when type instantiation issue is resolved
  // const createTrialSubscription = useMutation(api.subscriptions.createTrialSubscription);
  const createTrialSubscription = async (args: any) => {
    console.log("⚠️ Create trial subscription temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation
  const [isUpgrading, setIsUpgrading] = useState(false);

  // Use accurate count if available, fallback to seat management hook
  const currentSeats = teamMemberCount?.actualTeamMemberCount || fallbackCurrentSeats || 0;
  const maxSeats = teamMemberCount?.maxSeats || fallbackMaxSeats || 0;

  // Show loading state while checking permissions
  if (roleLoading || subLoading) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                <TextMuted>Sjekker tilganger...</TextMuted>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Debug information (remove in production)
  console.log('Debug - Subscription Management State:', {
    user: user?.id,
    isAdministrator,
    subscription: subscription ? 'Found' : 'Not found',
    isInTrial,
    subscriptionDetails: subscription ? {
      id: subscription._id,
      userId: subscription.userId,
      stripeCustomerId: subscription.stripeCustomerId,
      status: subscription.status,
      planLevel: subscription.planLevel
    } : null
  });

  // Check administrator access
  if (!isAdministrator) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <Heading2 className="mb-4">Kun for administratorer</Heading2>
              <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
                Denne siden er kun tilgjengelig for brukere med administratorrettigheter. 
                Kontakt din administrator hvis du trenger tilgang.
              </BodyText>
              <BodyText className="text-sm text-jobblogg-text-muted">
                Du er logget inn som {userWithRole?.role === 'prosjektleder' ? 'prosjektleder' : 'utførende bruker'}
              </BodyText>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Define handleUpgrade function before it's used
  const handleUpgrade = async () => {
    if (!user) {
      console.error('No user found');
      return;
    }

    console.log('🔥 Frontend: Starting upgrade process for user:', user.id);
    console.log('🔥 Frontend: createPortalSession function:', createPortalSession);
    setIsUpgrading(true);

    try {
      console.log('🔥 Frontend: Calling createPortalSession...');
      const result = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });

      console.log('Portal session result:', result);

      if (result?.url) {
        console.log('Redirecting to:', result.url);
        window.location.href = result.url;
      } else {
        console.error('No URL returned from createPortalSession');
        setIsUpgrading(false);
      }
    } catch (error: unknown) {
      console.error('Failed to create portal session:', error);

      // If no subscription found, try to create a trial subscription first
      if ((error as any).message?.includes('No subscription found')) {
        console.log('No subscription found, creating trial subscription...');
        try {
          const trialResult = await createTrialSubscription({
            userId: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}` || 'Bruker',
            email: user.primaryEmailAddress?.emailAddress || '',
            selectedPlan: 'basic'
          });

          console.log('Trial subscription created:', trialResult);

          // Now try to create portal session again
          const portalResult = await createPortalSession({
            userId: user.id,
            returnUrl: window.location.href
          });

          if (portalResult?.url) {
            window.location.href = portalResult.url;
          } else {
            setIsUpgrading(false);
            alert('Kunne ikke åpne kundeportalen. Prøv igjen senere.');
          }
        } catch (trialError) {
          console.error('Failed to create trial subscription:', trialError);
          setIsUpgrading(false);
          alert(`Test failed: ${(trialError as any).message}`);
        }
      } else {
        setIsUpgrading(false);
        alert('Det oppstod en feil. Prøv igjen senere.');
      }
    }
  };

  // No subscription state
  if (!subscription) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-jobblogg-primary-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <Heading2 className="mb-4">Ingen aktiv plan</Heading2>
              <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
                Du har ikke et aktivt abonnement. Start en gratis prøveperiode for å få tilgang til alle funksjoner.
              </BodyText>
              <PrimaryButton onClick={handleUpgrade} className="px-8">
                Start gratis prøveperiode
              </PrimaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }



  const handleViewAllPlans = () => {
    navigate('/pricing');
  };

  const handleTestSubscription = async () => {
    if (!user) {
      console.error('No user found for test');
      return;
    }

    console.log('Testing subscription lookup for user:', user.id);

    try {
      // Test the subscription query directly
      const testResult = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });

      console.log('Test result:', testResult);
      alert(`Test successful! URL: ${testResult.url}`);
    } catch (error) {
      console.error('Test failed:', error);
      alert(`Test failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Calculate trial progress
  const trialStart = subscription.trialStart || 0;
  const trialEnd = subscription.trialEnd || 0;
  const now = Date.now();
  const totalTrialDuration = trialEnd - trialStart;
  const elapsed = now - trialStart;
  const trialProgress = Math.min(100, Math.max(0, (elapsed / totalTrialDuration) * 100));
  const daysLeft = Math.max(0, Math.ceil((trialEnd - now) / (24 * 60 * 60 * 1000)));
  const isExpiringSoon = daysLeft <= 2;

  // Plan information
  const planNames = {
    basic: 'Liten bedrift',
    professional: 'Mellomstor bedrift',
    enterprise: 'Stor bedrift'
  };
  const currentPlan = {
    name: planNames[subscription.planLevel as keyof typeof planNames] || subscription.planLevel,
    level: subscription.planLevel,
    billing: subscription.billingInterval
  };

  return (
    <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
      <div className="bg-white rounded-xl shadow-soft p-8">
        <div className="max-w-2xl mx-auto">
          <div className="space-y-16">
            {/* Hero Section */}
            <section className="text-center space-y-8">
              <Heading2>Du er på {currentPlan.name}-planen</Heading2>

              {/* Trial Status */}
              {isInTrial && (
                <div className="max-w-sm mx-auto space-y-6">
                  <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium ${
                    isExpiringSoon
                      ? 'bg-jobblogg-warning-soft text-jobblogg-warning'
                      : 'bg-jobblogg-primary-soft text-jobblogg-primary'
                  }`}>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {daysLeft > 0
                      ? `${daysLeft} ${daysLeft === 1 ? 'dag' : 'dager'} igjen av prøveperioden`
                      : 'Prøveperioden utløper i dag'
                    }
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full bg-jobblogg-neutral rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
                      }`}
                      style={{ width: `${trialProgress}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row gap-6 justify-center">
                  <PrimaryButton 
                    onClick={handleUpgrade} 
                    disabled={isUpgrading}
                    className="min-w-[200px] px-8"
                  >
                    {isUpgrading ? 'Laster...' : (isInTrial ? 'Oppgrader nå' : 'Administrer abonnement')}
                  </PrimaryButton>
                  <SecondaryButton
                    onClick={handleViewAllPlans}
                    className="min-w-[200px] px-6"
                  >
                    {isInTrial ? 'Se alle planer' : 'Endre plan'}
                  </SecondaryButton>
                </div>

                {/* Debug Test Button - Remove in production */}
                <div className="flex justify-center">
                  <button
                    onClick={handleTestSubscription}
                    className="px-4 py-2 bg-gray-200 text-gray-800 rounded text-sm hover:bg-gray-300"
                  >
                    🔧 Test Subscription (Debug)
                  </button>
                </div>

                {/* Reassuring Microcopy */}
                {isInTrial && (
                  <TextMuted className="text-sm text-center">
                    Du kan endre plan når som helst i prøveperioden
                  </TextMuted>
                )}
              </div>
            </section>

            {/* Usage Section */}
            <section>
              <div className="bg-white rounded-xl shadow-soft p-6 border border-jobblogg-border">
                {!seatLoading && maxSeats > 0 && (
                  <div className="space-y-6">
                    {/* Two-Column Layout */}
                    <div className="flex flex-col sm:flex-row justify-between items-start gap-6">
                      {/* Left Column - Metric Display */}
                      <div className="flex-1">
                        <div className="text-4xl font-bold text-jobblogg-primary text-left">
                          {currentSeats}
                        </div>
                        <div className="text-sm text-jobblogg-text-muted text-left">
                          av {maxSeats} plasser brukt
                        </div>
                      </div>

                      {/* Right Column - Context and Action */}
                      <div className="flex-1 space-y-3">
                        <div className="font-medium text-jobblogg-text-strong text-left">
                          Team medlemmer
                        </div>
                        <div className="text-sm text-jobblogg-text-medium text-left">
                          {currentSeats >= maxSeats ? (
                            <span className="text-jobblogg-warning">Alle plasser brukt</span>
                          ) : (
                            `Du kan invitere ${maxSeats - currentSeats} flere medlemmer`
                          )}
                        </div>
                        <div className="text-left">
                          <SecondaryButton 
                            className="text-sm px-4 py-2 min-h-[44px]"
                            onClick={() => window.location.href = '/team'}
                          >
                            + Inviter medlem
                          </SecondaryButton>
                        </div>
                      </div>
                    </div>

                    {/* Full-Width Progress Bar */}
                    <div className="w-full bg-jobblogg-neutral rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          currentSeats >= maxSeats * 0.8 ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
                        }`}
                        style={{ width: `${Math.min(100, (currentSeats / maxSeats) * 100)}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </section>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default SubscriptionManagementFixed;
