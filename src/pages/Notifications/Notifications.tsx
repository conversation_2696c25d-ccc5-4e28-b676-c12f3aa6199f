import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
// import { useQuery, useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { Link } from 'react-router-dom';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { PageLayout } from '../../components/ui/Layout/PageLayout';
import { Heading2 } from '../../components/ui';

export const Notifications: React.FC = () => {
  const { user } = useUser();
  const [filter, setFilter] = useState<'all' | 'unread'>('unread');
  
  // Query comprehensive notification log
  // TODO: Re-enable when type instantiation issue is resolved
  // const notifications = useQuery(
  //   api.notifications.getNotificationLog,
  //   user?.id ? {
  //     userId: user.id,
  //     limit: 50,
  //     includeRead: filter === 'all',
  //   } : "skip"
  // );
  const notifications: any[] = []; // Temporarily provide fallback array due to type instantiation issues

  // Query notification statistics
  // TODO: Re-enable when type instantiation issue is resolved
  // const notificationStats = useQuery(
  //   api.notifications.getNotificationStats,
  //   user?.id ? { userId: user.id } : "skip"
  // );
  const notificationStats = { unreadCount: 0, totalCount: 0 }; // Temporarily provide fallback object due to type instantiation issues

  // TODO: Re-enable when type instantiation issue is resolved
  // const markAsRead = useMutation(api.notifications.markNotificationsAsRead);
  const markAsRead = async (args: any) => {
    console.log("⚠️ Mark as read temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  const formatTimeAgo = (timeAgo: number): string => {
    const minutes = Math.floor(timeAgo / (1000 * 60));
    const hours = Math.floor(timeAgo / (1000 * 60 * 60));
    const days = Math.floor(timeAgo / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Akkurat nå';
    if (minutes < 60) return `${minutes} min siden`;
    if (hours < 24) return `${hours} timer siden`;
    if (days === 1) return 'I går';
    if (days < 7) return `${days} dager siden`;
    
    return new Date(Date.now() - timeAgo).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'invitation_accepted':
        return (
          <div className="w-10 h-10 bg-jobblogg-accent/10 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-jobblogg-accent" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'invitation_declined':
        return (
          <div className="w-10 h-10 bg-jobblogg-error/10 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-jobblogg-error" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'chat_message':
        return (
          <div className="w-10 h-10 bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        );
      case 'project_update':
      case 'project_status_change':
        return (
          <div className="w-10 h-10 bg-jobblogg-warning/10 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
        );
      case 'team_invitation':
        return (
          <div className="w-10 h-10 bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-10 h-10 bg-jobblogg-neutral/10 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-jobblogg-text-muted" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  const getNotificationLink = (notification: any) => {
    switch (notification.type) {
      case 'invitation_accepted':
      case 'invitation_declined':
        if (notification.data?.projectId) {
          return `/project/${notification.data.projectId}`;
        }
        return '/invitations';
      case 'chat_message':
        if (notification.data?.projectId) {
          return `/project/${notification.data.projectId}#chat`;
        }
        return '/projects';
      case 'project_update':
      case 'project_status_change':
        if (notification.data?.projectId) {
          return `/project/${notification.data.projectId}`;
        }
        return '/projects';
      case 'team_invitation':
        return '/team';
      default:
        return '/';
    }
  };

  const handleNotificationClick = async (notification: any) => {
    if (!notification.isRead) {
      await markAsRead({
        userId: notification.userId,
        notificationIds: [notification._id],
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (user?.id) {
      await markAsRead({
        userId: user.id,
      });
    }
  };

  const unreadCount = notificationStats?.unreadCount || 0;
  const filteredNotifications = notifications || [];

  return (
    <PageLayout containerWidth="wide">
      <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Heading2>Varsler</Heading2>
            <p className="text-jobblogg-text-medium mt-2">
              {unreadCount > 0 ? `${unreadCount} uleste varsler` : 'Alle varsler er lest'}
            </p>
          </div>
          
          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="px-4 py-2 text-sm font-medium text-jobblogg-primary hover:text-jobblogg-primary-dark border border-jobblogg-primary hover:border-jobblogg-primary-dark rounded-lg transition-colors"
            >
              Merk alle som lest
            </button>
          )}
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-jobblogg-neutral/30 p-1 rounded-lg w-fit">
          <button
            onClick={() => setFilter('unread')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              filter === 'unread'
                ? 'bg-white text-jobblogg-text-strong shadow-sm'
                : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
            }`}
          >
            Uleste ({unreadCount})
          </button>
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              filter === 'all'
                ? 'bg-white text-jobblogg-text-strong shadow-sm'
                : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
            }`}
          >
            Alle ({notificationStats?.totalCount || 0})
          </button>
        </div>

        {/* Notifications List */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft">
          {filteredNotifications.length === 0 ? (
            <div className="p-12 text-center">
              <svg className="w-16 h-16 mx-auto mb-4 text-jobblogg-text-muted/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              <h3 className="text-lg font-medium text-jobblogg-text-strong mb-2">
                {filter === 'unread' ? 'Ingen uleste varsler' : 'Ingen varsler'}
              </h3>
              <p className="text-jobblogg-text-medium">
                {filter === 'unread' 
                  ? 'Du har ingen nye varsler å vise.' 
                  : 'Du har ikke mottatt noen varsler ennå.'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-jobblogg-border">
              {filteredNotifications.map((notification) => (
                <Link
                  key={notification._id}
                  to={getNotificationLink(notification)}
                  onClick={() => handleNotificationClick(notification)}
                  className={`block p-6 hover:bg-jobblogg-neutral/30 transition-colors ${
                    !notification.isRead ? 'bg-jobblogg-primary/5' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h3 className={`text-base font-medium ${
                          !notification.isRead ? 'text-jobblogg-text-strong' : 'text-jobblogg-text-medium'
                        }`}>
                          {notification.title}
                        </h3>
                        {!notification.isRead && (
                          <div className="w-3 h-3 bg-jobblogg-primary rounded-full flex-shrink-0 mt-1"></div>
                        )}
                      </div>
                      <p className="text-jobblogg-text-muted mt-1">
                        {notification.message}
                      </p>
                      <p className="text-sm text-jobblogg-text-muted mt-3">
                        {formatTimeAgo(notification.timeAgo)}
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
};
