import React, { useState, useEffect } from 'react';
import { Heading2, BodyText } from '../../../components/ui';
import { useUserRole } from '../../../hooks/useUserRole';

interface PlanTier {
  id: 'basic' | 'professional' | 'enterprise';
  name: string;
  description: string;
  employeeRange: string;
  monthlyPrice: number;
  annualPrice: number;
  monthlyPriceExclVat: number;
  annualPriceExclVat: number;
  annualEquivalentMonthly: number;
  annualEquivalentMonthlyExclVat: number;
  features: string[];
  isRecommended?: boolean;
}

interface Step5PlanSelectionProps {
  formData: any;
  onNext: () => void;
  onBack: () => void;
  onPlanSelect: (planLevel: string, billingInterval: 'month' | 'year') => void;
}

const pricingTiers: PlanTier[] = [
  {
    id: 'basic',
    name: 'Liten bedrift',
    description: 'Perfekt for enkeltmannsforetak og små team',
    employeeRange: '1–9 ansatte',
    monthlyPrice: 299,
    annualPrice: 239 * 12, // 2868
    monthlyPriceExclVat: 299,
    annualPriceExclVat: 239 * 12, // 2868
    annualEquivalentMonthly: 239,
    annualEquivalentMonthlyExclVat: 239,
    features: [
      'Ubegrenset prosjekter',
  'Prosjektlogg med bilder',
  'Chat med kunder og team',
  'Prosjektdeling med kunder',
  'Prosjektdeling med underleverandører',
  'Mobil app (iOS/Android)',
      '7 dagers gratis prøveperiode',
    ]
  },
  {
    id: 'professional',
    name: 'Mellomstor bedrift',
    description: 'For voksende entreprenørfirmaer',
    employeeRange: '10–49 ansatte',
    monthlyPrice: 999,
    annualPrice: 799 * 12, // 9588
    monthlyPriceExclVat: 999,
    annualPriceExclVat: 799 * 12, // 9588
    annualEquivalentMonthly: 799,
    annualEquivalentMonthlyExclVat: 799,
    features: [
      'Ubegrenset prosjekter',
  'Prosjektlogg med bilder',
  'Chat med kunder og team',
  'Prosjektdeling med kunder',
  'Prosjektdeling med underleverandører',
  'Mobil app (iOS/Android)',
     '7 dagers gratis prøveperiode',
    ]
  },
  {
    id: 'enterprise',
    name: 'Stor bedrift',
    description: 'For etablerte byggefirmaer',
    employeeRange: '50–249 ansatte',
    monthlyPrice: 2999,
    annualPrice: 2399 * 12, // 28788
    monthlyPriceExclVat: 2999,
    annualPriceExclVat: 2399 * 12, // 28788
    annualEquivalentMonthly: 2399,
    annualEquivalentMonthlyExclVat: 2399,
    features: [
      'Ubegrenset prosjekter',
  'Prosjektlogg med bilder',
  'Chat med kunder og team',
  'Prosjektdeling med kunder',
  'Prosjektdeling med underleverandører',
  'Mobil app (iOS/Android)',
     '7 dagers gratis prøveperiode',
    ]
  }
];

// Suggest plan based on company size from BRREG data
const suggestPlanBasedOnCompanySize = (employeeCount?: number): 'basic' | 'professional' | 'enterprise' => {
  if (!employeeCount) return 'basic';
  if (employeeCount <= 9) return 'basic';
  if (employeeCount <= 49) return 'professional';
  return 'enterprise';
};

// const formatPrice = (price: number): string => {
//   return new Intl.NumberFormat('nb-NO').format(price);
// };

// const calculateSavings = (monthlyPrice: number, annualPrice: number): number => {
//   return (monthlyPrice * 12) - annualPrice;
// };

export const Step5PlanSelection: React.FC<Step5PlanSelectionProps> = ({
  formData,
  onNext,
  onBack: _onBack,
  onPlanSelect
}) => {
  const { isAdministrator } = useUserRole();
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'professional' | 'enterprise'>('professional');
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month');
  const [recommendedPlan, setRecommendedPlan] = useState<'basic' | 'professional' | 'enterprise'>('professional');

  // Suggest plan based on company data
  useEffect(() => {
    const employeeCount = formData.brregData?.employeeCount;
    const suggestedPlan = suggestPlanBasedOnCompanySize(employeeCount);
    setSelectedPlan(suggestedPlan);
    setRecommendedPlan(suggestedPlan);
  }, [formData.brregData]);

  // Auto-proceed for non-administrators with recommended plan
  useEffect(() => {
    if (!isAdministrator) {
      const employeeCount = formData.brregData?.employeeCount;
      const suggestedPlan = suggestPlanBasedOnCompanySize(employeeCount);
      onPlanSelect(suggestedPlan, 'month');
      onNext();
    }
  }, [isAdministrator, formData.brregData, onPlanSelect, onNext]);

  const handlePlanSelect = (planId: 'basic' | 'professional' | 'enterprise') => {
    setSelectedPlan(planId);
    onPlanSelect(planId, billingInterval);
    // Remove automatic navigation - only select the plan
  };

  const handleProceedWithPlan = () => {
    onPlanSelect(selectedPlan, billingInterval);
    onNext();
  };

  const handleBillingChange = (interval: 'month' | 'year') => {
    setBillingInterval(interval);
    onPlanSelect(selectedPlan, interval);
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('nb-NO').format(price);
  };

  const getPriceDisplay = (tier: PlanTier) => {
    if (billingInterval === 'year') {
      return {
        primary: `${formatPrice(tier.annualPriceExclVat)} kr/år`,
        secondary: `tilsvarer ${formatPrice(tier.annualEquivalentMonthlyExclVat)} kr/mnd`
      };
    } else {
      return {
        primary: `${formatPrice(tier.monthlyPriceExclVat)} kr/mnd`,
        secondary: null
      };
    }
  };

  // Show loading state for non-administrators while auto-proceeding
  if (!isAdministrator) {
    return (
      <div className="space-y-8">
        <div className="text-center">
          <Heading2>Setter opp din plan</Heading2>
          <BodyText className="text-jobblogg-text-muted mt-2">
            Basert på bedriftsinformasjonen din setter vi opp den anbefalte planen automatisk.
          </BodyText>
          <div className="flex items-center justify-center mt-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <Heading2>Velg din plan</Heading2>
        <BodyText className="text-jobblogg-text-muted mt-2">
          Basert på bedriftsinformasjonen din anbefaler vi følgende plan. Du kan endre dette senere.
        </BodyText>
      </div>





      {/* Billing Toggle */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-4 p-1 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
          <button
            onClick={() => handleBillingChange('month')}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              billingInterval === 'month'
                ? 'bg-white text-jobblogg-primary shadow-sm'
                : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
            }`}
          >
            Betal Månedlig
          </button>
          <button
            onClick={() => handleBillingChange('year')}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              billingInterval === 'year'
                ? 'bg-white text-jobblogg-primary shadow-sm'
                : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
            }`}
          >
            Betal Årlig (Spar 20%)
          </button>
        </div>
      </div>

      {/* Plan Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {pricingTiers.map((tier) => {
          const isSelected = selectedPlan === tier.id;
          const isRecommended = recommendedPlan === tier.id;
          const priceDisplay = getPriceDisplay(tier);

          return (
            <div
              key={tier.id}
              className={`relative bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-300 ${
                isSelected
                  ? 'border-jobblogg-primary shadow-lg'
                  : 'border-jobblogg-border hover:border-jobblogg-primary/50 hover:shadow-md'
              }`}
              onClick={() => handlePlanSelect(tier.id)}
            >
              {/* Recommended Badge */}
              {isRecommended && (
                <div className="absolute -top-3 left-6">
                  <span className="bg-jobblogg-primary text-white px-3 py-1 rounded-full text-xs font-medium">
                    Anbefalt
                  </span>
                </div>
              )}

              <div className="space-y-4">
                {/* Plan Header */}
                <div>
                  <h3 className="text-xl font-bold text-jobblogg-text-strong mb-1">{tier.name}</h3>
                  <p className="text-sm text-jobblogg-text-muted">{tier.employeeRange}</p>
                  <p className="text-sm text-jobblogg-text-medium mt-2">{tier.description}</p>
                </div>

                {/* Pricing */}
                <div className="py-4">
                  <div className="flex items-baseline gap-1">
                    <span className="text-4xl font-bold text-jobblogg-text-strong">
                      {priceDisplay.primary}
                    </span>
                  </div>
                  <p className="text-sm text-jobblogg-text-muted mt-1">
                    ekskl. mva.
                  </p>

                  {priceDisplay.secondary && (
                    <p className="text-sm text-jobblogg-text-medium mt-1">
                      {priceDisplay.secondary}
                    </p>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-2">
                  {tier.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-jobblogg-text-medium">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <div className="pt-4">
                  <button
                    className={`w-full py-3 px-4 rounded-xl font-semibold transition-all duration-200 ${
                      isSelected
                        ? 'bg-jobblogg-primary text-white shadow-md hover:bg-jobblogg-primary/90'
                        : 'border-2 border-jobblogg-primary text-jobblogg-primary bg-white hover:bg-jobblogg-primary hover:text-white'
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePlanSelect(tier.id);
                      handleProceedWithPlan();
                    }}
                  >
                    Start gratis prøveperiode
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Trial Period Information Box */}
      <div className="bg-jobblogg-surface rounded-lg p-4 border border-jobblogg-border">
        <div className="flex items-center gap-2 mb-2">
          <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm font-medium text-jobblogg-text-strong">
            7 dagers gratis prøveperiode
          </span>
        </div>
        <p className="text-sm text-jobblogg-text-medium">
          Du starter med en gratis prøveperiode på 7 dager. Ingen kredittkort påkrevd nå. Du kan endre eller avbryte planen din når som helst.
        </p>
      </div>
    </div>
  );
};