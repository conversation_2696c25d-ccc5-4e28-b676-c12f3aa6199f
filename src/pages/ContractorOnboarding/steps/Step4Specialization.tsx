import React, { useEffect, useState } from 'react';
import { PrimaryButton, SecondaryButton, SpecializationSelector, Heading2, BodyText, TextMuted } from '../../../components/ui';
import { suggestSpecializationFromNaceCode, type Specialization } from '../../../utils/specializations';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';

interface Step4SpecializationProps {
  formData: ContractorOnboardingFormData;
  updateFormData: (updates: Partial<ContractorOnboardingFormData>) => void;
  errors: { [key: string]: string };
  onNext: () => void;
  onPrevious: () => void;
  setErrors: (errors: { [key: string]: string }) => void;
}

/**
 * Step 4: Specialization Selection
 * 
 * Features:
 * - Auto-suggest specialization from Brønnøysundregisteret NACE code
 * - Manual specialization selection with categories
 * - Support for primary + secondary specializations
 * - Validation and error handling
 */
export const Step4Specialization: React.FC<Step4SpecializationProps> = ({
  formData,
  updateFormData,
  errors,
  onNext,
  onPrevious,
  setErrors
}) => {
  const [suggestedSpecialization, setSuggestedSpecialization] = useState<Specialization | null>(null);

  // Auto-suggest specialization from Brønnøysundregisteret data
  useEffect(() => {
    if (formData.brregData?.naeringskode1 && !formData.primarySpecialization) {
      const suggestion = suggestSpecializationFromNaceCode(formData.brregData.naeringskode1);
      setSuggestedSpecialization(suggestion);
      
      if (suggestion) {
        updateFormData({ 
          specializationSource: 'brregData'
        });
      }
    }
  }, [formData.brregData, formData.primarySpecialization, updateFormData]);

  // Handle primary specialization change
  const handlePrimaryChange = (specialization: string | undefined) => {
    updateFormData({ 
      primarySpecialization: specialization,
      specializationSource: specialization ? 
        (suggestedSpecialization && specialization === suggestedSpecialization.id ? 'brregData' : 'manual') 
        : undefined
    });
    
    // Clear errors when user makes a selection
    if (specialization && errors.primarySpecialization) {
      setErrors({ ...errors, primarySpecialization: '' });
    }
  };

  // Handle secondary specializations change
  const handleSecondaryChange = (specializations: string[]) => {
    updateFormData({ secondarySpecializations: specializations });
  };

  // Validate and proceed to next step
  const handleNext = () => {
    const newErrors: { [key: string]: string } = {};

    // Validate primary specialization is required
    if (!formData.primarySpecialization) {
      newErrors.primarySpecialization = 'Hovedfagområde er påkrevd';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear errors and proceed
    setErrors({});
    onNext();
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <Heading2>Fagområder</Heading2>
        <BodyText className="text-jobblogg-text-muted max-w-2xl mx-auto">
          Velg fagområdene som best beskriver bedriftens tjenester. Dette hjelper andre å finne dere som underleverandør.
        </BodyText>
      </div>

      {/* Company Context */}
      <div className="bg-jobblogg-neutral-secondary rounded-xl p-6">
        <div className="space-y-2">
          <BodyText className="font-medium">
            {formData.companyName}
          </BodyText>
          {formData.brregData?.industryDescription && (
            <TextMuted className="text-sm">
              Næringskode: {formData.brregData.naeringskode1} - {formData.brregData.industryDescription}
            </TextMuted>
          )}
        </div>
      </div>

      {/* Specialization Selector */}
      <div className="max-w-2xl mx-auto">
        <SpecializationSelector
          primarySpecialization={formData.primarySpecialization}
          secondarySpecializations={formData.secondarySpecializations || []}
          onPrimaryChange={handlePrimaryChange}
          onSecondaryChange={handleSecondaryChange}
          suggestedSpecialization={suggestedSpecialization}
          showSuggestion={true}
          maxSecondary={3}
          error={errors.primarySpecialization}
        />
      </div>

      {/* Help Text */}
      <div className="max-w-2xl mx-auto bg-jobblogg-info/10 border border-jobblogg-info/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <svg className="w-5 h-5 text-jobblogg-info flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="space-y-2">
            <BodyText className="text-sm font-medium text-jobblogg-info">
              Tips for valg av fagområder
            </BodyText>
            <div className="text-sm text-jobblogg-text-muted space-y-1">
              <p>• Velg hovedfagområdet som best beskriver deres primære virksomhet</p>
              <p>• Legg til tilleggsfagområder hvis dere tilbyr flere tjenester</p>
              <p>• Dette hjelper andre å finne dere når de søker etter underleverandører</p>
              <p>• Dere kan oppdatere fagområdene senere i bedriftsprofilen</p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <SecondaryButton onClick={onPrevious}>
          Tilbake
        </SecondaryButton>
        
        <PrimaryButton onClick={handleNext}>
          Fortsett
        </PrimaryButton>
      </div>
    </div>
  );
};
