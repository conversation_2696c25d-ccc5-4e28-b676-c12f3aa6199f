import React, { useEffect, useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { PrimaryButton, SecondaryButton, TextInput, TextArea, FormError, PhoneInput, LockedInput, ToggleSwitch } from '../../../components/ui';
import { validateNorwegianPhone } from '../../../components/ui/Form/PhoneInput';
import { extractNorwegianPhone } from '../../../services/companyLookup';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';

interface Step3ContactDetailsProps {
  formData: ContractorOnboardingFormData;
  updateFormData: (updates: Partial<ContractorOnboardingFormData>) => void;
  errors: { [key: string]: string };
  onNext: () => void;
  onPrevious: () => void;
  setErrors: (errors: { [key: string]: string }) => void;
  brregData: any; // Brønnøysundregisteret data for auto-population
  autoPopulatedFields: {
    contactPerson: boolean;
    phone: boolean;
    email: boolean;
  };
  setAutoPopulatedFields: (fields: { contactPerson: boolean; phone: boolean; email: boolean }) => void;
  fieldOverrides: {
    phone: boolean;
    email: boolean;
  };
  setFieldOverrides: (overrides: { phone: boolean; email: boolean }) => void;
}

/**
 * Step 3: Contact Details
 * 
 * Collects contractor contact information:
 * - Contact person name
 * - Phone number (Norwegian format)
 * - Email address
 * - Optional notes
 */
export const Step3ContactDetails: React.FC<Step3ContactDetailsProps> = ({
  formData,
  updateFormData,
  errors,
  onNext,
  onPrevious,
  setErrors,
  brregData,
  autoPopulatedFields,
  setAutoPopulatedFields,
  fieldOverrides,
  setFieldOverrides
}) => {
  const { user } = useUser();
  const [phoneError, setPhoneError] = useState('');

  // Get dynamic contact person label based on registry data
  const getContactPersonLabel = (): string => {
    if (brregData?.managingDirector?.roleDescription) {
      return brregData.managingDirector.roleDescription;
    }
    return 'Daglig leder'; // Default fallback
  };

  // Get contact person field description
  const getContactPersonDescription = (): string => {
    const label = getContactPersonLabel();
    return `Navn på ${label.toLowerCase()} som skal være hovedkontakt for prosjekter`;
  };

  // Get validation error message
  const getContactPersonValidationError = (): string => {
    const label = getContactPersonLabel();
    return `${label} er påkrevd`;
  };

  // Get dynamic phone field label based on registry data source
  const getPhoneLabel = (): string => {
    if (brregData?.registryContact?.phoneSource === 'mobil') {
      return 'Mobilnummer';
    } else if (brregData?.registryContact?.phoneSource === 'telefon') {
      return 'Telefonnummer';
    }
    return 'Mobilnummer'; // Default for manual entry
  };

  // Get phone field description
  const getPhoneDescription = (): string => {
    const label = getPhoneLabel().toLowerCase();
    return `Norsk ${label} med +47 landskode`;
  };

  // Get phone validation error message
  const getPhoneValidationError = (): string => {
    const label = getPhoneLabel();
    return `${label} er påkrevd`;
  };

  // Auto-populate fields from Brønnøysundregisteret data
  useEffect(() => {
    if (brregData) {
      console.log('[Step3ContactDetails] Auto-population check for company:', {
        companyName: brregData.name,
        orgNumber: brregData.organizationNumber,
        managingDirector: brregData.managingDirector,
        registryContact: brregData.registryContact,
        currentFormData: { contactPerson: formData.contactPerson, phone: formData.phone, email: formData.email },
        fieldOverrides: fieldOverrides
      });

      const updates: Partial<ContractorOnboardingFormData> = {};
      const newAutoPopulated = { ...autoPopulatedFields };

      // Auto-populate contact person from managing director if available
      // Contact person is ALWAYS locked when available from registry (no override)
      if (brregData.managingDirector?.fullName) {
        // Check if the current value matches the registry value (for navigation scenarios)
        const isRegistryValue = formData.contactPerson === brregData.managingDirector.fullName;

        if (!formData.contactPerson) {
          // First time auto-population
          updates.contactPerson = brregData.managingDirector.fullName;
          newAutoPopulated.contactPerson = true;
          console.log('[Step3ContactDetails] Auto-populating contact person:', brregData.managingDirector.fullName);
        } else if (isRegistryValue) {
          // Navigation scenario: field already has registry value, ensure it stays locked
          newAutoPopulated.contactPerson = true;
          console.log('[Step3ContactDetails] Maintaining locked state for registry contact person:', brregData.managingDirector.fullName);
        }
        // If formData.contactPerson exists but doesn't match registry, it's user-modified, keep unlocked
      }

      // Auto-populate phone if available and not already set by user
      const phoneToTry = brregData.registryContact?.phone;

      // If no phone in registryContact, try alternative sources
      if (!phoneToTry) {
        // TODO: Add other potential phone sources here
        // For now, we'll just log that no phone was found
        console.log('[Step3ContactDetails] No phone in registryContact, checking alternatives...');
      }

      if (phoneToTry && !fieldOverrides.phone) {
        const extractedPhone = extractNorwegianPhone(phoneToTry);
        console.log('[Step3ContactDetails] Extracted phone result:', extractedPhone);

        if (extractedPhone) {
          // Check if the current value matches the registry value (for navigation scenarios)
          const isRegistryValue = formData.phone === extractedPhone;

          if (!formData.phone) {
            // First time auto-population
            updates.phone = extractedPhone;
            newAutoPopulated.phone = true;
            console.log('[Step3ContactDetails] Phone auto-populated successfully:', extractedPhone);
          } else if (isRegistryValue) {
            // Navigation scenario: field already has registry value, ensure it stays auto-populated
            newAutoPopulated.phone = true;
            console.log('[Step3ContactDetails] Maintaining auto-populated state for registry phone:', extractedPhone);
          }
          // If formData.phone exists but doesn't match registry, it's user-modified, keep normal state
        } else {
          console.warn('[Step3ContactDetails] Phone extraction failed for:', phoneToTry);
        }
      } else {
        console.log('[Step3ContactDetails] Phone auto-population skipped:', {
          hasPhoneToTry: !!phoneToTry,
          hasFormDataPhone: !!formData.phone,
          hasPhoneOverride: fieldOverrides.phone
        });
      }

      // Auto-populate email if available and not already set by user
      if (brregData.registryContact?.email && !fieldOverrides.email) {
        // Check if the current value matches the registry value (for navigation scenarios)
        const isRegistryValue = formData.email === brregData.registryContact.email;

        if (!formData.email) {
          // First time auto-population
          updates.email = brregData.registryContact.email;
          newAutoPopulated.email = true;
          console.log('[Step3ContactDetails] Email auto-populated:', brregData.registryContact.email);
        } else if (isRegistryValue) {
          // Navigation scenario: field already has registry value, ensure it stays auto-populated
          newAutoPopulated.email = true;
          console.log('[Step3ContactDetails] Maintaining auto-populated state for registry email:', brregData.registryContact.email);
        }
        // If formData.email exists but doesn't match registry, it's user-modified, keep normal state
      }

      // Always update auto-populated state, even if no form data updates are needed
      // This is crucial for navigation scenarios where fields already have registry values
      const hasStateChanges = (
        newAutoPopulated.contactPerson !== autoPopulatedFields.contactPerson ||
        newAutoPopulated.phone !== autoPopulatedFields.phone ||
        newAutoPopulated.email !== autoPopulatedFields.email
      );

      if (Object.keys(updates).length > 0) {
        console.log('[Step3ContactDetails] Applying updates:', updates);
        updateFormData(updates);
        setAutoPopulatedFields(newAutoPopulated);
      } else if (hasStateChanges) {
        console.log('[Step3ContactDetails] No form updates, but updating auto-populated state:', newAutoPopulated);
        setAutoPopulatedFields(newAutoPopulated);
      } else {
        console.log('[Step3ContactDetails] No updates to apply');
      }
    }
  }, [brregData, formData.contactPerson, formData.phone, formData.email, fieldOverrides, updateFormData]);

  // Auto-populate email with Clerk authentication email (priority over registry email)
  useEffect(() => {
    if (user?.primaryEmailAddress?.emailAddress && !formData.email && !fieldOverrides.email) {
      console.log('[Step3ContactDetails] Auto-populating email with Clerk authentication email:', user.primaryEmailAddress.emailAddress);
      updateFormData({ email: user.primaryEmailAddress.emailAddress });

      // Mark as auto-populated and locked (similar to Company Profile Modal)
      setAutoPopulatedFields({
        contactPerson: autoPopulatedFields.contactPerson,
        phone: autoPopulatedFields.phone,
        email: true
      });
    }
  }, [user?.primaryEmailAddress?.emailAddress, formData.email, fieldOverrides.email, updateFormData]);

  // Handle phone validation
  const handlePhoneValidation = (_isValid: boolean, error?: string) => {
    setPhoneError(error || '');
  };

  // Handle phone number change (PhoneInput provides raw digits)
  const handlePhoneChange = (digits: string) => {
    updateFormData({ phone: digits });

    // Mark as user-modified if it was auto-populated
    if (autoPopulatedFields.phone) {
      setFieldOverrides({ phone: true, email: fieldOverrides.email });
      setAutoPopulatedFields({
        contactPerson: autoPopulatedFields.contactPerson,
        phone: false,
        email: autoPopulatedFields.email
      });
    }

    // Clear phone error when user types
    if (errors.phone) {
      setErrors({ ...errors, phone: '' });
    }
    if (phoneError) {
      setPhoneError('');
    }
  };

  // Validate email format as user types
  const handleEmailChange = (value: string) => {
    updateFormData({ email: value });

    // Mark as user-modified if it was auto-populated
    if (autoPopulatedFields.email) {
      setFieldOverrides({ phone: fieldOverrides.phone, email: true });
      setAutoPopulatedFields({
        contactPerson: autoPopulatedFields.contactPerson,
        phone: autoPopulatedFields.phone,
        email: false
      });
    }

    // Clear email error when user types
    if (errors.email) {
      setErrors({ ...errors, email: '' });
    }
  };

  // Toggle field override (only for phone and email, contact person is always locked)
  const toggleFieldOverride = (field: 'phone' | 'email') => {
    setFieldOverrides({
      phone: field === 'phone' ? !fieldOverrides.phone : fieldOverrides.phone,
      email: field === 'email' ? !fieldOverrides.email : fieldOverrides.email
    });
    if (!fieldOverrides[field]) {
      setAutoPopulatedFields({
        contactPerson: autoPopulatedFields.contactPerson,
        phone: field === 'phone' ? false : autoPopulatedFields.phone,
        email: field === 'email' ? false : autoPopulatedFields.email
      });
    }
  };

  // Validate contact person name (only for manual entry)
  const handleContactPersonChange = (value: string) => {
    // Only allow changes if the field is not auto-populated from registry
    if (!autoPopulatedFields.contactPerson) {
      updateFormData({ contactPerson: value });

      // Clear error when user types
      if (errors.contactPerson) {
        setErrors({ ...errors, contactPerson: '' });
      }
    }
  };

  // Validate form and proceed to next step
  const handleNext = () => {
    const newErrors: { [key: string]: string } = {};

    // Validate contact person (dynamic based on role type)
    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = getContactPersonValidationError();
    }

    // Validate phone number using enhanced validation
    if (!formData.phone.trim()) {
      newErrors.phone = getPhoneValidationError();
    } else {
      const phoneType = brregData?.registryContact?.phoneSource === 'telefon' ? 'landline' : 'mobile';
      const phoneValidation = validateNorwegianPhone(formData.phone, phoneType);
      if (!phoneValidation.isValid) {
        newErrors.phone = phoneValidation.error || 'Ugyldig telefonnummer';
      }
    }

    // Validate email (skip validation if locked to Clerk authentication)
    const isClerkEmail = formData.email === user?.primaryEmailAddress?.emailAddress;
    const isEmailLocked = autoPopulatedFields.email && !fieldOverrides.email;

    if (!isClerkEmail || !isEmailLocked) {
      // Only validate if not locked to Clerk authentication
      if (!formData.email.trim()) {
        newErrors.email = 'E-postadresse er påkrevd';
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email.trim())) {
          newErrors.email = 'Ugyldig e-postadresse format';
        }
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear errors and proceed
    setErrors({});
    onNext();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-jobblogg-text-strong mb-2">
          Kontaktinformasjon
        </h2>
        <p className="text-jobblogg-text-muted">
          Legg til dine kontaktdetaljer for kommunikasjon med kunder
        </p>
      </div>

      {/* Contact Person (Dynamic label based on registry data) */}
      <div className="space-y-2">
        {autoPopulatedFields.contactPerson ? (
          <LockedInput
            label={getContactPersonLabel()}
            value={formData.contactPerson}
            fullWidth
            helperText="Hentet automatisk fra Brønnøysundregisteret og kan ikke endres"
          />
        ) : (
          <TextInput
            label={getContactPersonLabel()}
            placeholder="Arne Løken"
            required
            fullWidth
            value={formData.contactPerson}
            onChange={(e) => handleContactPersonChange(e.target.value)}
            error={errors.contactPerson}
            helperText={getContactPersonDescription()}
          />
        )}
      </div>

      {/* Phone Number (Dynamic label based on data source) */}
      <div className="space-y-2">
        {autoPopulatedFields.phone && !fieldOverrides.phone ? (
          <div className="space-y-2">
            <LockedInput
              label={getPhoneLabel()}
              value={`+47 ${formData.phone.replace(/(\d{3})(\d{2})(\d{3})/, '$1 $2 $3')}`}
              fullWidth
              helperText="Hentet automatisk fra Brønnøysundregisteret og kan ikke endres"
            />
            <div className="flex items-center justify-between">
              <span className="text-sm text-jobblogg-text-muted">
                Vil du endre {getPhoneLabel().toLowerCase()}?
              </span>
              <ToggleSwitch
                checked={fieldOverrides.phone}
                onChange={() => toggleFieldOverride('phone')}
                label="Rediger manuelt"
              />
            </div>
          </div>
        ) : (
          <PhoneInput
            label={getPhoneLabel()}
            required
            fullWidth
            value={formData.phone}
            onChange={handlePhoneChange}
            error={phoneError || errors.phone}
            helperText={autoPopulatedFields.phone ? "Redigerer manuelt (opprinnelig fra Brønnøysundregisteret)" : getPhoneDescription()}
            phoneType={brregData?.registryContact?.phoneSource === 'telefon' ? 'landline' : 'mobile'}
            enableValidation={true}
            onValidation={handlePhoneValidation}
          />
        )}
      </div>

      {/* Email Address */}
      <div className="space-y-2">
        {autoPopulatedFields.email && !fieldOverrides.email ? (
          // Check if email is from Clerk authentication vs. registry
          (() => {
            const isClerkEmail = formData.email === user?.primaryEmailAddress?.emailAddress;
            const isRegistryEmail = formData.email === brregData?.registryContact?.email;

            if (isClerkEmail) {
              // Clerk authentication email - permanently locked (no toggle)
              return (
                <LockedInput
                  label="Innloggings-e-post"
                  value={formData.email}
                  fullWidth
                  helperText="Denne e-postadressen er knyttet til din innlogging og kan ikke endres"
                />
              );
            } else if (isRegistryEmail) {
              // Registry email - can be overridden
              return (
                <div className="space-y-2">
                  <LockedInput
                    label="E-postadresse"
                    value={formData.email}
                    fullWidth
                    helperText="Hentet automatisk fra Brønnøysundregisteret"
                  />
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-jobblogg-text-muted">
                      Vil du endre e-postadresse?
                    </span>
                    <ToggleSwitch
                      checked={fieldOverrides.email}
                      onChange={() => toggleFieldOverride('email')}
                      label="Rediger manuelt"
                    />
                  </div>
                </div>
              );
            } else {
              // Fallback to locked input
              return (
                <LockedInput
                  label="E-postadresse"
                  value={formData.email}
                  fullWidth
                  helperText="Auto-utfylt e-postadresse"
                />
              );
            }
          })()
        ) : (
          <TextInput
            label="E-postadresse"
            placeholder="<EMAIL>"
            type="email"
            required
            fullWidth
            value={formData.email}
            onChange={(e) => handleEmailChange(e.target.value)}
            error={errors.email}
            helperText={autoPopulatedFields.email ? "Redigerer manuelt (opprinnelig fra Brønnøysundregisteret)" : "E-postadresse for kommunikasjon og varsler"}
          />
        )}
      </div>

      {/* Notes (Optional) */}
      <div className="space-y-2">
        <TextArea
          label="Notater (valgfritt)"
          placeholder="Tilleggsinformasjon om bedriften eller spesielle instruksjoner..."
          fullWidth
          rows={3}
          value={formData.notes}
          onChange={(e) => updateFormData({ notes: e.target.value })}
          error={errors.notes}
          helperText="Eventuell tilleggsinformasjon som kan være nyttig for kunder"
        />
      </div>

      {/* Information Box */}
      <div className="bg-jobblogg-primary-soft rounded-xl p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-2">
              Hvordan brukes kontaktinformasjonen?
            </h4>
            <div className="space-y-2 text-sm text-jobblogg-text-muted">
              <p>
                • <strong>{getContactPersonLabel()}:</strong> Vises til kunder i delte prosjekter og chat-meldinger
              </p>
              <p>
                • <strong>{getPhoneLabel()}:</strong> Brukes for direkte kontakt og vises i prosjektdetaljer
              </p>
              <p>
                • <strong>E-post:</strong> For varsler, rapporter og kommunikasjon via systemet (bruker din innloggings-e-post)
              </p>
              <p>
                • <strong>Notater:</strong> Vises kun til kunder i delte prosjekter (valgfritt)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Form Errors */}
      {errors.submit && (
        <FormError message={errors.submit} />
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <SecondaryButton onClick={onPrevious}>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </SecondaryButton>

        <PrimaryButton onClick={handleNext}>
          Fortsett
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step3ContactDetails;
