import React from 'react';
import { PrimaryButton, Heading2, BodyText, TextMuted } from '../../../components/ui';

interface Step1IntroductionProps {
  onNext: () => void;
}

/**
 * Step 1: Introduction to contractor onboarding
 * 
 * Provides a welcoming introduction explaining:
 * - Why company registration is necessary
 * - What data will be collected
 * - How the data will be used
 * - Privacy and security assurances
 */
export const Step1Introduction: React.FC<Step1IntroductionProps> = ({
  onNext
}) => {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="w-20 h-20 mx-auto mb-6 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
          <svg className="w-10 h-10 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <Heading2 className="text-3xl font-bold text-jobblogg-text-strong mb-4">
          Velkommen til JobbLogg! 👋
        </Heading2>
        <BodyText className="text-lg text-jobblogg-text-muted max-w-2xl mx-auto">
          For å komme i gang må vi registrere din bedrift i systemet. Dette gjør det enklere for deg å administrere prosjekter og kommunisere med kunder.
        </BodyText>
      </div>

      {/* Important Requirement Notice */}
      <div className="bg-jobblogg-primary-soft border-l-4 border-jobblogg-primary rounded-r-xl p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-2">
              Viktig informasjon
            </h4>
            <BodyText className="text-jobblogg-text-muted">
              For å registrere din bedrift i JobbLogg må du være <strong className="text-jobblogg-text-strong">daglig leder</strong> i selskapet.
              Dette sikrer at kun autoriserte personer kan administrere bedriftens prosjekter og kundekommunikasjon.
            </BodyText>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="bg-jobblogg-card rounded-xl p-6 space-y-6">
        <h3 className="text-xl font-semibold text-jobblogg-text-strong mb-4">
          Hva skjer i registreringsprosessen?
        </h3>
        
        <div className="grid gap-4 md:grid-cols-2">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-jobblogg-text-strong mb-1">
                Bedriftssøk
              </h4>
              <TextMuted className="text-sm">
                Vi henter bedriftsinformasjon fra Brønnøysundregisteret for å sikre korrekte data
              </TextMuted>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-jobblogg-text-strong mb-1">
                Kontaktinformasjon
              </h4>
              <TextMuted className="text-sm">
                Legg til dine kontaktdetaljer for kommunikasjon med kunder
              </TextMuted>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-jobblogg-text-strong mb-1">
                Bekreftelse
              </h4>
              <TextMuted className="text-sm">
                Gjennomgå og bekreft all informasjon før registrering
              </TextMuted>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-jobblogg-success-soft rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-jobblogg-text-strong mb-1">
                Klar til bruk
              </h4>
              <TextMuted className="text-sm">
                Din bedrift er registrert og du kan begynne å bruke JobbLogg
              </TextMuted>
            </div>
          </div>
        </div>
      </div>

      {/* Privacy and Security Section */}
      <div className="bg-jobblogg-neutral-secondary rounded-xl p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-jobblogg-warning-soft rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-4 h-4 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-2">
              Personvern og sikkerhet
            </h4>
            <div className="space-y-2 text-sm text-jobblogg-text-muted">
              <p>
                • All bedriftsinformasjon lagres sikkert og brukes kun for å administrere dine prosjekter
              </p>
              <p>
                • Vi henter offentlig tilgjengelig informasjon fra Brønnøysundregisteret
              </p>
              <p>
                • Du kan når som helst oppdatere eller slette din bedriftsinformasjon
              </p>
              <p>
                • Informasjonen deles kun med kunder i prosjekter du velger å dele
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Time Estimate */}
      <div className="text-center">
        <div className="inline-flex items-center gap-2 bg-jobblogg-primary-soft rounded-full px-4 py-2">
          <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <TextMuted className="text-sm font-medium">
            Estimert tid: 3-5 minutter
          </TextMuted>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center pt-4">
        <PrimaryButton
          onClick={onNext}
          size="lg"
          className="px-8"
        >
          Kom i gang
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step1Introduction;
