import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { ProjectCard, Heading2, EmptyState, DashboardLayout, StatsCard, BodyText, TextMuted, ArchiveActions, ProjectMapCard, isAddressComplete, PrimaryButton, SecondaryButton, SubcontractorProjectCard, ProjectSearchContainer } from '../../components/ui';
import { ChatStatsCard } from '../../components/chat';
import { TeamStatsCard } from '../../components/team';
import { useOfflineProjects } from '../../hooks/useOfflineProjects';
import { usePWA } from '../../hooks/usePWA';
// import OfflineConsentModal from '../../components/OfflineConsentModal'; // TODO: Use for offline consent
import { GlobalNotifications } from '../../components/notifications/GlobalNotifications';
import { ConvexAuthErrorBoundary } from '../../components/ConvexAuthErrorBoundary';
import { useUserRole } from '../../hooks/useUserRole';
import { useAutoTrackActivity } from '../../hooks/useUserActivity';
import { useBlockedUserCheck } from '../../hooks/useBlockedUserCheck';
import { SubscriptionGate, MinimalTrialIndicator } from '../../components/subscription';

// Draft project interface for old CreateProject form
interface DraftProject {
  formData: {
    projectName: string;
    description: string;
    customerName: string;
    customerType: 'privat' | 'bedrift';
    contactPerson: string;
    phone: string;
    email: string;
    address: string;
    orgNumber: string;
    notes: string;
  };
  useExistingCustomer: boolean;
  selectedCustomerId: string;
  timestamp: number;
}

// Draft project interface for new CreateProjectWizard
interface WizardDraftProject {
  formData: {
    projectName: string;
    description: string;
    customerName: string;
    customerType: 'privat' | 'bedrift';
    contactPerson: string;
    phone: string;
    email: string;
    address: string;
    orgNumber: string;
    notes: string;
    jobDescription: string;
    accessNotes: string;
    equipmentNeeds: string;
    unresolvedQuestions: string;
    personalNotes: string;
  };
  currentStep: number;
  useExistingCustomer: boolean;
  selectedCustomerId: string;
  createdProjectId: string | null;
  timestamp: number;
}

const STORAGE_KEY = 'jobblogg-create-project-form';
const WIZARD_STORAGE_KEY = 'jobblogg-create-project-wizard';
const DRAFT_EXPIRY_DAYS = 7;



const Dashboard: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();

  // Archive filtering state
  const [showArchived, setShowArchived] = useState(false);

  // User role information
  const { isAdministrator, isLoading: roleLoading } = useUserRole();

  // Track user activity on dashboard visit
  useAutoTrackActivity('dashboard_visit');

  // Check if user is blocked and redirect if necessary
  const { isBlocked: _isBlocked, isLoading: _blockCheckLoading } = useBlockedUserCheck();

  // Query active or archived projects based on filter with team-based access
  // TODO: Re-enable when type instantiation issue is resolved
  // const onlineProjects = useQuery(
  //   api.projectsTeam.getAccessibleProjectsWithTeamSupport,
  //   user?.id ? { userId: user.id, includeArchived: false } : "skip"
  // );
  // const archivedProjects = useQuery(
  //   api.projectsTeam.getAccessibleProjectsWithTeamSupport,
  //   user?.id ? { userId: user.id, includeArchived: true } : "skip"
  // );
  const onlineProjects: any[] = []; // Temporarily provide fallback array due to type instantiation issues
  const archivedProjects: any[] = []; // Temporarily provide fallback array due to type instantiation issues

  // Offline functionality
  const { isOnline, hasOfflineSession, canAccessOfflineData } = usePWA();
  const {
    projects: allProjects,
    getProjectSyncStatus,
    isProjectOffline,
    isProjectCached,
    isProjectAvailableOffline
  } = useOfflineProjects();

  // State for offline consent modal - TODO: Use for offline consent
  // const [showOfflineConsent, setShowOfflineConsent] = useState(false);

  // Use combined projects (online + offline) or fallback to online only
  const projects = allProjects.length > 0 ? allProjects : onlineProjects;

  // Archive/restore functionality is now handled by ArchiveActions component



  // Draft project state
  const [draftProject, setDraftProject] = useState<DraftProject | null>(null);
  const [wizardDraftProject, setWizardDraftProject] = useState<WizardDraftProject | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showWizardDeleteConfirm, setShowWizardDeleteConfirm] = useState(false);

  // Draft management functions
  const loadDraftProject = () => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsed: DraftProject = JSON.parse(savedData);

        // Check if draft is valid and not expired
        if (parsed.timestamp && parsed.formData?.projectName?.trim()) {
          const daysSinceCreated = (Date.now() - parsed.timestamp) / (1000 * 60 * 60 * 24);

          if (daysSinceCreated <= DRAFT_EXPIRY_DAYS) {
            setDraftProject(parsed);
            return;
          } else {
            // Auto-cleanup expired draft
            localStorage.removeItem(STORAGE_KEY);
          }
        }
      }
      setDraftProject(null);
    } catch (error) {
      console.error('Error loading draft project:', error);
      setDraftProject(null);
    }
  };

  // Load wizard draft project
  const loadWizardDraftProject = () => {
    try {
      const savedData = localStorage.getItem(WIZARD_STORAGE_KEY);
      if (savedData) {
        const parsed: WizardDraftProject = JSON.parse(savedData);

        // Check if draft is valid and not expired
        if (parsed.timestamp && parsed.formData?.projectName?.trim()) {
          const daysSinceCreated = (Date.now() - parsed.timestamp) / (1000 * 60 * 60 * 24);

          if (daysSinceCreated <= DRAFT_EXPIRY_DAYS) {
            setWizardDraftProject(parsed);
            return;
          } else {
            // Auto-cleanup expired draft
            localStorage.removeItem(WIZARD_STORAGE_KEY);
          }
        }
      }
      setWizardDraftProject(null);
    } catch (error) {
      console.error('Error loading wizard draft project:', error);
      setWizardDraftProject(null);
    }
  };

  const deleteDraft = () => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setDraftProject(null);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('Error deleting draft:', error);
    }
  };

  const deleteWizardDraft = () => {
    try {
      localStorage.removeItem(WIZARD_STORAGE_KEY);
      setWizardDraftProject(null);
      setShowWizardDeleteConfirm(false);
    } catch (error) {
      console.error('Error deleting wizard draft:', error);
    }
  };

  const continueDraftEditing = () => {
    navigate('/create');
  };

  const continueWizardDraftEditing = () => {
    navigate('/create-wizard');
  };

  const formatDraftTimestamp = (timestamp: number): string => {
    const now = Date.now();
    const diffMinutes = Math.floor((now - timestamp) / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMinutes < 60) {
      return `Oppdatert for ${diffMinutes} min siden`;
    } else if (diffHours < 24) {
      return `Oppdatert for ${diffHours} timer siden`;
    } else {
      return `Oppdatert for ${diffDays} dager siden`;
    }
  };

  // Load draft projects on component mount and when localStorage changes
  useEffect(() => {
    loadDraftProject();
    loadWizardDraftProject();

    // Listen for localStorage changes (e.g., from other tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEY) {
        loadDraftProject();
      } else if (e.key === WIZARD_STORAGE_KEY) {
        loadWizardDraftProject();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Handle offline consent prompt - NO LONGER NEEDED
  // Offline functionality is now classified as "necessary" under GDPR
  // and is automatically available to authenticated users
  useEffect(() => {
    // Offline consent is no longer required - it's part of necessary functionality
    console.log('[Dashboard] Offline functionality is automatically available (necessary feature)');
  }, [user?.id, hasOfflineSession, canAccessOfflineData]);

  // Handle offline consent - TODO: Use for offline consent handling
  // const handleOfflineConsent = () => {
  //   setShowOfflineConsent(false);
  //   console.log('[Dashboard] User consented to offline storage - refreshing page to enable caching');
  //   // The consent is handled by the modal component
  //   // Refresh page to trigger offline data initialization and project caching
  //   window.location.reload();
  // };

  // const handleOfflineDecline = () => {
  //   setShowOfflineConsent(false);
  // };



  // Loading state with modern skeleton
  if (projects === undefined) {
    return (
      <div className="min-h-screen bg-jobblogg-neutral">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 mb-12">
            <div className="flex items-center gap-6">
              <div className="skeleton h-12 w-64"></div>
              <div className="skeleton h-10 w-10 rounded-full"></div>
            </div>
            <div className="skeleton h-12 w-40 rounded-xl"></div>
          </div>

          {/* Stats Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border">
                <div className="skeleton h-4 w-20 mb-3"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-3 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Archive/restore functionality is now handled by the ArchiveActions component

  // Get the appropriate project list based on filter
  const currentProjects = showArchived ? archivedProjects : projects;

  // Sort projects by creation date (newest first)
  const sortedProjects = currentProjects?.sort((a, b) => b.createdAt - a.createdAt) || [];

  // Debug logging
  console.log('Dashboard Debug:', {
    userId: user?.id,
    userLoaded: !!user,
    projectsRaw: projects,
    projectsCount: projects?.length,
    archivedProjectsRaw: archivedProjects,
    archivedProjectsCount: archivedProjects?.length,
    currentProjectsCount: currentProjects?.length,
    sortedProjectsCount: sortedProjects.length,
    showArchived,
    projectsUndefined: projects === undefined,
    archivedUndefined: archivedProjects === undefined
  });

  // Show loading state if user is not loaded yet
  if (!user) {
    return (
      <DashboardLayout title="Laster..." subtitle="Henter brukerinformasjon...">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show loading state if queries are still loading
  if (projects === undefined || archivedProjects === undefined) {
    return (
      <DashboardLayout title="Laster prosjekter..." subtitle="Henter dine prosjekter...">
        <div className="animate-pulse space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-24 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <ConvexAuthErrorBoundary
      fallback={
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
              Autentiseringsproblem
            </h3>
            <p className="text-jobblogg-text-medium mb-4">
              Det oppstod et problem med autentiseringen. Vennligst last inn siden på nytt.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="w-full px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary-dark transition-colors"
            >
              Last inn på nytt
            </button>
          </div>
        </div>
      }
    >
      <>
        <DashboardLayout
      title="Dine prosjekter"
      headerActions={
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">


          {/* Primary Create Project Button */}
          <SubscriptionGate feature="create_project">
            <PrimaryButton
              onClick={() => navigate('/create-wizard')}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              }
              className="min-h-[44px] w-full sm:w-auto order-1 sm:order-none"
            >
              Nytt prosjekt
            </PrimaryButton>
          </SubscriptionGate>

          {/* Team Management Button (Administrators only) */}
          {!roleLoading && isAdministrator && (
            <SecondaryButton
              onClick={() => navigate('/team')}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 515.196-2.121m0 0a5.002 5.002 0 019.608 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              }
              className="min-h-[44px] w-full sm:w-auto order-1 sm:order-none"
            >
              Administrer team
            </SecondaryButton>
          )}

          {/* Archive Filter Toggle */}
          <div className="flex items-center gap-2 order-2 sm:order-none">
            <button
              onClick={() => setShowArchived(false)}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                !showArchived
                  ? 'bg-jobblogg-primary text-white'
                  : 'text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10'
              }`}
            >
              Aktive ({projects?.length || 0})
            </button>
            <button
              onClick={() => setShowArchived(true)}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                showArchived
                  ? 'bg-jobblogg-warning text-white'
                  : 'text-jobblogg-text-medium hover:text-jobblogg-warning hover:bg-jobblogg-warning/10'
              }`}
            >
              Arkiverte ({archivedProjects?.length || 0})
            </button>

            {/* Link to dedicated archived projects page */}
            {(archivedProjects?.length || 0) > 0 && (
              <button
                onClick={() => navigate('/archived-projects')}
                className="px-3 py-1.5 rounded-lg text-sm font-medium text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10 transition-all duration-200 flex items-center gap-1"
                title="Gå til arkiverte prosjekter"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Administrer
              </button>
            )}
          </div>
        </div>
      }
      statsSection={
        <div className="grid-stats">
          <StatsCard
            title="Aktive prosjekter"
            value={projects?.length || 0}
            variant="primary"
            animationDelay="0s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            }
          />
          <StatsCard
            title="Denne måneden"
            value={(projects || []).filter(p => {
              const projectDate = new Date(p.createdAt);
              const now = new Date();
              return projectDate.getMonth() === now.getMonth() &&
                     projectDate.getFullYear() === now.getFullYear();
            }).length}
            variant="accent"
            animationDelay="0.1s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            }
          />
          <StatsCard
            title="Siste prosjekt"
            value={(projects && projects.length > 0) ?
              new Date(projects.sort((a, b) => b.createdAt - a.createdAt)[0].createdAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short' }) :
              '-'
            }
            variant="primary"
            animationDelay="0.2s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />

          {/* Team Statistics Card (Administrators only) */}
          {!roleLoading && isAdministrator && (
            <TeamStatsCard
              animationDelay="0.3s"
              onClick={() => navigate('/team')}
            />
          )}

          {/* Chat Statistics Card */}
          <ChatStatsCard
            animationDelay={!roleLoading && isAdministrator ? "0.4s" : "0.3s"}
            onClick={() => navigate('/conversations')}
          />

        </div>
      }
    >
      {/* Minimal Trial Indicator */}
      <MinimalTrialIndicator />

      {/* Draft Projects Section */}
      {(draftProject || wizardDraftProject) && (
        <section className="space-section">
          <Heading2 className="mb-6 flex items-center gap-3">
            <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Prosjektutkast
          </Heading2>

          <div className="grid-mobile-cards">
            {/* Old CreateProject Draft */}
            {draftProject && (
            <div className="bg-white rounded-xl border-2 border-dashed border-jobblogg-warning/30 p-6 hover:border-jobblogg-warning/50 transition-colors duration-200 animate-slide-up">
              {/* Draft Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-warning/10 text-jobblogg-warning border border-jobblogg-warning/20">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Kladd
                  </div>
                </div>
                <TextMuted className="text-xs">
                  {formatDraftTimestamp(draftProject.timestamp)}
                </TextMuted>
              </div>

              {/* Draft Content */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
                  {draftProject.formData.projectName || 'Uten navn'}
                </h3>

                {draftProject.formData.customerName && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {draftProject.formData.customerName}
                      {draftProject.formData.customerType === 'bedrift' && (
                        <span className="ml-1 text-xs bg-jobblogg-neutral px-1 py-0.5 rounded">
                          Bedrift
                        </span>
                      )}
                    </TextMuted>
                  </div>
                )}

                {draftProject.formData.address && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {draftProject.formData.address}
                    </TextMuted>
                  </div>
                )}

                {draftProject.formData.description && (
                  <BodyText className="text-sm text-jobblogg-text-muted mt-3 line-clamp-2">
                    {draftProject.formData.description}
                  </BodyText>
                )}
              </div>

              {/* Draft Actions */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={continueDraftEditing}
                  className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Fortsett redigering
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-error/10 hover:bg-jobblogg-error/20 text-jobblogg-error font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Slett kladd
                </button>
              </div>
            </div>
            )}

            {/* New CreateProjectWizard Draft */}
            {wizardDraftProject && (
            <div className="bg-white rounded-xl border-2 border-dashed border-jobblogg-primary/30 p-6 hover:border-jobblogg-primary/50 transition-colors duration-200 animate-slide-up">
              {/* Draft Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Prosjektveiviser
                  </div>
                  <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                    Steg {wizardDraftProject.currentStep}/3
                  </div>
                </div>
                <TextMuted className="text-xs ml-4">
                  {formatDraftTimestamp(wizardDraftProject.timestamp)}
                </TextMuted>
              </div>

              {/* Draft Content */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
                  {wizardDraftProject.formData.projectName || 'Uten navn'}
                </h3>

                {wizardDraftProject.formData.customerName && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {wizardDraftProject.formData.customerName}
                      {wizardDraftProject.formData.customerType === 'bedrift' && (
                        <span className="ml-1 text-xs bg-jobblogg-neutral px-1 py-0.5 rounded">
                          Bedrift
                        </span>
                      )}
                    </TextMuted>
                  </div>
                )}

                {wizardDraftProject.formData.address && (
                  <div className="flex items-center gap-2 mb-2">
                    <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <TextMuted className="text-sm">
                      {wizardDraftProject.formData.address}
                    </TextMuted>
                  </div>
                )}

                {wizardDraftProject.formData.description && (
                  <BodyText className="text-sm text-jobblogg-text-muted mt-3 line-clamp-2">
                    {wizardDraftProject.formData.description}
                  </BodyText>
                )}
              </div>

              {/* Draft Actions */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={continueWizardDraftEditing}
                  className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Fortsett
                </button>
                <button
                  onClick={() => setShowWizardDeleteConfirm(true)}
                  className="inline-flex items-center justify-center gap-2 px-4 py-3 bg-jobblogg-error/10 hover:bg-jobblogg-error/20 text-jobblogg-error font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Slett utkast
                </button>
              </div>
            </div>
            )}
          </div>
        </section>
      )}

      {/* Projects Grid Section with Search and Filter */}
      <section className="space-section">
        <Heading2 className="mb-6 flex items-center gap-3">
          <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Prosjektoversikt
        </Heading2>

        <ProjectSearchContainer
          projects={sortedProjects}
          persistFilters={true}
          storageKey="dashboard-project-search"
          searchPlaceholder="Søk i prosjekter, kunder og adresser..."
          showResultsSummary={true}
        >
          {(filteredProjects) => (
            <>
              <div className="grid-mobile-cards">
                {/* Project Cards with Map Integration */}
                {filteredProjects.map((project, index) => {
            // Check if this is a subcontractor project
            const isSubcontractorProject = project.userAccessLevel === 'subcontractor';

            // For subcontractor projects, use enhanced SubcontractorProjectCard
            if (isSubcontractorProject) {
              return (
                <SubcontractorProjectCard
                  key={project._id}
                  title={project.name}
                  description={project.description || 'Ingen beskrivelse tilgjengelig'}
                  projectId={project._id}
                  updatedAt={new Date(project.createdAt).toLocaleDateString('nb-NO')}
                  onClick={() => {
                    // If invitation is pending, go to invitation detail page
                    if (project.invitationStatus === 'pending') {
                      navigate(`/invitations/${(project as any).assignmentId || project._id}`);
                    } else {
                      navigate(`/project/${project._id}`);
                    }
                  }}
                  animationDelay={`${index * 0.1}s`}
                  customer={project.customer}
                  invitationStatus={project.invitationStatus}
                  assignedBy={(project as any).assignedBy}
                  assignedAt={project.assignedAt}
                  specialization={project.subcontractorSpecialization}
                  mainContractorCompany={project.mainContractorCompany}
                  userAccessLevel={project.userAccessLevel}
                />
              );
            }

            // Check if project has complete address information for map display
            const hasCompleteAddress = project.customer && isAddressComplete(
              project.customer.streetAddress,
              project.customer.postalCode,
              project.customer.city
            );

            // Check project offline status
            const projectId = project._id || (project as any).id;
            const isOfflineCreated = isProjectOffline(projectId); // Created while offline
            const isProjectCachedForOffline = isProjectCached(projectId); // Cached from online
            const isAvailableOffline = isProjectAvailableOffline(projectId); // Available offline (cached or created)
            const syncStatus = getProjectSyncStatus(projectId);

            console.log('[Dashboard] Project status for', project.name, {
              projectId,
              isOfflineCreated: isOfflineCreated, // Was this project created offline?
              isProjectCachedForOffline: isProjectCachedForOffline, // Is this project cached for offline use?
              isAvailableOffline: isAvailableOffline, // Can we access this project offline?
              syncStatus: syncStatus, // Current sync status
              currentlyOnline: isOnline, // Are we online right now?
              explanation: isOfflineCreated
                ? 'Project created offline (needs sync)'
                : isProjectCachedForOffline
                  ? 'Project cached from online (available offline)'
                  : 'Project only available online'
            });

            // Use ProjectMapCard for projects with complete addresses, regular ProjectCard otherwise
            if (hasCompleteAddress) {
              return (
                <div key={projectId} className="relative">
                  <ProjectMapCard
                    project={project as any}
                    className="cursor-pointer"
                    onClick={() => navigate(`/project/${projectId}`)}
                    width={300}
                    height={200}
                    zoom={15}
                  />

                  {/* Offline Status Indicator */}
                  {isOfflineCreated && (
                    <div className="absolute top-2 left-2">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${
                        syncStatus === 'pending' ? 'bg-jobblogg-warning-soft text-jobblogg-warning' :
                        syncStatus === 'syncing' ? 'bg-jobblogg-primary-soft text-jobblogg-primary' :
                        syncStatus === 'error' ? 'bg-jobblogg-error-soft text-jobblogg-error' :
                        'bg-jobblogg-success-soft text-jobblogg-success'
                      }`}>
                        <div className={`w-2 h-2 rounded-full ${
                          syncStatus === 'pending' ? 'bg-jobblogg-warning' :
                          syncStatus === 'syncing' ? 'bg-jobblogg-primary animate-pulse' :
                          syncStatus === 'error' ? 'bg-jobblogg-error' :
                          'bg-jobblogg-success'
                        }`} />
                        {syncStatus === 'pending' ? 'Venter' :
                         syncStatus === 'syncing' ? 'Synker' :
                         syncStatus === 'error' ? 'Feil' :
                         'Offline'}
                      </div>
                    </div>
                  )}

                  {/* Archive Actions Overlay */}
                  {(project.isArchived || showArchived) && (
                    <div className="absolute top-2 right-2">
                      <ArchiveActions
                        projectId={projectId}
                        isArchived={project.isArchived}
                        variant="button"
                        className="text-xs px-2 py-1"
                      />
                    </div>
                  )}
                </div>
              );
            }

            // Fallback to regular ProjectCard with offline status
            return (
              <div key={projectId} className="relative">
                <ProjectCard
                  title={project.name}
                  description={project.description || 'Ingen beskrivelse tilgjengelig'}
                  projectId={projectId}
                  projectOwnerId={(project as any).userId}
                  updatedAt={new Date(project.createdAt || (project as any)._creationTime).toLocaleDateString('nb-NO')}
                  onClick={() => {
                    console.log('🚀 [Dashboard] Navigating to project:', {
                      projectId,
                      projectName: project.name,
                      isOfflineCreated: isOfflineCreated, // FALSE = cached from online
                      isProjectCachedForOffline: isProjectCachedForOffline, // TRUE = available offline
                      isAvailableOffline: isAvailableOffline, // TRUE = can access offline
                      syncStatus: syncStatus, // 'cached' = cached from online
                      currentlyOnline: isOnline, // Your current network status
                      url: `/project/${projectId}`,
                      shouldWork: isAvailableOffline ? '✅ Should work offline' : '❌ Needs internet'
                    });
                    navigate(`/project/${projectId}`);
                  }}
                  animationDelay={`${index * 0.1}s`}
                  customer={project.customer}
                  isArchived={project.isArchived}
                  archivedAt={(project as any).archivedAt}
                  showArchiveActions={false}
                />

                {/* Status Indicators */}
                {(isOfflineCreated || isProjectCachedForOffline || (!isOnline && isAvailableOffline)) && (
                  <div className="absolute top-4 right-4 flex flex-col gap-1">
                    {/* Offline-Created Project Indicator */}
                    {isOfflineCreated && (
                      <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${
                        syncStatus === 'pending' ? 'bg-jobblogg-warning-soft text-jobblogg-warning' :
                        syncStatus === 'syncing' ? 'bg-jobblogg-primary-soft text-jobblogg-primary' :
                        syncStatus === 'error' ? 'bg-jobblogg-error-soft text-jobblogg-error' :
                        'bg-jobblogg-success-soft text-jobblogg-success'
                      }`}>
                        <div className={`w-2 h-2 rounded-full ${
                          syncStatus === 'pending' ? 'bg-jobblogg-warning' :
                          syncStatus === 'syncing' ? 'bg-jobblogg-primary animate-pulse' :
                          syncStatus === 'error' ? 'bg-jobblogg-error' :
                          'bg-jobblogg-success'
                        }`} />
                        {syncStatus === 'pending' ? 'Venter sync' :
                         syncStatus === 'syncing' ? 'Synker' :
                         syncStatus === 'error' ? 'Sync feil' :
                         'Offline opprettet'}
                      </div>
                    )}

                    {/* Currently Offline but Project Available Indicator */}
                    {!isOnline && isAvailableOffline && !isOfflineCreated && (
                      <div className="px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 bg-jobblogg-warning-soft text-jobblogg-warning">
                        <div className="w-2 h-2 rounded-full bg-jobblogg-warning" />
                        Offline tilgjengelig
                      </div>
                    )}

                    {/* Cached for Offline Indicator (when online) */}
                    {isOnline && isProjectCachedForOffline && !isOfflineCreated && (
                      <div className="px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 bg-jobblogg-accent-soft text-jobblogg-accent">
                        <div className="w-2 h-2 rounded-full bg-jobblogg-accent" />
                        Offline-klar
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}

                {/* Empty State using UI Component */}
                {filteredProjects.length === 0 && (
                  <div className="col-span-full">
                    <EmptyState
                      title={showArchived ? "📦 Ingen arkiverte prosjekter" : "🚀 Kom i gang med ditt første prosjekt!"}
                      description={showArchived
                        ? "Du har ingen arkiverte prosjekter ennå. Prosjekter du arkiverer vil vises her."
                        : "JobbLogg hjelper deg å dokumentere arbeidsprosessen din med bilder og notater. Opprett ditt første prosjekt for å komme i gang."
                      }
                      actionLabel={showArchived ? "Vis aktive prosjekter" : "Opprett ditt første prosjekt"}
                      onAction={() => showArchived ? setShowArchived(false) : navigate('/create-wizard')}
                    />
                  </div>
                )}
              </div>
            </>
          )}
        </ProjectSearchContainer>
      </section>
      </DashboardLayout>

      {/* Delete Draft Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 animate-scale-up">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-jobblogg-error/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">Slett kladd</h3>
                <TextMuted className="text-sm">Denne handlingen kan ikke angres</TextMuted>
              </div>
            </div>

            <BodyText className="mb-6 text-jobblogg-text-medium">
              Er du sikker på at du vil slette kladden "{draftProject?.formData.projectName || 'Uten navn'}"?
              All utfylt informasjon vil gå tapt.
            </BodyText>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-3 bg-jobblogg-neutral hover:bg-jobblogg-border text-jobblogg-text-strong font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Avbryt
              </button>
              <button
                onClick={deleteDraft}
                className="flex-1 px-4 py-3 bg-jobblogg-error hover:bg-jobblogg-error/90 text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Slett kladd
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Wizard Draft Confirmation Dialog */}
      {showWizardDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 animate-scale-up">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-jobblogg-error/10 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">Slett utkast</h3>
                <TextMuted className="text-sm">Denne handlingen kan ikke angres</TextMuted>
              </div>
            </div>

            <BodyText className="mb-6 text-jobblogg-text-medium">
              Er du sikker på at du vil slette utkastet "{wizardDraftProject?.formData.projectName || 'Uten navn'}"?
              All utfylt informasjon fra steg {wizardDraftProject?.currentStep}/3 vil gå tapt.
            </BodyText>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => setShowWizardDeleteConfirm(false)}
                className="flex-1 px-4 py-3 bg-jobblogg-neutral hover:bg-jobblogg-border text-jobblogg-text-strong font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Avbryt
              </button>
              <button
                onClick={deleteWizardDraft}
                className="flex-1 px-4 py-3 bg-jobblogg-error hover:bg-jobblogg-error/90 text-white font-medium rounded-lg transition-colors duration-200 min-h-[44px]"
              >
                Slett utkast
              </button>
            </div>
          </div>
        </div>
      )}

        {/* Global Toast Notifications */}
        <GlobalNotifications />

        {/* Offline Consent Modal - NO LONGER NEEDED */}
        {/* Offline functionality is now part of necessary cookies */}
      </>
    </ConvexAuthErrorBoundary>
  );
};

export default Dashboard;
