import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
// import { useMutation, useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser, useAuth } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { 
  PageLayout, 
  Heading1, 
  Heading2, 
  BodyText, 
  TextMuted, 
  PrimaryButton, 
  SecondaryButton,
  FormError,
  StatsCard
} from '../../components/ui';

const TeamOnboarding: React.FC = () => {
  const { user, isLoaded } = useUser();
  const { isSignedIn } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [isAccepting, setIsAccepting] = useState(false);
  const [acceptanceResult, setAcceptanceResult] = useState<any>(null);
  const [invitationInfo, setInvitationInfo] = useState<any>(null);

  // TODO: Re-enable when type instantiation issue is resolved
  // const acceptInvitation = useMutation(api.teamManagement.acceptTeamInvitation);
  const acceptInvitation = async (args: any) => {
    console.log("⚠️ Accept invitation temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Query invitation info without requiring authentication
  // TODO: Re-enable when type instantiation issue is resolved
  // const invitationData = useQuery(
  //   api.teamManagement.getInvitationInfo,
  //   invitationToken ? { invitationToken } : "skip"
  // );
  const invitationData = null; // Temporarily provide fallback due to type instantiation issues

  // Get invitation token from URL parameters
  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setInvitationToken(token);
    } else {
      setError('Invitasjonstoken mangler i URL');
    }
  }, [searchParams]);

  // Update invitation info when data is loaded
  useEffect(() => {
    if (invitationData) {
      setInvitationInfo(invitationData);
    }
  }, [invitationData]);

  const handleAcceptInvitation = async () => {
    if (!user?.id || !invitationToken) {
      setError('Bruker ikke autentisert eller invitasjonstoken mangler');
      return;
    }

    setIsAccepting(true);
    setError('');

    try {
      const result = await acceptInvitation({
        invitationToken,
        clerkUserId: user.id,
      });

      setAcceptanceResult(result);
    } catch (err) {
      console.error('Failed to accept invitation:', err);
      setError(err instanceof Error ? err.message : 'Kunne ikke akseptere invitasjon');
    } finally {
      setIsAccepting(false);
    }
  };

  const handleSignUpWithInvitation = () => {
    // Preserve invitation token in URL when redirecting to sign-up
    const signUpUrl = `/sign-up?redirect=${encodeURIComponent(`/team-onboarding?token=${invitationToken}`)}`;
    navigate(signUpUrl);
  };

  const handleSignInWithInvitation = () => {
    // Preserve invitation token in URL when redirecting to sign-in
    const signInUrl = `/sign-in?redirect=${encodeURIComponent(`/team-onboarding?token=${invitationToken}`)}`;
    navigate(signInUrl);
  };

  // Loading state while Clerk loads or invitation data loads
  if (!isLoaded || (invitationToken && !invitationData && !error)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-jobblogg-neutral/20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
          <BodyText className="text-jobblogg-text-muted">
            {!isLoaded ? 'Laster autentisering...' : 'Laster invitasjonsinformasjon...'}
          </BodyText>
        </div>
      </div>
    );
  }

  // Error state (invalid or expired invitation)
  if (error && !invitationData) {
    return (
      <PageLayout
        title="Invitasjon ikke funnet"
        subtitle="Det oppstod et problem med invitasjonslinken"
        containerWidth="narrow"
      >
        <div className="text-center space-y-8">
          {/* Error Icon */}
          <div className="w-20 h-20 mx-auto bg-jobblogg-error/10 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          <div className="space-y-4">
            <Heading1 className="text-jobblogg-error">Ugyldig invitasjon</Heading1>
            <BodyText className="text-lg">{error}</BodyText>
          </div>

          <PrimaryButton onClick={() => navigate('/')}>
            Gå til JobbLogg
          </PrimaryButton>
        </div>
      </PageLayout>
    );
  }

  // Success state
  if (acceptanceResult) {
    return (
      <PageLayout 
        title="Velkommen til teamet!"
        subtitle="Du er nå medlem av teamet og kan begynne å samarbeide på prosjekter"
        containerWidth="narrow"
      >
        <div className="text-center space-y-8">
          {/* Success Icon */}
          <div className="w-20 h-20 mx-auto bg-jobblogg-success/10 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>

          {/* Success Message */}
          <div className="space-y-4">
            <Heading1 className="text-jobblogg-success">Invitasjon akseptert!</Heading1>
            <BodyText className="text-lg">
              {acceptanceResult.message}
            </BodyText>
          </div>

          {/* Team Information */}
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
            <Heading2>Din rolle i teamet</Heading2>
            
            <div className="grid gap-4 sm:grid-cols-2">
              <StatsCard
                title="Rolle"
                value={acceptanceResult.role === 'administrator' ? 'Administrator' : 'Utførende'}
                variant="primary"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                }
              />
              
              <StatsCard
                title="Bedrift"
                value={acceptanceResult.companyName}
                variant="accent"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                }
              />
            </div>

            <div className="bg-jobblogg-primary/5 rounded-lg p-4 border border-jobblogg-primary/20">
              <BodyText className="text-sm">
                <strong>Som {acceptanceResult.role === 'administrator' ? 'Administrator' : 'Utførende'} kan du:</strong>
              </BodyText>
              <ul className="text-sm text-jobblogg-text-medium mt-2 space-y-1">
                <li>• Opprette og administrere prosjekter</li>
                <li>• Legge til loggoppføringer og bilder</li>
                <li>• Samarbeide med andre teammedlemmer</li>
                {acceptanceResult.role === 'administrator' && (
                  <li>• Invitere nye teammedlemmer</li>
                )}
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <PrimaryButton
              onClick={() => navigate('/')}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
                </svg>
              }
            >
              Gå til dashboard
            </PrimaryButton>
            
            {acceptanceResult.role === 'administrator' && (
              <SecondaryButton
                onClick={() => navigate('/team')}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121m0 0a5.002 5.002 0 019.608 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                }
              >
                Administrer team
              </SecondaryButton>
            )}
          </div>
        </div>
      </PageLayout>
    );
  }

  // Show different UI based on authentication status
  if (!isSignedIn) {
    // User is not signed in - show invitation info and sign up/in options
    return (
      <PageLayout
        title="Teammedlem invitasjon"
        subtitle="Du har blitt invitert til å bli med i et team på JobbLogg"
        containerWidth="narrow"
      >
        <div className="text-center space-y-8">
          {/* Invitation Icon */}
          <div className="w-20 h-20 mx-auto bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
            <svg className="w-10 h-10 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121m0 0a5.002 5.002 0 019.608 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>

          {/* Welcome Message */}
          <div className="space-y-4">
            <Heading1>Bli med i teamet!</Heading1>
            <BodyText className="text-lg">
              Du har mottatt en invitasjon til å bli teammedlem på JobbLogg.
            </BodyText>
          </div>

          {/* Invitation Details */}
          {invitationInfo && (
            <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
              <Heading2>Invitasjonsdetaljer</Heading2>

              <div className="grid gap-4 sm:grid-cols-2">
                <div className="text-left">
                  <TextMuted className="text-sm font-medium">Bedrift</TextMuted>
                  <BodyText className="font-medium">{invitationInfo.companyName}</BodyText>
                </div>

                <div className="text-left">
                  <TextMuted className="text-sm font-medium">Rolle</TextMuted>
                  <BodyText className="font-medium">
                    {invitationInfo.role === 'administrator' ? 'Administrator' : 'Utførende'}
                  </BodyText>
                </div>
              </div>

              <div className="text-left">
                <TextMuted className="text-sm font-medium">Invitert av</TextMuted>
                <BodyText className="font-medium">{invitationInfo.inviterName}</BodyText>
              </div>

              {invitationInfo.invitedAt && (
                <div className="text-left">
                  <TextMuted className="text-sm font-medium">Invitert</TextMuted>
                  <BodyText className="font-medium">
                    {new Date(invitationInfo.invitedAt).toLocaleDateString('no-NO')}
                  </BodyText>
                </div>
              )}
            </div>
          )}

          {/* Authentication Options */}
          <div className="bg-jobblogg-primary/5 rounded-xl border border-jobblogg-primary/20 p-6 space-y-4">
            <Heading2>For å akseptere invitasjonen</Heading2>
            <BodyText>
              Du må først opprette en konto eller logge inn for å bli med i teamet.
            </BodyText>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <PrimaryButton
                onClick={handleSignUpWithInvitation}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                }
              >
                Opprett konto
              </PrimaryButton>

              <SecondaryButton
                onClick={handleSignInWithInvitation}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                }
              >
                Logg inn
              </SecondaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // User is signed in - show invitation acceptance UI
  return (
    <PageLayout
      title="Teammedlem invitasjon"
      subtitle="Du har blitt invitert til å bli med i et team på JobbLogg"
      containerWidth="narrow"
    >
      <div className="text-center space-y-8">
        {/* Invitation Icon */}
        <div className="w-20 h-20 mx-auto bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
          <svg className="w-10 h-10 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 515.196-2.121m0 0a5.002 5.002 0 919.608 0M15 7a3 3 0 11-6 0 3 3 0 616 0z" />
          </svg>
        </div>

        {/* Welcome Message */}
        <div className="space-y-4">
          <Heading1>Bli med i teamet!</Heading1>
          <BodyText className="text-lg">
            Du har mottatt en invitasjon til å bli teammedlem på JobbLogg.
            Som teammedlem kan du samarbeide på prosjekter og dele arbeid med kolleger.
          </BodyText>
        </div>

        {/* User Information */}
        {user && (
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
            <Heading2 className="mb-4">Din brukerinformasjon</Heading2>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-jobblogg-primary/10 flex items-center justify-center">
                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div className="text-left">
                <BodyText className="font-medium">
                  {user.fullName || user.firstName || 'Bruker'}
                </BodyText>
                <TextMuted className="text-sm">
                  {user.primaryEmailAddress?.emailAddress}
                </TextMuted>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <FormError>{error}</FormError>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <PrimaryButton
            onClick={handleAcceptInvitation}
            disabled={isAccepting || !invitationToken || !user}
            icon={isAccepting ? (
              <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          >
            {isAccepting ? 'Aksepterer invitasjon...' : 'Aksepter invitasjon'}
          </PrimaryButton>
          
          <SecondaryButton
            onClick={() => navigate('/')}
            disabled={isAccepting}
          >
            Avbryt
          </SecondaryButton>
        </div>
      </div>
    </PageLayout>
  );
};

export default TeamOnboarding;
