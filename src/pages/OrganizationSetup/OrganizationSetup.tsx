import React from 'react';
import { useOrganization, useUser, useOrganizationList } from '@clerk/clerk-react';
import { 
  PageLayout, 
  Heading1, 
  Heading2, 
  BodyText, 
  PrimaryButton, 
  SecondaryButton,
  Card 
} from '../../components/ui';

export const OrganizationSetup: React.FC = () => {
  const { user } = useUser();
  const { organization, isLoaded } = useOrganization();
  const { createOrganization } = useOrganizationList();

  if (!isLoaded) {
    return (
      <PageLayout containerWidth="narrow">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
        </div>
      </PageLayout>
    );
  }

  const handleCreateOrganization = async () => {
    if (!user) return;
    
    try {
      await createOrganization?.({
        name: `${user.firstName || 'Min'} Bedrift`,
      });
    } catch (error) {
      console.error('Failed to create organization:', error);
    }
  };

  return (
    <PageLayout containerWidth="narrow">
      <div className="space-y-8">
        <div className="text-center">
          <Heading1 className="mb-4">Organisasjon Setup</Heading1>
          <BodyText className="text-jobblogg-text-medium">
            Konfigurer din organisasjon for å aktivere automatisk e-postutsending av invitasjoner.
          </BodyText>
        </div>

        {organization ? (
          <Card className="p-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-jobblogg-success-soft rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              
              <div>
                <Heading2 className="text-jobblogg-success mb-2">
                  Organisasjon Aktivert! 🎉
                </Heading2>
                <BodyText className="text-jobblogg-text-medium">
                  <strong>{organization.name}</strong> er nå konfigurert.
                </BodyText>
              </div>

              <div className="bg-jobblogg-success-soft border border-jobblogg-success rounded-lg p-4">
                <BodyText className="text-sm text-jobblogg-text-strong">
                  ✅ Automatisk e-postutsending er nå aktivert<br/>
                  ✅ Invitasjoner sendes direkte til teammedlemmer<br/>
                  ✅ Ingen manuell deling nødvendig
                </BodyText>
              </div>

              <div className="flex gap-3 justify-center">
                <SecondaryButton onClick={() => window.location.href = '/team'}>
                  Gå til Teamadministrasjon
                </SecondaryButton>
                <PrimaryButton onClick={() => window.location.href = '/'}>
                  Tilbake til Dashboard
                </PrimaryButton>
              </div>
            </div>
          </Card>
        ) : (
          <Card className="p-6">
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>

              <div>
                <Heading2 className="mb-2">Opprett Organisasjon</Heading2>
                <BodyText className="text-jobblogg-text-medium">
                  For å aktivere automatisk e-postutsending av invitasjoner, må du opprette en organisasjon.
                </BodyText>
              </div>

              <div className="bg-jobblogg-info-soft border border-jobblogg-info rounded-lg p-4 text-left">
                <BodyText className="text-sm text-jobblogg-text-strong mb-2">
                  <strong>Hva skjer når du oppretter en organisasjon:</strong>
                </BodyText>
                <BodyText className="text-sm text-jobblogg-text-medium">
                  • Automatisk e-postutsending aktiveres<br/>
                  • Du blir administrator for organisasjonen<br/>
                  • Teammedlemmer kan inviteres via e-post<br/>
                  • Roller og tilganger administreres automatisk
                </BodyText>
              </div>

              <div className="space-y-3">
                <PrimaryButton 
                  onClick={handleCreateOrganization}
                  className="w-full"
                >
                  Opprett Organisasjon
                </PrimaryButton>
                
                <SecondaryButton 
                  onClick={() => window.location.href = '/team'}
                  className="w-full"
                >
                  Fortsett uten organisasjon (manuell deling)
                </SecondaryButton>
              </div>

              <BodyText className="text-xs text-jobblogg-text-muted">
                Du kan alltid opprette en organisasjon senere for å aktivere automatisk e-postutsending.
              </BodyText>
            </div>
          </Card>
        )}

        {/* Instructions Card */}
        <Card className="p-6">
          <Heading2 className="mb-4">Konfigurasjonsguide</Heading2>
          <div className="space-y-4">
            <div>
              <BodyText className="font-medium text-jobblogg-text-strong mb-2">
                1. Clerk Dashboard Konfigurering:
              </BodyText>
              <BodyText className="text-sm text-jobblogg-text-medium">
                • Gå til <a href="https://dashboard.clerk.com" target="_blank" rel="noopener noreferrer" className="text-jobblogg-primary hover:underline">Clerk Dashboard</a><br/>
                • Aktiver "Organizations" feature<br/>
                • Konfigurer roller: "daglig_leder" (Daglig Leder) og "utfoerende" (Utførende)
              </BodyText>
            </div>

            <div>
              <BodyText className="font-medium text-jobblogg-text-strong mb-2">
                2. E-post Konfigurering:
              </BodyText>
              <BodyText className="text-sm text-jobblogg-text-medium">
                • Clerk sender automatisk invitasjons-e-poster<br/>
                • E-postene inneholder sikre invitasjonslinker<br/>
                • Mottakere kan akseptere invitasjoner direkte
              </BodyText>
            </div>

            <div>
              <BodyText className="font-medium text-jobblogg-text-strong mb-2">
                3. Roller og Tilganger:
              </BodyText>
              <BodyText className="text-sm text-jobblogg-text-medium">
                • <strong>Daglig Leder:</strong> Kan invitere medlemmer og administrere organisasjonen<br/>
                • <strong>Utførende:</strong> Kan opprette og administrere prosjekter
              </BodyText>
            </div>
          </div>
        </Card>
      </div>
    </PageLayout>
  );
};

export default OrganizationSetup;
