import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  PageLayout,
  Heading1,
  Heading2,
  TextMedium,
  TextMuted,
  PrimaryButton,
  Card
} from '../../components/ui';

interface PricingTier {
  id: string;
  name: string;
  description: string;
  employeeRange: string;
  monthlyPrice: number;
  annualPrice: number;
  isPopular?: boolean;
  isEnterprise?: boolean;
}

// PRICING CALCULATION VERIFICATION:
// Annual pricing provides exactly 20% discount as advertised
// Formula: monthlyPrice * 12 * 0.8 = annualPrice
//
// Basic: 299 * 12 * 0.8 = 2870.4 → 2870 NOK/year (239 NOK/month when billed annually)
// Professional: 999 * 12 * 0.8 = 9590.4 → 9590 NOK/year (799 NOK/month when billed annually)
// Enterprise: 2999 * 12 * 0.8 = 28790.4 → 28790 NOK/year (2399 NOK/month when billed annually)

const pricingTiers: PricingTier[] = [
  {
    id: 'small',
    name: 'Liten bedrift',
    description: 'Perfekt for enkeltmannsforetak og små team',
    employeeRange: '1–9 ansatte',
    monthlyPrice: 299,
    annualPrice: 2870, // Exactly 20% discount (299 * 12 * 0.8 = 2870.4)
    isPopular: true
  },
  {
    id: 'medium',
    name: 'Mellomstor bedrift',
    description: 'For voksende entreprenørfirmaer',
    employeeRange: '10–49 ansatte',
    monthlyPrice: 999,
    annualPrice: 9590 // Exactly 20% discount (999 * 12 * 0.8 = 9590.4)
  },
  {
    id: 'large',
    name: 'Stor bedrift',
    description: 'For etablerte byggefirmaer',
    employeeRange: '50–249 ansatte',
    monthlyPrice: 2999,
    annualPrice: 28790 // Exactly 20% discount (2999 * 12 * 0.8 = 28790.4)
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Tilpassede løsninger for store konsern',
    employeeRange: '250+ ansatte',
    monthlyPrice: 0,
    annualPrice: 0,
    isEnterprise: true
  }
];

const features = [
  'Ubegrensede prosjekter og loggoppføringer',
  'Full chat-funksjonalitet med filvedlegg',
  'Prosjektdeling med kunder',
  'Team-administrasjon og rolletildeling',
  'Kundedatabase med Brønnøysundregisteret-integrasjon',
  'Prosjektarkivering og historikk',
  'Real-time kommunikasjon',
  'Mobil-optimalisert opplevelse',
  'Underentreprenør-involvering i prosjekter',
  'Norsk kundesupport',
  'GDPR-kompatibel datalagring i Norge',
  '7 dagers gratis prøveperiode'
];

export const PricingPage: React.FC = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  const formatPrice = (price: number): string => {
    return price.toString();
  };

  const calculateSavings = (monthly: number, annual: number): number => {
    return (monthly * 12) - annual;
  };

  return (
    <PageLayout
      title="Priser"
      containerWidth="wide"
      className="bg-white"
    >
      <div className="space-y-16 sm:space-y-20">
        {/* Back Navigation */}
        <div className="flex justify-start">
          <Link
            to="/"
            className="inline-flex items-center gap-2 text-jobblogg-text-medium hover:text-jobblogg-primary font-medium transition-all duration-200 focus-ring rounded-lg px-3 py-2 group"
            aria-label="Tilbake til hovedsiden"
          >
            <svg className="w-4 h-4 transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Tilbake til hovedsiden
          </Link>
        </div>

        {/* Hero Section */}
        <div className="text-center space-y-6 sm:space-y-8">
          <div className="space-y-4">
            <Heading1 className="text-3xl sm:text-4xl lg:text-5xl font-bold">
              Enkel prising for alle bedriftsstørrelser
            </Heading1>
            <TextMedium className="text-lg sm:text-xl max-w-3xl mx-auto text-jobblogg-text-medium">
              Full funksjonalitet til en fast pris per bedriftskategori. Ingen skjulte kostnader eller per-bruker avgifter.
            </TextMedium>
          </div>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 p-1 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
            <button
              onClick={() => setIsAnnual(false)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                !isAnnual 
                  ? 'bg-white text-jobblogg-primary shadow-sm' 
                  : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
              }`}
            >
              Månedlig
            </button>
            <button
              onClick={() => setIsAnnual(true)}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 relative ${
                isAnnual 
                  ? 'bg-white text-jobblogg-primary shadow-sm' 
                  : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
              }`}
            >
              Årlig
              <span className="absolute -top-2 -right-2 bg-jobblogg-accent text-white text-xs px-2 py-1 rounded-full">
                -20%
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {pricingTiers.map((tier) => (
            <Card
              key={tier.id}
              className={`relative p-6 sm:p-8 hover:shadow-lg transition-all duration-300 ${
                tier.isPopular ? 'bg-jobblogg-primary-soft border-jobblogg-primary shadow-lg' : ''
              }`}
            >
              {tier.isPopular && (
                <div className="absolute -top-0 -right-0 bg-jobblogg-primary text-white px-3 py-1 rounded-bl-lg rounded-tr-xl text-sm font-medium">
                  Mest populær
                </div>
              )}

              <div className="space-y-6">
                {/* Header */}
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-jobblogg-text-strong">
                    {tier.name}
                  </h3>
                  <TextMuted className="text-sm">
                    {tier.employeeRange}
                  </TextMuted>
                  <TextMedium className="text-sm">
                    {tier.description}
                  </TextMedium>
                </div>

                {/* Price */}
                <div className="space-y-2">
                  {tier.isEnterprise ? (
                    <div className="space-y-1">
                      <div className="text-3xl font-bold text-jobblogg-text-strong">
                        Tilpasset pris
                      </div>
                      <TextMuted className="text-sm">
                        Kontakt oss for tilbud
                      </TextMuted>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      <div className="flex items-baseline space-x-1">
                        <span className="text-3xl font-bold text-jobblogg-text-strong">
                          {formatPrice(isAnnual ? Math.round(tier.annualPrice / 12) : tier.monthlyPrice)}&nbsp;kr
                        </span>
                        <span className="text-lg text-jobblogg-text-muted">
                          / md.
                        </span>
                      </div>
                      <div className="text-sm text-jobblogg-text-muted">
                        ekskl. mva.
                      </div>
                      <div className="text-sm text-jobblogg-text-muted">
                        7 dagers gratis prøveperiode
                      </div>
                      {isAnnual && (
                        <div className="text-sm text-jobblogg-accent font-medium">
                          Spar {formatPrice(calculateSavings(tier.monthlyPrice, tier.annualPrice))}&nbsp;kr/år
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* CTA Button */}
                <div className="pt-4">
                  {tier.isEnterprise ? (
                    <PrimaryButton
                      fullWidth
                      className="min-h-[44px]"
                      ariaLabel={`Kontakt oss for ${tier.name}`}
                    >
                      Kontakt oss
                    </PrimaryButton>
                  ) : (
                    <Link to="/sign-up" className="block">
                      <PrimaryButton
                        fullWidth
                        className="min-h-[44px]"
                        ariaLabel={`Start gratis prøveperiode for ${tier.name}`}
                      >
                        Start gratis prøveperiode
                      </PrimaryButton>
                    </Link>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Features Section */}
        <div className="bg-jobblogg-surface rounded-2xl p-8 sm:p-12">
          <div className="text-center space-y-6 mb-12">
            <Heading2 className="text-2xl sm:text-3xl font-bold">
              Alt inkludert i alle planer
            </Heading2>
            <TextMedium className="text-lg max-w-2xl mx-auto text-jobblogg-text-medium">
              Samme kraftige funksjonalitet uansett bedriftsstørrelse. Ingen begrensninger eller skjulte kostnader.
            </TextMedium>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className="flex items-start space-x-3 p-4 bg-white rounded-xl border border-jobblogg-border"
              >
                <div className="flex-shrink-0 w-5 h-5 bg-jobblogg-accent rounded-full flex items-center justify-center mt-0.5">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <TextMedium className="text-sm text-jobblogg-text-strong">
                  {feature}
                </TextMedium>
              </div>
            ))}
          </div>
        </div>

        {/* Value Proposition Section */}
        <div className="text-center space-y-8 sm:space-y-12">
          <div className="space-y-4">
            <Heading2 className="text-2xl sm:text-3xl font-bold">
              Fastpris – enklere for deg
            </Heading2>
            <TextMedium className="text-lg max-w-3xl mx-auto text-jobblogg-text-medium">
              Vi tror på enkel og forutsigbar prising som vokser med din bedrift, ikke mot den.
            </TextMedium>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-4">
              <div className="w-12 h-12 bg-jobblogg-primary-soft rounded-xl flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">
                  Forutsigbare kostnader
                </h3>
                <TextMedium className="text-sm text-jobblogg-text-medium">
                  Ingen overraskelser på regningen. Fast pris uansett hvor mange ansatte som bruker systemet.
                </TextMedium>
              </div>
            </div>

            <div className="space-y-4">
              <div className="w-12 h-12 bg-jobblogg-accent-soft rounded-xl flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">
                  Skalerbar løsning
                </h3>
                <TextMedium className="text-sm text-jobblogg-text-medium">
                  Voks fra 1 til 249 ansatte uten å bekymre deg for økende per-bruker kostnader.
                </TextMedium>
              </div>
            </div>

            <div className="space-y-4">
              <div className="w-12 h-12 bg-jobblogg-warning-soft rounded-xl flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong">
                  Full funksjonalitet
                </h3>
                <TextMedium className="text-sm text-jobblogg-text-medium">
                  Alle funksjoner inkludert fra dag én. Ingen oppgraderinger eller tilleggskostnader.
                </TextMedium>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="space-y-8 sm:space-y-12">
          <div className="text-center space-y-4">
            <Heading2 className="text-2xl sm:text-3xl font-bold">
              Ofte stilte spørsmål
            </Heading2>
            <TextMedium className="text-lg max-w-2xl mx-auto text-jobblogg-text-medium">
              Få svar på de vanligste spørsmålene om prising og funksjonalitet.
            </TextMedium>
          </div>

          <div className="space-y-4">
            <div className="bg-white rounded-xl border border-jobblogg-border p-6">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-3">
                Hva skjer hvis vi vokser utover vår nåværende kategori?
              </h3>
              <TextMedium className="text-jobblogg-text-medium">
                Du oppgraderer enkelt til neste kategori når du trenger det. Alle data og innstillinger følger med, og du får tilgang til samme funksjonalitet som før.
              </TextMedium>
            </div>

            <div className="bg-white rounded-xl border border-jobblogg-border p-6">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-3">
                Er det noen skjulte kostnader eller tilleggsavgifter?
              </h3>
              <TextMedium className="text-jobblogg-text-medium">
                Nei, prisen du ser er alt du betaler. Ingen oppstartsavgifter, ingen per-bruker kostnader, og ingen skjulte tillegg. Alle funksjoner er inkludert.
              </TextMedium>
            </div>

            <div className="bg-white rounded-xl border border-jobblogg-border p-6">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-3">
                Kan jeg bytte mellom månedlig og årlig fakturering?
              </h3>
              <TextMedium className="text-jobblogg-text-medium">
                Ja, du kan når som helst bytte mellom månedlig og årlig fakturering. Ved oppgradering til årlig fakturering får du 20% rabatt.
              </TextMedium>
            </div>

            <div className="bg-white rounded-xl border border-jobblogg-border p-6">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-3">
                Hvor lang er den gratis prøveperioden?
              </h3>
              <TextMedium className="text-jobblogg-text-medium">
                Du får 7 dager gratis tilgang til alle funksjoner. Ingen kredittkort påkrevd for å starte prøveperioden.
              </TextMedium>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center space-y-6 bg-gradient-to-br from-jobblogg-primary to-jobblogg-primary-light rounded-2xl p-8 sm:p-12 text-white">
          <div className="space-y-4">
            <Heading2 className="text-2xl sm:text-3xl font-bold text-white">
              Klar til å komme i gang?
            </Heading2>
            <TextMedium className="text-lg text-white/90 max-w-2xl mx-auto">
              Prøv gratis i 7 dager – helt uten binding eller oppstartsgebyr.
            </TextMedium>
          </div>

          <div className="flex justify-center pt-4">
            <Link to="/sign-up" className="w-full sm:w-auto">
              <button
                className="w-full sm:w-auto min-w-[200px] bg-white text-jobblogg-primary hover:bg-gray-50 border border-white rounded-xl px-6 py-3.5 text-base sm:text-lg min-h-[48px] font-semibold inline-flex items-center justify-center gap-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-jobblogg-primary shadow-soft hover:shadow-medium"
                aria-label="Start gratis prøveperiode på 7 dager"
              >
                Start gratis prøveperiode
              </button>
            </Link>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default PricingPage;
