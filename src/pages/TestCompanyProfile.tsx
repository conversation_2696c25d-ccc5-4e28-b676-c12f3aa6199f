import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { 
  PageLayout, 
  PrimaryButton, 
  SecondaryButton, 
  TextStrong, 
  TextMedium, 
  TextMuted,
  CompanyProfileModal 
} from '../components/ui';

/**
 * Test page for Company Profile functionality
 * 
 * This page allows testing of the company profile modal
 * and related functionality in a controlled environment.
 */
const TestCompanyProfile: React.FC = () => {
  const { user } = useUser();
  const [showModal, setShowModal] = useState(false);

  // Get contractor company data
  // TODO: Re-enable when type instantiation issue is resolved
  // const contractorCompany = useQuery(
  //   api.contractorCompany.getContractorCompanyWithDetails,
  //   user?.id ? { clerkUserId: user.id } : "skip"
  // );
  const contractorCompany = undefined; // Temporarily disabled due to type instantiation issues

  // Get contractor onboarding status
  // TODO: Re-enable when type instantiation issue is resolved
  // const onboardingStatus = useQuery(
  //   api.contractorOnboarding.getContractorOnboardingStatus,
  //   user?.id ? { clerkUserId: user.id } : "skip"
  // );
  const onboardingStatus = undefined; // Temporarily disabled due to type instantiation issues

  // Provide fallback structure to prevent property access errors
  const company = contractorCompany || {
    name: '',
    orgNumber: '',
    contactPerson: '',
    phone: '',
    email: '',
    streetAddress: '',
    postalCode: '',
    city: '',
    entrance: '',
    notes: '',
    useCustomAddress: false,
    brregFetchedAt: null
  };

  if (!user) {
    return (
      <PageLayout title="Test Bedriftsprofil" showBackButton backUrl="/">
        <div className="text-center py-8">
          <TextMuted>Du må være logget inn for å teste bedriftsprofil-funksjonaliteten.</TextMuted>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title="Test Bedriftsprofil" 
      showBackButton 
      backUrl="/"
      headerActions={
        <PrimaryButton
          onClick={() => setShowModal(true)}
          disabled={!contractorCompany}
        >
          Åpne Bedriftsprofil
        </PrimaryButton>
      }
    >
      <div className="space-y-6">
        {/* User Information */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
          <TextStrong as="h2" className="mb-4">Brukerinformasjon</TextStrong>
          <div className="space-y-2">
            <div>
              <TextMedium><strong>Clerk User ID:</strong> {user.id}</TextMedium>
            </div>
            <div>
              <TextMedium><strong>E-post:</strong> {user.primaryEmailAddress?.emailAddress}</TextMedium>
            </div>
            <div>
              <TextMedium><strong>Navn:</strong> {user.fullName || 'Ikke satt'}</TextMedium>
            </div>
          </div>
        </div>

        {/* Onboarding Status */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
          <TextStrong as="h2" className="mb-4">Onboarding Status</TextStrong>
          {onboardingStatus === undefined ? (
            <TextMuted>Laster onboarding status...</TextMuted>
          ) : onboardingStatus ? (
            <div className="space-y-2">
              <div>
                <TextMedium><strong>Fullført:</strong> {(onboardingStatus as any)?.contractorCompleted ? 'Ja' : 'Nei'}</TextMedium>
              </div>
              <div>
                <TextMedium><strong>Bedrift ID:</strong> {(onboardingStatus as any)?.contractorCompanyId || 'Ikke satt'}</TextMedium>
              </div>
            </div>
          ) : (
            <TextMuted>Ingen onboarding status funnet</TextMuted>
          )}
        </div>

        {/* Company Information */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
          <TextStrong as="h2" className="mb-4">Bedriftsinformasjon</TextStrong>
          {contractorCompany === undefined ? (
            <TextMuted>Laster bedriftsinformasjon...</TextMuted>
          ) : contractorCompany ? (
            <div className="space-y-3">
              <div>
                <TextMedium><strong>Bedriftsnavn:</strong> {company.name}</TextMedium>
              </div>
              <div>
                <TextMedium><strong>Org.nr:</strong> {company.orgNumber}</TextMedium>
              </div>
              <div>
                <TextMedium><strong>Kontaktperson:</strong> {company.contactPerson}</TextMedium>
              </div>
              <div>
                <TextMedium><strong>Telefon:</strong> {company.phone || 'Ikke satt'}</TextMedium>
              </div>
              <div>
                <TextMedium><strong>E-post:</strong> {company.email || 'Ikke satt'}</TextMedium>
              </div>
              <div>
                <TextMedium><strong>Adresse:</strong> {company.streetAddress ?
                  `${company.streetAddress}, ${company.postalCode} ${company.city}` :
                  'Ikke satt'
                }</TextMedium>
              </div>
              {company.entrance && (
                <div>
                  <TextMedium><strong>Inngang:</strong> {company.entrance}</TextMedium>
                </div>
              )}
              {company.notes && (
                <div>
                  <TextMedium><strong>Notater:</strong> {company.notes}</TextMedium>
                </div>
              )}
              <div>
                <TextMedium><strong>Tilpasset adresse:</strong> {company.useCustomAddress ? 'Ja' : 'Nei'}</TextMedium>
              </div>
              {company.brregFetchedAt && (
                <div>
                  <TextMedium><strong>Sist oppdatert fra Brreg:</strong> {new Date(company.brregFetchedAt).toLocaleString('nb-NO')}</TextMedium>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <TextMuted>Ingen bedriftsinformasjon funnet. Du må fullføre contractor onboarding først.</TextMuted>
              <SecondaryButton onClick={() => window.location.href = '/contractor-onboarding'}>
                Gå til Contractor Onboarding
              </SecondaryButton>
            </div>
          )}
        </div>

        {/* Test Actions */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
          <TextStrong as="h2" className="mb-4">Test Handlinger</TextStrong>
          <div className="flex flex-wrap gap-3">
            <PrimaryButton
              onClick={() => setShowModal(true)}
              disabled={!contractorCompany}
            >
              Åpne Bedriftsprofil Modal
            </PrimaryButton>
            
            <SecondaryButton
              onClick={() => window.location.reload()}
            >
              Oppdater Side
            </SecondaryButton>

            <SecondaryButton
              onClick={() => console.log('Company data:', contractorCompany)}
            >
              Log Bedriftsdata
            </SecondaryButton>
          </div>
          
          {!contractorCompany && (
            <div className="mt-4 p-4 bg-jobblogg-warning-soft border border-jobblogg-warning rounded-lg">
              <TextMedium className="text-jobblogg-warning-dark">
                <strong>Merk:</strong> Bedriftsprofil modal er deaktivert fordi ingen bedriftsinformasjon ble funnet. 
                Fullfør contractor onboarding først.
              </TextMedium>
            </div>
          )}
        </div>

        {/* API Status */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
          <TextStrong as="h2" className="mb-4">API Status</TextStrong>
          <div className="space-y-2">
            <div>
              <TextMedium><strong>Contractor Company Query:</strong> {
                contractorCompany === undefined ? 'Loading...' : 
                contractorCompany ? 'Success' : 'No data'
              }</TextMedium>
            </div>
            <div>
              <TextMedium><strong>Onboarding Status Query:</strong> {
                onboardingStatus === undefined ? 'Loading...' : 
                onboardingStatus ? 'Success' : 'No data'
              }</TextMedium>
            </div>
          </div>
        </div>
      </div>

      {/* Company Profile Modal */}
      <CompanyProfileModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSuccess={() => {
          console.log('Company profile updated successfully!');
          // Optionally show a toast notification here
        }}
      />
    </PageLayout>
  );
};

export default TestCompanyProfile;
