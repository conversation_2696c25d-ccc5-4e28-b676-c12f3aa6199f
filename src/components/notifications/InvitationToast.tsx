import React, { useEffect, useState } from 'react';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { useUser } from '@clerk/clerk-react'; // TODO: Re-enable when needed
import { useNavigate } from 'react-router-dom';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { getSpecializationById } from '../../utils/specializations';

interface InvitationToastProps {
  className?: string;
}

export const InvitationToast: React.FC<InvitationToastProps> = ({ className = '' }) => {
  // const { user } = useUser(); // TODO: Re-enable when needed
  const navigate = useNavigate();
  const [dismissedInvitations, setDismissedInvitations] = useState<Set<string>>(new Set());
  const [showToast, setShowToast] = useState(false);

  // Query pending incoming invitations only (not outgoing invitations sent by this user)
  // TODO: Re-enable when type instantiation issue is resolved
  // const pendingInvitations = useQuery(
  //   api.subcontractorInvitations.getIncomingSubcontractorInvitations,
  //   user?.id ? {
  //     userId: user.id,
  //     status: 'pending',
  //   } : "skip"
  // );
  const pendingInvitations: any[] = []; // Temporarily provide fallback array due to type instantiation issues

  // Get the most recent invitation that hasn't been dismissed
  const latestInvitation = pendingInvitations?.find(
    invitation => !dismissedInvitations.has(invitation._id)
  );

  // Show toast when there's a new invitation
  useEffect(() => {
    if (latestInvitation) {
      setShowToast(true);
      
      // Auto-hide after 10 seconds
      const timer = setTimeout(() => {
        setShowToast(false);
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, [latestInvitation?._id]);

  const handleViewInvitation = () => {
    if (latestInvitation) {
      navigate(`/invitations/${latestInvitation._id}`);
      setShowToast(false);
    }
  };

  const handleViewAll = () => {
    navigate('/invitations');
    setShowToast(false);
  };

  const handleDismiss = () => {
    if (latestInvitation) {
      setDismissedInvitations(prev => new Set(prev).add(latestInvitation._id));
    }
    setShowToast(false);
  };

  if (!showToast || !latestInvitation) {
    return null;
  }

  const formatUrgency = (urgency?: string) => {
    switch (urgency) {
      case 'high': return { text: 'Høy prioritet', color: 'text-jobblogg-error' };
      case 'medium': return { text: 'Middels prioritet', color: 'text-jobblogg-warning' };
      case 'low': return { text: 'Lav prioritet', color: 'text-jobblogg-success' };
      default: return { text: 'Ikke oppgitt', color: 'text-jobblogg-text-medium' };
    }
  };

  const urgencyInfo = formatUrgency(latestInvitation.urgency);

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border max-w-sm w-full animate-slide-down">
        {/* Header */}
        <div className="px-4 py-3 bg-gradient-to-r from-jobblogg-accent to-jobblogg-primary rounded-t-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <h3 className="text-sm font-semibold text-white">
                Ny prosjektinvitasjon
              </h3>
            </div>
            <button
              onClick={handleDismiss}
              className="text-white/80 hover:text-white transition-colors duration-200"
              aria-label="Lukk notifikasjon"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="space-y-3">
            {/* Project Info */}
            <div>
              <h4 className="font-semibold text-jobblogg-text-strong text-sm">
                {latestInvitation.projectPreview?.name || 'Prosjekt'}
              </h4>
              <p className="text-xs text-jobblogg-text-medium">
                {latestInvitation.projectPreview?.mainContractorCompany}
              </p>
            </div>

            {/* Details */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 00-2 2H8a2 2 0 00-2-2V6m8 0h2a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h2" />
                </svg>
                <span className="text-xs text-jobblogg-text-medium">
                  {getSpecializationById(latestInvitation.subcontractorSpecialization || '')?.name || latestInvitation.subcontractorSpecialization}
                </span>
              </div>
              
              {latestInvitation.estimatedDuration && (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-xs text-jobblogg-text-medium">
                    {latestInvitation.estimatedDuration}
                  </span>
                </div>
              )}

              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className={`text-xs ${urgencyInfo.color}`}>
                  {urgencyInfo.text}
                </span>
              </div>
            </div>

            {/* Personal Message Preview */}
            {latestInvitation.invitationMessage && (
              <div className="bg-jobblogg-neutral/50 p-2 rounded-lg">
                <p className="text-xs text-jobblogg-text-medium line-clamp-2">
                  <strong>Melding:</strong> {latestInvitation.invitationMessage}
                </p>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2 mt-4">
            <button
              onClick={handleViewInvitation}
              className="flex-1 bg-jobblogg-primary text-white text-xs font-medium py-2 px-3 rounded-lg hover:bg-jobblogg-primary-hover transition-colors duration-200"
            >
              Se invitasjon
            </button>
            <button
              onClick={handleViewAll}
              className="text-xs text-jobblogg-text-medium hover:text-jobblogg-text-strong transition-colors duration-200 px-2"
            >
              Se alle
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="h-1 bg-jobblogg-neutral rounded-b-xl overflow-hidden">
          <div 
            className="h-full bg-gradient-to-r from-jobblogg-accent to-jobblogg-primary animate-progress-bar"
            style={{
              animation: 'progress-bar 10s linear forwards'
            }}
          />
        </div>
      </div>

      <style>{`
        @keyframes progress-bar {
          from { width: 100%; }
          to { width: 0%; }
        }
        .animate-progress-bar {
          animation: progress-bar 10s linear forwards;
        }
      `}</style>
    </div>
  );
};

export default InvitationToast;
