import React, { useState, useEffect, useCallback } from 'react';
// import { useQuery, useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
import { useNavigate } from 'react-router-dom';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved

interface ResponseToastProps {
  className?: string;
}

const ResponseToastInner: React.FC<ResponseToastProps> = ({ className = '' }) => {
  const { user } = useUser();
  const navigate = useNavigate();
  const [dismissedNotifications, setDismissedNotifications] = useState<Set<string>>(new Set());
  const [showToast, setShowToast] = useState(false);

  // Query unread response notifications with error handling
  // TODO: Re-enable when type instantiation issue is resolved
  // const responseNotifications = useQuery(
  //   api.subcontractorInvitations.getInvitationResponseNotifications,
  //   user?.id ? {
  //     userId: user.id,
  //     status: 'unread',
  //   } : "skip"
  // );
  const responseNotifications: any[] = []; // Temporarily provide fallback array due to type instantiation issues

  // Mutation to mark notifications as read
  // TODO: Re-enable when type instantiation issue is resolved
  // const markAsRead = useMutation(api.subcontractorInvitations.markResponseNotificationsAsRead);
  const markAsRead = async (args: any) => {
    console.log("⚠️ Mark as read temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Get the most recent notification that hasn't been dismissed
  const latestNotification = responseNotifications?.find(
    notification => !dismissedNotifications.has(notification._id)
  );

  // Define handleDismiss with useCallback to avoid dependency issues
  const handleDismiss = useCallback(async () => {
    if (latestNotification && user) {
      // Add to dismissed set
      setDismissedNotifications(prev => new Set([...prev, latestNotification._id]));

      // Mark as read in database
      try {
        await markAsRead({
          userId: user.id,
          notificationIds: [latestNotification._id],
        });
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
    setShowToast(false);
  }, [latestNotification, markAsRead, user]);

  // Show toast when there's a new notification
  useEffect(() => {
    if (latestNotification) {
      setShowToast(true);
      // Auto-hide after 10 seconds for acceptance, 15 seconds for decline
      const timeout = latestNotification.isAcceptance ? 10000 : 15000;
      const timer = setTimeout(() => {
        handleDismiss();
      }, timeout);

      return () => clearTimeout(timer);
    } else {
      setShowToast(false);
    }
  }, [latestNotification, handleDismiss]);

  const handleViewProject = () => {
    if (latestNotification?.projectInfo) {
      navigate(`/project/${latestNotification.projectInfo._id}`);
      handleDismiss();
    }
  };

  const handleViewInvitations = () => {
    navigate('/invitations');
    handleDismiss();
  };

  const formatTimeAgo = (timeAgo: number) => {
    const minutes = Math.floor(timeAgo / (1000 * 60));
    const hours = Math.floor(timeAgo / (1000 * 60 * 60));
    const days = Math.floor(timeAgo / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d siden`;
    if (hours > 0) return `${hours}t siden`;
    if (minutes > 0) return `${minutes}m siden`;
    return 'Akkurat nå';
  };

  // Early return if no toast should be shown
  if (!showToast || !latestNotification || !user) {
    return null;
  }

  // Safe property access with fallbacks
  const isAcceptance = latestNotification.isAcceptance || false;
  const subcontractorName = latestNotification.data?.subcontractorName || 'Underleverandør';
  const projectName = latestNotification.data?.projectName || 'Prosjekt';
  const timeAgo = latestNotification.timeAgo || 0;

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border max-w-sm w-full animate-slide-down">
        {/* Header */}
        <div className={`px-4 py-3 rounded-t-xl ${
          isAcceptance
            ? 'bg-gradient-to-r from-jobblogg-success to-jobblogg-success-dark'
            : 'bg-gradient-to-r from-jobblogg-error to-jobblogg-error-dark'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isAcceptance ? (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <h3 className="text-sm font-semibold text-white">
                {isAcceptance ? 'Invitasjon godtatt! 🎉' : 'Invitasjon avslått'}
              </h3>
            </div>
            <button
              onClick={handleDismiss}
              className="text-white/80 hover:text-white transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="space-y-3">
            {/* Main message */}
            <div>
              <p className="text-sm font-medium text-jobblogg-text-strong">
                {subcontractorName}
              </p>
              <p className="text-xs text-jobblogg-text-medium">
                {isAcceptance
                  ? `har godtatt invitasjonen til "${projectName}"`
                  : `har avslått invitasjonen til "${projectName}"`
                }
              </p>
            </div>

            {/* Response message if provided */}
            {latestNotification.data?.responseMessage && (
              <div className="bg-jobblogg-neutral/50 p-2 rounded-lg">
                <p className="text-xs text-jobblogg-text-medium">
                  <strong>Melding:</strong> {latestNotification.data.responseMessage}
                </p>
              </div>
            )}

            {/* Timestamp */}
            <div className="flex items-center justify-between text-xs text-jobblogg-text-muted">
              <span>{formatTimeAgo(timeAgo)}</span>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={handleViewProject}
              className={`flex-1 px-3 py-2 text-xs font-medium rounded-lg transition-colors ${
                isAcceptance
                  ? 'bg-jobblogg-success text-white hover:bg-jobblogg-success-dark'
                  : 'bg-jobblogg-neutral text-jobblogg-text-strong hover:bg-jobblogg-neutral-dark'
              }`}
            >
              Se prosjekt
            </button>
            <button
              onClick={handleViewInvitations}
              className="flex-1 px-3 py-2 text-xs font-medium text-jobblogg-text-medium bg-jobblogg-neutral/50 hover:bg-jobblogg-neutral rounded-lg transition-colors"
            >
              Alle svar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Error boundary wrapper
export const ResponseToast: React.FC<ResponseToastProps> = (props) => {
  try {
    return <ResponseToastInner {...props} />;
  } catch (error) {
    console.error('ResponseToast component error:', error);
    return null;
  }
};
