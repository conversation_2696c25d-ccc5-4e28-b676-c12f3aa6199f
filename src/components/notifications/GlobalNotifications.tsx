import React from 'react';
import { InvitationToast } from './InvitationToast';
import { ResponseToast } from './ResponseToast';

interface GlobalNotificationsProps {
  className?: string;
}

/**
 * Global notification container that manages all toast notifications
 * This component should be placed at the app level to ensure notifications
 * are visible across all pages where contractors work
 */
export const GlobalNotifications: React.FC<GlobalNotificationsProps> = ({
  className = ''
}) => {
  return (
    <div className={`${className}`}>
      {/* Invitation notifications for subcontractors (incoming invitations) */}
      <InvitationToast />

      {/* Response notifications for contractors (invitation responses) */}
      <ResponseToast className="top-20" />
    </div>
  );
};
