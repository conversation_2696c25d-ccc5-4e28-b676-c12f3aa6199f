import React, { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  noIndex?: boolean;
}

/**
 * SEO Head component for managing meta tags and structured data
 * 
 * @example
 * ```tsx
 * <SEOHead 
 *   title="JobbLogg - Dokumenter arbeidet ditt enkelt og profesjonelt"
 *   description="Transparent prosjektoppfølging for håndverkere og fagfolk"
 *   keywords="håndverker, prosjektdokumentasjon, byggebransje, chat"
 * />
 * ```
 */
export const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'JobbLogg - Dokumenter arbeidet ditt enkelt og profesjonelt',
  description = 'Transparent prosjektoppfølging for håndverkere og fagfolk. Del framgang med kunder i sanntid og bygg tillit gjennom åpenhet.',
  keywords = 'hånd<PERSON><PERSON>, prosjektdokumentasjon, byggebransje, chat, transparent, dokumentasjon, fagfolk, kunde, kommunikasjon',
  image = '/og-image.jpg',
  url = 'https://jobblogg.no',
  type = 'website',
  noIndex = false,
}) => {
  const fullTitle = title.includes('JobbLogg') ? title : `${title} | JobbLogg`;
  const fullUrl = url.startsWith('http') ? url : `https://jobblogg.no${url}`;
  const fullImage = image.startsWith('http') ? image : `https://jobblogg.no${image}`;

  useEffect(() => {
    // Update document title
    document.title = fullTitle;

    // Update meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Basic Meta Tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);

    // Robots
    if (noIndex) {
      updateMetaTag('robots', 'noindex, nofollow');
    }

    // Open Graph / Facebook
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:title', fullTitle, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', fullImage, true);
    updateMetaTag('og:url', fullUrl, true);
    updateMetaTag('og:site_name', 'JobbLogg', true);
    updateMetaTag('og:locale', 'nb_NO', true);

    // Twitter Card
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', fullTitle);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', fullImage);

    // Additional SEO
    updateMetaTag('author', 'JobbLogg');
    updateMetaTag('language', 'Norwegian');
    updateMetaTag('geo.region', 'NO');
    updateMetaTag('geo.country', 'Norway');

  }, [fullTitle, description, keywords, type, fullImage, fullUrl, noIndex]);

  return null;
};

export default SEOHead;
