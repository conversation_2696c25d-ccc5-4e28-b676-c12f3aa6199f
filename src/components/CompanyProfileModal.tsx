import React, { useState, useEffect } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  TextInput,
  PhoneInput,
  TextArea,
  TextStrong,
  TextMedium,
  LockedInput,
  ToggleSwitch
} from './ui';
import { validateNorwegianPhone } from './ui/Form/PhoneInput';
import { useCompanyLookup } from '../hooks/useCompanyLookup';
import type { CompanyInfo } from '../services/companyLookup';
import { OfflineFeatureGuard } from './OfflineFeatureGuard';

interface CompanyProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface CompanyFormData {
  companyName: string;
  orgNumber: string;
  contactPerson: string;
  phone: string;
  email: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  notes: string;
  useCustomAddress: boolean;
}

/**
 * Company Profile Modal Component
 *
 * Reuses the robust logic from contractor onboarding wizard for:
 * - Form validation and error handling
 * - Field locking mechanisms for Brønnøysundregisteret data
 * - Custom address toggle functionality
 * - Dynamic field labeling and string conversion
 * - Auto-population with proper data handling
 *
 * @example
 * ```tsx
 * <CompanyProfileModal
 *   isOpen={showModal}
 *   onClose={() => setShowModal(false)}
 *   onSuccess={() => console.log('Company updated!')}
 * />
 * ```
 */
export const CompanyProfileModal: React.FC<CompanyProfileModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { user } = useUser();

  // Get current company data
  // TODO: Re-enable when type instantiation issue is resolved
  // const contractorCompany = useQuery(
  //   api.contractorCompany.getContractorCompanyWithDetails,
  //   user?.id ? { clerkUserId: user.id } : "skip"
  // );
  const contractorCompany = undefined; // Temporarily disabled due to type instantiation issues

  // Company lookup hook for Brønnøysundregisteret integration
  const { clearResults } = useCompanyLookup();

  // Form state - reusing onboarding wizard patterns
  const [formData, setFormData] = useState<CompanyFormData>({
    companyName: '',
    orgNumber: '',
    contactPerson: '',
    phone: '',
    email: '',
    streetAddress: '',
    postalCode: '',
    city: '',
    entrance: '',
    notes: '',
    useCustomAddress: false
  });

  // State for managing auto-populated fields (from onboarding wizard Step 3)
  const [autoPopulatedFields, setAutoPopulatedFields] = useState<{
    companyName: boolean;
    contactPerson: boolean;
    phone: boolean;
    email: boolean;
    streetAddress: boolean;
    postalCode: boolean;
    city: boolean;
  }>({
    companyName: false,
    contactPerson: false,
    phone: false,
    email: false,
    streetAddress: false,
    postalCode: false,
    city: false
  });

  // Removed field overrides - registry data should remain locked for data integrity

  // Brønnøysundregisteret integration state (from onboarding wizard)
  const [brregData, setBrregData] = useState<CompanyInfo | null>(null);
  const [brregFetchedAt, setBrregFetchedAt] = useState<number | null>(null);
  // Removed contactPersonType state - not needed since we use brregData.managingDirector.roleDescription directly

  // Form validation and UI state
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [phoneError, setPhoneError] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Mutations
  // TODO: Re-enable when type instantiation issue is resolved
  // const updateCompany = useMutation(api.contractorCompany.updateContractorCompany);
  const updateCompany = async (args: any) => {
    console.log("⚠️ Update company temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Helper function for safe string conversion (from debugging session)
  const safeStringConvert = (value: any): string => {
    if (value === null || value === undefined) return '';
    if (typeof value === 'string') return value;
    if (typeof value === 'number') return String(value);
    if (typeof value === 'object') {
      // Try different possible object structures
      if (value.street) return String(value.street);
      if (value.postalCode) return String(value.postalCode);
      if (value.city) return String(value.city);
      if (value.name) return String(value.name);
      if (value.value) return String(value.value);
      if (value.text) return String(value.text);

      // If it's an array, take the first element
      if (Array.isArray(value) && value.length > 0) {
        return String(value[0]);
      }

      return JSON.stringify(value); // Last resort
    }
    return String(value);
  };

  // Handle phone validation
  const handlePhoneValidation = (_isValid: boolean, error?: string) => {
    setPhoneError(error || '');
  };

  // Dynamic field labeling (from onboarding wizard Step 3)
  const getContactPersonLabel = (): string => {
    // First priority: Use role description from Brønnøysundregisteret data (same as Step3ContactDetails.tsx)
    if (brregData?.managingDirector?.roleDescription) {
      return brregData.managingDirector.roleDescription;
    }
    // Fallback to default (same as Step3ContactDetails.tsx)
    return 'Daglig leder';
  };

  const getContactPersonValidationError = (): string => {
    const label = getContactPersonLabel();
    return `${label} er påkrevd`;
  };

  // Phone field labeling (from onboarding wizard Step 3)
  const getPhoneLabel = (): string => {
    const phoneSource = brregData?.registryContact?.phoneSource;
    if (phoneSource === 'mobil') {
      return 'Mobilnummer';
    } else if (phoneSource === 'telefon') {
      return 'Telefonnummer';
    }
    return 'Mobilnummer'; // Default to mobile
  };

  // Load company data when modal opens (enhanced with string conversion)
  useEffect(() => {
    if (isOpen) {
      // Provide fallback empty object if contractorCompany is undefined
      const company = contractorCompany || {
        name: '',
        orgNumber: '',
        contactPerson: '',
        phone: '',
        streetAddress: '',
        postalCode: '',
        city: '',
        entrance: '',
        notes: '',
        useCustomAddress: false,
        brregData: null as any,
        brregFetchedAt: null as any
      };

      if (company) {
      // Debug: Log the entire contractor company data structure
      console.log('[CompanyProfileModal] Full contractor company data:', {
        contractorCompany,
        brregData: company.brregData,
        brregFetchedAt: company.brregFetchedAt,
        managingDirector: company.brregData?.managingDirector,
        contactPerson: company.contactPerson
      });
      // Set form data with safe string conversion
      // Note: email is now always set to Clerk authentication email, not from database
      setFormData({
        companyName: safeStringConvert(company.name),
        orgNumber: safeStringConvert(company.orgNumber),
        contactPerson: safeStringConvert(company.contactPerson),
        phone: safeStringConvert(company.phone).replace('+47 ', '') || '',
        email: user?.primaryEmailAddress?.emailAddress || '', // Always use Clerk email
        streetAddress: safeStringConvert(company.streetAddress),
        postalCode: safeStringConvert(company.postalCode),
        city: safeStringConvert(company.city),
        entrance: safeStringConvert(company.entrance),
        notes: safeStringConvert(company.notes),
        useCustomAddress: company.useCustomAddress || false
      });

      // Set Brønnøysundregisteret data if available
      if (company.brregData) {
        setBrregData(company.brregData as CompanyInfo);
        // Initialize brregFetchedAt to show timestamps immediately
        const initialTimestamp = company.brregFetchedAt || Date.now();
        setBrregFetchedAt(initialTimestamp);

        // Debug logging for dynamic labeling
        const managingDirector = company.brregData?.managingDirector;
        console.log('[CompanyProfileModal] Brønnøysundregisteret data loaded:', {
          hasManagingDirector: !!managingDirector,
          managingDirector: managingDirector,
          roleDescription: (typeof managingDirector === 'object' && managingDirector && 'roleDescription' in managingDirector)
            ? (managingDirector as any).roleDescription
            : 'N/A',
          initialTimestamp
        });
      }

      // Consolidate field locking logic (same as refresh function)
      const br = company.brregData;
      const hasOrg = Boolean(company.orgNumber);
      const hasBr = Boolean(br);
      const md = br?.managingDirector;
      const isSoleProp = br?.organizationForm === 'ENKELTPERSONFORETAK';

      // Contact person should be locked if we have org number (registered company)
      // This matches the logic where we show "Daglig leder" as default for registered companies
      const shouldLockContactPerson = hasOrg || Boolean(md) || isSoleProp;

      const autoPopulated = {
        companyName: hasOrg,
        contactPerson: shouldLockContactPerson,
        phone: false, // always editable per recent UI changes
        email: true, // always locked to Clerk authentication email
        streetAddress: hasBr && !company.useCustomAddress,
        postalCode: hasBr && !company.useCustomAddress,
        city: hasBr && !company.useCustomAddress,
      };

      // Debug logging for field locking
      console.log('[CompanyProfileModal] Field locking status:', {
        hasBr,
        hasOrg,
        hasManagingDirector: Boolean(md),
        isSoleProp,
        shouldLockContactPerson,
        autoPopulated,
        brregData: br,
        managingDirector: md,
        contractorCompanyData: contractorCompany
      });

      setAutoPopulatedFields(autoPopulated);

      setErrors({});
      setSuccessMessage('');
      }
    }
  }, [isOpen, contractorCompany]);

  // Handle field changes (from onboarding wizard pattern)
  const handleFieldChange = (field: keyof CompanyFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // If toggling custom address, update auto-populated fields for address
    if (field === 'useCustomAddress') {
      const hasAddressData = !!(brregFetchedAt && (brregData?.visitingAddress?.street || brregData?.businessAddress?.street));
      setAutoPopulatedFields(prev => ({
        ...prev,
        streetAddress: hasAddressData && !value,
        postalCode: hasAddressData && !value,
        city: hasAddressData && !value,
      }));
    }
  };

  // Helper function to get managing director name from brregData
  const getManagingDirectorName = (managingDirector: any): string => {
    if (typeof managingDirector === 'string') {
      return managingDirector;
    }
    if (typeof managingDirector === 'object' && managingDirector) {
      return managingDirector.fullName || `${managingDirector.firstName || ''} ${managingDirector.lastName || ''}`.trim();
    }
    return '';
  };

  // Update fields from registry data (only locked fields)
  const updateFieldsFromRegistry = (freshBreregData: CompanyInfo) => {
    const updates: Partial<CompanyFormData> = {};

    // Company name (always locked when org number exists)
    if (autoPopulatedFields.companyName && freshBreregData.name) {
      updates.companyName = freshBreregData.name;
    }

    // Contact person (locked when managing director exists)
    if (autoPopulatedFields.contactPerson && freshBreregData.managingDirector) {
      updates.contactPerson = getManagingDirectorName(freshBreregData.managingDirector);
    }

    // Phone is always editable, email is locked to Clerk auth - DO NOT UPDATE EITHER

    // Address fields (only if custom address toggle is OFF)
    // During refresh, we should always update address fields if custom address is OFF,
    // regardless of current autoPopulatedFields state
    if (!formData.useCustomAddress) {
      const addressSource = freshBreregData.visitingAddress || freshBreregData.businessAddress;

      // Debug logging for address updates
      console.log('[CompanyProfileModal] Updating address from registry:', {
        useCustomAddress: formData.useCustomAddress,
        addressSource,
        currentAddress: {
          street: formData.streetAddress,
          postalCode: formData.postalCode,
          city: formData.city
        },
        willUpdate: !!addressSource
      });

      if (addressSource) {
        if (addressSource.street) {
          updates.streetAddress = addressSource.street;
        }
        if (addressSource.postalCode) {
          updates.postalCode = addressSource.postalCode;
        }
        if (addressSource.city) {
          updates.city = addressSource.city;
        }
      }
    }

    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Handle Brønnøysundregisteret refresh
  const handleRefreshBrreg = async () => {
    if (!formData.orgNumber) {
      setErrors({ orgNumber: 'Organisasjonsnummer er påkrevd for å oppdatere fra Brønnøysundregisteret' });
      return;
    }

    setIsRefreshing(true);
    setErrors({});

    try {
      // Simulate fetching Brønnøysundregisteret data for testing
      // In production, this would use the actual company lookup service
      const mockBrregData: CompanyInfo = {
        name: formData.companyName,
        organizationNumber: formData.orgNumber,
        status: 'active',
        organizationForm: 'Aksjeselskap',
        organizationFormCode: 'AS',
        managingDirector: {
          fullName: 'Robert Hansen',
          firstName: 'Robert',
          lastName: 'Hansen',
          roleType: 'DAGL',
          roleDescription: 'Daglig leder'
        },
        registryContact: {
          phone: '96644444',
          email: '<EMAIL>',
          phoneSource: 'mobil'
        },
        visitingAddress: {
          street: 'Nørvegata 34E',
          postalCode: '6008',
          city: 'ÅLESUND'
        }
      };

      // Update state with fresh data
      setBrregData(mockBrregData);
      setBrregFetchedAt(Date.now());

      // Update form fields from registry data (only locked fields)
      updateFieldsFromRegistry(mockBrregData);

      // Update auto-populated fields based on fresh data (same logic as initial load)
      const hasBreregData = true;
      const hasOrgNumber = !!formData.orgNumber;
      const shouldLockContactPersonRefresh = hasOrgNumber || !!mockBrregData.managingDirector;

      // Use same address source logic as updateFieldsFromRegistry
      const addressSource = mockBrregData.visitingAddress || mockBrregData.businessAddress;

      // Debug logging for auto-populated fields during refresh
      console.log('[CompanyProfileModal] Updating auto-populated fields during refresh:', {
        hasBreregData,
        hasOrgNumber,
        shouldLockContactPersonRefresh,
        useCustomAddress: formData.useCustomAddress,
        addressSource,
        hasAddressData: {
          street: !!addressSource?.street,
          postalCode: !!addressSource?.postalCode,
          city: !!addressSource?.city
        }
      });

      const newAutoPopulated = {
        companyName: hasOrgNumber,
        contactPerson: shouldLockContactPersonRefresh,
        // Phone remains always editable, email is always locked to Clerk auth
        phone: false,
        email: true,
        // Address fields only if custom address toggle is OFF and we have address data
        streetAddress: hasBreregData && !formData.useCustomAddress && !!addressSource?.street,
        postalCode: hasBreregData && !formData.useCustomAddress && !!addressSource?.postalCode,
        city: hasBreregData && !formData.useCustomAddress && !!addressSource?.city
      };

      console.log('[CompanyProfileModal] New auto-populated state:', newAutoPopulated);

      setAutoPopulatedFields(prev => ({
        ...prev,
        ...newAutoPopulated
      }));

      // Optional success message
      setSuccessMessage('Bedriftsinformasjon oppdatert fra Brønnøysundregisteret');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (_error: any) {
      setErrors({ general: 'Kunne ikke hente data fra Brønnøysundregisteret' });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Form validation (reusing onboarding wizard patterns)
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Bedriftsnavn er påkrevd';
    }

    if (!formData.orgNumber.trim()) {
      newErrors.orgNumber = 'Organisasjonsnummer er påkrevd';
    } else if (!/^\d{9}$/.test(formData.orgNumber.replace(/\s/g, ''))) {
      newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
    }

    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = getContactPersonValidationError();
    }

    // Email validation removed - email is now locked to Clerk authentication

    if (formData.phone) {
      const phoneValidation = validateNorwegianPhone(formData.phone, 'mobile');
      if (!phoneValidation.isValid) {
        newErrors.phone = phoneValidation.error || 'Ugyldig telefonnummer';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission (reusing onboarding wizard patterns)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !contractorCompany || !user) {
      return;
    }

    setIsSaving(true);
    setErrors({});

    try {
      // Format phone number for storage
      const formattedPhone = formData.phone ? `+47 ${formData.phone}` : undefined;

      await updateCompany({
        clerkUserId: user.id,
        name: formData.companyName.trim(),
        contactPerson: formData.contactPerson.trim(),
        phone: formattedPhone,
        // email removed - now locked to Clerk authentication, not saved to database
        streetAddress: formData.streetAddress.trim() || undefined,
        postalCode: formData.postalCode.trim() || undefined,
        city: formData.city.trim() || undefined,
        entrance: formData.entrance.trim() || undefined,
        orgNumber: formData.orgNumber.replace(/\s/g, ''),
        notes: formData.notes.trim() || undefined,
        useCustomAddress: formData.useCustomAddress
      });

      setSuccessMessage('Bedriftsprofil oppdatert');

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close modal after short delay
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (error: any) {
      console.error('Error updating company:', error);
      setErrors({
        general: error.message || 'En feil oppstod ved oppdatering av bedriftsprofil'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSaving) {
      clearResults();
      onClose();
    }
  };

  // Don't render if not open or no company data
  if (!isOpen || !contractorCompany) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Bedriftsprofil"
      size="lg"
      className="company-profile-modal"
    >
      <OfflineFeatureGuard
        feature="edit-project"
        fallback={
          <div className="space-y-6">
            <div className="flex items-center text-sm text-jobblogg-text-muted">
              <span>Bedriftsprofil</span>
            </div>
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-jobblogg-warning-soft flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <TextStrong className="text-base mb-2">Bedriftsprofil krever internett</TextStrong>
              <TextMedium className="text-jobblogg-text-muted mb-4">
                Oppdatering av bedriftsprofil krever tilgang til Brønnøysundregisteret og andre eksterne tjenester.
              </TextMedium>
              <TextMedium className="text-sm text-jobblogg-text-muted">
                Koble til internett for å redigere bedriftsinformasjon.
              </TextMedium>
            </div>
          </div>
        }
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Breadcrumb Navigation */}
          <div className="flex items-center text-sm text-jobblogg-text-muted">
            <span>Bedriftsprofil</span>
          </div>

        {/* Success Message */}
        {successMessage && (
          <div className="bg-jobblogg-success-soft border border-jobblogg-success text-jobblogg-success-dark px-4 py-3 rounded-lg">
            <TextMedium>{successMessage}</TextMedium>
          </div>
        )}

        {/* General Error */}
        {errors.general && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error text-jobblogg-error-dark px-4 py-3 rounded-lg">
            <TextMedium>{errors.general}</TextMedium>
          </div>
        )}

        {/* Company Information Section */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
            <TextStrong as="h3" className="text-lg text-jobblogg-text-strong">Bedriftsinformasjon</TextStrong>
            <SecondaryButton
              type="button"
              onClick={handleRefreshBrreg}
              loading={isRefreshing}
              disabled={!formData.orgNumber || isRefreshing}
              size="sm"
              className="w-full sm:w-auto min-h-[44px] sm:min-h-[36px]"
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              }
            >
              {isRefreshing ? 'Oppdaterer...' : 'Oppdater fra Brønnøysundregisteret'}
            </SecondaryButton>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Company Name */}
            {autoPopulatedFields.companyName ? (
              <LockedInput
                label="Bedriftsnavn"
                value={formData.companyName}
                fullWidth
                helperText={brregFetchedAt ? `Hentet fra Brønnøysundregisteret ${new Date(brregFetchedAt).toLocaleDateString('nb-NO')} ${new Date(brregFetchedAt).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}` : 'Registrert bedrift - navn kan ikke endres'}
              />
            ) : (
              <TextInput
                label="Bedriftsnavn"
                value={formData.companyName}
                onChange={(e) => handleFieldChange('companyName', e.target.value)}
                error={errors.companyName}
                required
                placeholder="Skriv inn bedriftsnavn"
              />
            )}

            {/* Organization Number */}
            <LockedInput
              label="Organisasjonsnummer"
              value={formData.orgNumber}
              fullWidth
              helperText="Organisasjonsnummer kan ikke endres"
            />
          </div>
        </div>

        {/* Contact Information Section */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
          <TextStrong as="h3" className="text-lg text-jobblogg-text-strong">Kontaktinformasjon</TextStrong>

          {/* Contact Person (Dynamic: Daglig Leder / Innehaver) */}
          {(() => {
            console.log('[CompanyProfileModal] Contact person field rendering:', {
              autoPopulatedContactPerson: autoPopulatedFields.contactPerson,
              contactPersonValue: formData.contactPerson,
              brregFetchedAt: brregFetchedAt,
              label: getContactPersonLabel()
            });
            return null;
          })()}
          {autoPopulatedFields.contactPerson ? (
            <LockedInput
              label={getContactPersonLabel()}
              value={formData.contactPerson}
              fullWidth
              helperText={brregFetchedAt ? `Hentet fra Brønnøysundregisteret ${new Date(brregFetchedAt).toLocaleDateString('nb-NO')} ${new Date(brregFetchedAt).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}` : 'Hentet fra Brønnøysundregisteret'}
            />
          ) : (
            <TextInput
              label={getContactPersonLabel()}
              value={formData.contactPerson}
              onChange={(e) => handleFieldChange('contactPerson', e.target.value)}
              error={errors.contactPerson}
              required
              placeholder="Skriv inn kontaktperson"
            />
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Phone - Always editable for flexibility */}
            <PhoneInput
              label={getPhoneLabel()}
              value={formData.phone}
              onChange={(value) => handleFieldChange('phone', value)}
              error={phoneError || errors.phone}
              placeholder="12345678"
              phoneType="mobile"
              enableValidation={true}
              onValidation={handlePhoneValidation}
            />

            {/* Email - Always locked to Clerk authentication */}
            <LockedInput
              label="Innloggings-e-post"
              value={formData.email}
              fullWidth
              helperText="Denne e-postadressen er knyttet til din innlogging og kan ikke endres"
            />
          </div>
        </div>

        {/* Address Section */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
          <TextStrong as="h3" className="text-lg text-jobblogg-text-strong">Adresse</TextStrong>

          {/* Address Toggle - Show for registered companies (org number) or when we have Brreg data */}
          {(() => {
            const shouldShowToggle = !!(formData.orgNumber || brregData);
            console.log('[CompanyProfileModal] Address toggle visibility:', {
              hasOrgNumber: !!formData.orgNumber,
              hasBrregData: !!brregData,
              shouldShowToggle,
              useCustomAddress: formData.useCustomAddress
            });
            return shouldShowToggle ? (
              <div className="bg-jobblogg-neutral rounded-lg p-4">
                <ToggleSwitch
                  label="Bruk egendefinert adresse"
                  checked={formData.useCustomAddress}
                  onChange={(checked) => handleFieldChange('useCustomAddress', checked)}
                  helperText="Aktiver for å bruke en annen adresse enn den registrerte adressen fra Brønnøysundregisteret"
                />
              </div>
            ) : null;
          })()}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              {/* Street Address */}
              {autoPopulatedFields.streetAddress && !formData.useCustomAddress ? (
                <LockedInput
                  label="Gateadresse"
                  value={formData.streetAddress}
                  fullWidth
                  helperText={brregFetchedAt ? `Hentet fra Brønnøysundregisteret ${new Date(brregFetchedAt).toLocaleDateString('nb-NO')} ${new Date(brregFetchedAt).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}` : 'Hentet fra Brønnøysundregisteret'}
                />
              ) : (
                <TextInput
                  label="Gateadresse"
                  value={formData.streetAddress}
                  onChange={(e) => handleFieldChange('streetAddress', e.target.value)}
                  error={errors.streetAddress}
                  placeholder="Gateadresse"
                />
              )}
            </div>

            {/* Postal Code */}
            {autoPopulatedFields.postalCode && !formData.useCustomAddress ? (
              <LockedInput
                label="Postnummer"
                value={formData.postalCode}
                fullWidth
                helperText={brregFetchedAt ? `Hentet fra Brønnøysundregisteret ${new Date(brregFetchedAt).toLocaleDateString('nb-NO')} ${new Date(brregFetchedAt).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}` : 'Hentet fra Brønnøysundregisteret'}
              />
            ) : (
              <TextInput
                label="Postnummer"
                value={formData.postalCode}
                onChange={(e) => handleFieldChange('postalCode', e.target.value)}
                error={errors.postalCode}
                placeholder="0000"
                maxLength={4}
              />
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* City */}
            {autoPopulatedFields.city && !formData.useCustomAddress ? (
              <LockedInput
                label="Poststed"
                value={formData.city}
                fullWidth
                helperText={brregFetchedAt ? `Hentet fra Brønnøysundregisteret ${new Date(brregFetchedAt).toLocaleDateString('nb-NO')} ${new Date(brregFetchedAt).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}` : 'Hentet fra Brønnøysundregisteret'}
              />
            ) : (
              <TextInput
                label="Poststed"
                value={formData.city}
                onChange={(e) => handleFieldChange('city', e.target.value)}
                error={errors.city}
                placeholder="Poststed"
              />
            )}

            <TextInput
              label="Inngang/etasje"
              value={formData.entrance}
              onChange={(e) => handleFieldChange('entrance', e.target.value)}
              error={errors.entrance}
              placeholder="Inngang, etasje, etc."
            />
          </div>
        </div>

        {/* Notes Section */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4">
          <TextStrong as="h3" className="text-lg text-jobblogg-text-strong">Notater</TextStrong>
          <TextArea
            label="Notater"
            value={formData.notes}
            onChange={(e) => handleFieldChange('notes', e.target.value)}
            error={errors.notes}
            placeholder="Tilleggsnotater om bedriften..."
            rows={4}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border">
          <PrimaryButton
            type="submit"
            loading={isSaving}
            disabled={isSaving}
            className="w-full sm:w-auto sm:order-2 min-h-[44px]"
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            }
          >
            {isSaving ? 'Lagrer...' : 'Lagre endringer'}
          </PrimaryButton>

          <SecondaryButton
            type="button"
            onClick={handleClose}
            disabled={isSaving}
            className="w-full sm:w-auto sm:order-1 min-h-[44px]"
          >
            Avbryt
          </SecondaryButton>
        </div>
      </form>
      </OfflineFeatureGuard>
    </Modal>
  );
};

export default CompanyProfileModal;
