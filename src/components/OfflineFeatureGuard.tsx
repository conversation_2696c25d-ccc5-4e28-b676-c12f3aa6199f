import React from 'react';
import { usePWA } from '../hooks/usePWA';
import { useFeatureAvailability } from '../hooks/useFeatureAvailability';

interface OfflineFeatureGuardProps {
  children: React.ReactNode;
  feature: 'create-project' | 'upload-image' | 'edit-project' | 'view-logs' | 'chat' | 'view-project';
  fallback?: React.ReactNode;
  showMessage?: boolean;
  className?: string;
}

/**
 * Component that conditionally renders features based on online/offline status
 * and available offline capabilities
 */
export const OfflineFeatureGuard: React.FC<OfflineFeatureGuardProps> = ({
  children,
  feature,
  fallback,
  showMessage = true,
  className = ''
}) => {
  const { isOnline, canAccessOfflineData } = usePWA();

  // Define which features are available offline
  const offlineFeatures = {
    'create-project': false, // Requires Brønnøysundregisteret API and customer database
    'upload-image': true,    // Can cache images locally
    'edit-project': false,   // Requires online sync
    'view-logs': true,       // Can view cached logs
    'view-project': true,    // Can view cached project data
    'chat': false           // Requires real-time connection
  };

  // Check if feature is available in current state
  const isFeatureAvailable = () => {
    if (isOnline) return true; // All features available online
    return canAccessOfflineData && offlineFeatures[feature];
  };

  // Get appropriate message for unavailable feature
  const getUnavailableMessage = () => {
    if (!isOnline && !canAccessOfflineData) {
      return {
        title: 'Krever internettforbindelse',
        message: 'Denne funksjonen krever at du er koblet til internett eller har aktivert offline-modus.',
        action: 'Koble til internett eller aktiver offline-modus i innstillingene'
      };
    }

    if (!isOnline && !offlineFeatures[feature]) {
      return {
        title: 'Ikke tilgjengelig offline',
        message: 'Denne funksjonen krever internettforbindelse for å fungere.',
        action: 'Koble til internett for å bruke denne funksjonen'
      };
    }

    return {
      title: 'Funksjon ikke tilgjengelig',
      message: 'Denne funksjonen er midlertidig ikke tilgjengelig.',
      action: 'Prøv igjen senere'
    };
  };

  if (isFeatureAvailable()) {
    return <>{children}</>;
  }

  // Show fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Show default unavailable message
  if (!showMessage) {
    return null;
  }

  const messageInfo = getUnavailableMessage();

  return (
    <div className={`${className}`}>
      <div className="bg-jobblogg-neutral-soft border border-jobblogg-border rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="w-10 h-10 bg-jobblogg-warning-soft rounded-full flex items-center justify-center flex-shrink-0">
            <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-jobblogg-text-strong mb-1">
              {messageInfo.title}
            </h3>
            <p className="text-sm text-jobblogg-text-medium mb-2">
              {messageInfo.message}
            </p>
            <p className="text-xs text-jobblogg-text-muted">
              {messageInfo.action}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};



/**
 * Component for showing offline mode banner
 */
export const OfflineModeBanner: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { isOnline, canAccessOfflineData } = usePWA();

  if (isOnline || !canAccessOfflineData) {
    return null;
  }

  return (
    <div className={`bg-jobblogg-primary-soft border-b border-jobblogg-primary/20 ${className}`}>
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center justify-center gap-2 text-sm">
          <div className="w-2 h-2 bg-jobblogg-primary rounded-full animate-pulse" />
          <span className="text-jobblogg-primary font-medium">
            📱 Offline-modus aktiv
          </span>
          <span className="text-jobblogg-text-medium">
            - Endringer synkroniseres når du kobler til internett
          </span>
        </div>
      </div>
    </div>
  );
};

/**
 * Button component that adapts to offline state
 */
interface OfflineAwareButtonProps {
  children: React.ReactNode;
  feature: OfflineFeatureGuardProps['feature'];
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  variant?: 'primary' | 'secondary';
}

export const OfflineAwareButton: React.FC<OfflineAwareButtonProps> = ({
  children,
  feature,
  onClick,
  disabled = false,
  className = '',
  variant = 'primary'
}) => {
  const { isFeatureAvailable } = useFeatureAvailability();
  const { isOnline } = usePWA();

  const isAvailable = isFeatureAvailable(feature);
  const isDisabled = disabled || !isAvailable;

  const baseClasses = `
    px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200
    disabled:opacity-50 disabled:cursor-not-allowed
    ${className}
  `;

  const variantClasses = variant === 'primary' 
    ? 'bg-jobblogg-primary text-white hover:bg-jobblogg-primary-hover disabled:hover:bg-jobblogg-primary'
    : 'bg-jobblogg-neutral text-jobblogg-text-strong hover:bg-jobblogg-neutral-hover disabled:hover:bg-jobblogg-neutral';

  const handleClick = () => {
    if (isAvailable && onClick) {
      onClick();
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={isDisabled}
      className={`${baseClasses} ${variantClasses}`}
      title={!isAvailable ? 'Ikke tilgjengelig i nåværende modus' : undefined}
    >
      <div className="flex items-center gap-2">
        {!isOnline && isAvailable && (
          <div className="w-2 h-2 bg-current rounded-full opacity-60" />
        )}
        {children}
      </div>
    </button>
  );
};

export default OfflineFeatureGuard;
