/**
 * Contractor onboarding utility functions for external use
 * 
 * This file provides contractor onboarding functions that can be imported
 * by other components without breaking Fast Refresh in React components.
 */

// Re-export utility functions from the main utils
export {
  markContractorOnboardingCompleted,
  useContractorOnboardingStatusSimple as useContractorCompanySimple
} from '../utils/contractorOnboardingUtils';

// Additional utility functions that may be needed
export const resetContractorOnboardingStatus = () => {
  // Clear localStorage data
  const userId = localStorage.getItem('clerk-user-id');
  if (userId) {
    localStorage.removeItem(`jobblogg-contractor-onboarding-${userId}`);
  }
};

export const clearAllContractorOnboardingData = () => {
  // Clear all contractor onboarding related data
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.startsWith('jobblogg-contractor-onboarding-')) {
      localStorage.removeItem(key);
    }
  });
};
