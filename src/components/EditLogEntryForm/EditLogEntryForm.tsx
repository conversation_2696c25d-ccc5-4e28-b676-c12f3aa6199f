import React, { useState, useRef, useEffect } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { TextStrong, TextMedium, TextMuted, PrimaryButton, TextArea, FormError, SubmitButton } from '../ui';

interface EditLogEntryFormProps {
  entryId: string;
  currentDescription: string;
  currentImageUrl?: string | null;
  onSave: () => void;
  onCancel: () => void;
}

const EditLogEntryForm: React.FC<EditLogEntryFormProps> = ({
  entryId,
  currentDescription,
  currentImageUrl,
  onSave,
  onCancel
}) => {
  const { user } = useUser();
  const [description, setDescription] = useState(currentDescription);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(currentImageUrl || null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [removeCurrentImage, setRemoveCurrentImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Mutations
  // TODO: Re-enable when type instantiation issue is resolved
  // const editLogEntry = useMutation(api.logEntries.editLogEntry);
  const editLogEntry = async (args: any) => {
    console.log("⚠️ Edit log entry temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation
  // TODO: Re-enable when type instantiation issue is resolved
  // const generateUploadUrl = useMutation(api.logEntries.generateUploadUrl);
  const generateUploadUrl = async (args: any) => {
    console.log("⚠️ Generate upload URL temporarily disabled due to type issues", args);
    return { uploadUrl: '', storageId: '' };
  }; // Temporarily mock mutation

  // Clean up preview URL on unmount
  useEffect(() => {
    return () => {
      if (imagePreview && imagePreview !== currentImageUrl) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview, currentImageUrl]);

  const validateAndSetImage = (file: File): boolean => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setErrors({ image: 'Kun JPEG, PNG og WebP bilder er tillatt' });
      return false;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setErrors({ image: 'Bildet kan ikke være større enn 10MB' });
      return false;
    }

    setSelectedImage(file);
    
    // Clean up previous preview if it's not the current image
    if (imagePreview && imagePreview !== currentImageUrl) {
      URL.revokeObjectURL(imagePreview);
    }
    
    setImagePreview(URL.createObjectURL(file));
    setRemoveCurrentImage(false);
    setErrors({});
    return true;
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      validateAndSetImage(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      validateAndSetImage(files[0]);
    }
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    if (imagePreview && imagePreview !== currentImageUrl) {
      URL.revokeObjectURL(imagePreview);
    }
    setImagePreview(null);
    setRemoveCurrentImage(true);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!description || description.trim().length < 5) {
      newErrors.description = 'Beskrivelse må være minst 5 tegn lang';
    }

    if (description && description.length > 1000) {
      newErrors.description = 'Beskrivelse kan ikke være lengre enn 1000 tegn';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const determineChangeType = (): string => {
    const descriptionChanged = description.trim() !== currentDescription.trim();
    const imageChanged = selectedImage !== null || removeCurrentImage || (!currentImageUrl && selectedImage);
    
    if (descriptionChanged && imageChanged) return "both";
    if (descriptionChanged) return "description";
    if (imageChanged) return "image";
    return "description"; // fallback
  };

  const generateChangeSummary = (): string => {
    const descriptionChanged = description.trim() !== currentDescription.trim();
    const imageChanged = selectedImage !== null || removeCurrentImage;
    
    if (descriptionChanged && imageChanged) {
      return "Oppdatert beskrivelse og bilde";
    } else if (descriptionChanged) {
      return "Oppdatert beskrivelse";
    } else if (selectedImage) {
      return currentImageUrl ? "Erstattet bilde" : "Lagt til bilde";
    } else if (removeCurrentImage) {
      return "Fjernet bilde";
    }
    return "Mindre endringer";
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setUploadProgress('');
    setErrors({});

    try {
      // Validate form
      if (!validateForm()) {
        setIsLoading(false);
        return;
      }

      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      let imageId: string | undefined = undefined;

      // Handle image upload if new image is selected
      if (selectedImage) {
        setUploadProgress('Laster opp bilde...');

        const uploadResult = await generateUploadUrl({});
        const result = await fetch(uploadResult.uploadUrl, {
          method: 'POST',
          headers: { 'Content-Type': selectedImage.type },
          body: selectedImage
        });

        if (!result.ok) {
          throw new Error('Failed to upload image');
        }

        const { storageId } = await result.json();
        imageId = storageId;
      } else if (removeCurrentImage) {
        // Remove current image
        imageId = undefined;
      }
      // If neither selectedImage nor removeCurrentImage, keep current image (don't set imageId)

      setUploadProgress('Lagrer endringer...');

      // Update the log entry
      const editArgs: any = {
        entryId: entryId as any,
        userId: user.id,
        description: description.trim(),
        changeType: determineChangeType(),
        changeSummary: generateChangeSummary()
      };

      // Only include imageId if we're changing the image
      if (selectedImage || removeCurrentImage) {
        editArgs.imageId = imageId as any;
      }

      await editLogEntry(editArgs);

      // Clean up and notify parent
      if (imagePreview && imagePreview !== currentImageUrl) {
        URL.revokeObjectURL(imagePreview);
      }
      
      onSave();

    } catch (error) {
      console.error('Error editing log entry:', error);
      setErrors({ general: 'Det oppstod en feil ved lagring av endringene. Prøv igjen.' });
      setUploadProgress('');
    } finally {
      setIsLoading(false);
    }
  };

  const hasChanges = () => {
    const descriptionChanged = description.trim() !== currentDescription.trim();
    const imageChanged = selectedImage !== null || removeCurrentImage;
    return descriptionChanged || imageChanged;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border p-8">
      <div className="mb-8 text-center">
        <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-accent-soft rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </div>
        <TextStrong as="h2" className="text-2xl mb-2">✏️ Rediger loggføring</TextStrong>
        <TextMedium>Oppdater beskrivelse eller bytt ut bilde</TextMedium>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* General Form Error */}
        {errors.general && (
          <FormError error={errors.general} />
        )}

        {/* Image Upload/Edit Section */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <TextMedium className="font-semibold">Bilde</TextMedium>
            <TextMuted className="text-sm">(valgfritt)</TextMuted>
          </div>

          {!imagePreview ? (
            <div
              className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200 ${
                isDragOver
                  ? 'border-jobblogg-primary bg-jobblogg-primary-soft'
                  : 'border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-primary-soft/30'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <TextStrong as="h3" className="text-lg mb-2">
                {isDragOver ? '📁 Slipp bildet her!' : '📸 Last opp nytt bilde'}
              </TextStrong>
              <TextMedium className="mb-4">
                Dra og slipp eller klikk for å velge bilde
              </TextMedium>
              <TextMuted className="text-sm">
                JPEG, PNG eller WebP • Maks 10MB
              </TextMuted>
            </div>
          ) : (
            <div className="relative">
              <img
                src={imagePreview}
                alt="Forhåndsvisning"
                className="w-full max-h-96 object-cover rounded-xl border border-jobblogg-border"
              />
              <button
                type="button"
                onClick={handleRemoveImage}
                className="absolute top-3 right-3 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full transition-colors duration-200"
                title="Fjern bilde"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}

          {errors.image && (
            <FormError error={errors.image} />
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept=".jpg,.jpeg,.png,.webp"
            onChange={handleImageChange}
            className="hidden"
          />
        </div>

        {/* Description Field */}
        <TextArea
          label="Beskrivelse *"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Beskriv hva som ble gjort, utfordringer, fremgang, eller andre viktige detaljer..."
          rows={4}
          maxLength={1000}
          showCharacterCount
          error={errors.description}
          helperText="💡 Tips: Detaljerte beskrivelser hjelper deg å følge fremgangen over tid"
          required
        />

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <SubmitButton
            loading={isLoading}
            loadingText={uploadProgress || 'Lagrer endringer...'}
            disabled={!hasChanges()}
            className="flex-1"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Lagre endringer
          </SubmitButton>
          
          <PrimaryButton
            type="button"
            onClick={onCancel}
            variant="ghost"
            className="flex-1 sm:flex-none"
          >
            Avbryt
          </PrimaryButton>
        </div>
      </form>
    </div>
  );
};

export default EditLogEntryForm;
