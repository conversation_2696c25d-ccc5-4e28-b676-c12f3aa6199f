import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useCookieConsent } from '../../hooks/useCookieConsent';
import { PrimaryButton, SecondaryButton, TextMedium, TextMuted } from '../ui';

export const CookieBanner: React.FC = () => {
  const { 
    showBanner, 
    acceptAll, 
    acceptNecessary, 
    updateConsent, 
    consent,
    hideBanner 
  } = useCookieConsent();
  
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState({
    functional: consent?.functional || false,
    analytics: consent?.analytics || false,
    marketing: consent?.marketing || false,
  });

  if (!showBanner) return null;

  const handleSavePreferences = () => {
    updateConsent(preferences);
  };

  const handleTogglePreference = (type: keyof typeof preferences) => {
    setPreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 pointer-events-none">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/20 pointer-events-auto" onClick={hideBanner} />
      
      {/* Banner */}
      <div className="relative w-full max-w-2xl bg-white rounded-xl shadow-xl border border-jobblogg-border pointer-events-auto animate-slide-up">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-start justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-jobblogg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-strong">
                  Vi bruker informasjonskapsler
                </h3>
                <TextMuted className="text-sm">
                  For å gi deg den beste opplevelsen
                </TextMuted>
              </div>
            </div>
            
            <button
              onClick={hideBanner}
              className="w-8 h-8 rounded-lg hover:bg-jobblogg-neutral transition-colors flex items-center justify-center text-jobblogg-text-muted hover:text-jobblogg-text-strong"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="space-y-4">
            <TextMedium>
              Vi bruker informasjonskapsler og lokal lagring for å levere JobbLogg-tjenesten.
              Nødvendige funksjoner inkluderer autentisering og offline-funksjonalitet for
              bruk på byggeplasser uten internett.
            </TextMedium>

            {!showDetails && (
              <div className="flex items-center gap-2 text-sm">
                <TextMuted>
                  Les mer i vår{' '}
                  <Link 
                    to="/cookie-policy" 
                    className="text-jobblogg-primary hover:underline"
                  >
                    cookie-erklæring
                  </Link>
                </TextMuted>
              </div>
            )}

            {/* Detailed preferences */}
            {showDetails && (
              <div className="space-y-4 border-t border-jobblogg-border pt-4">
                <div className="space-y-3">
                  {/* Necessary cookies - always enabled */}
                  <div className="flex items-center justify-between p-3 bg-jobblogg-success/5 border border-jobblogg-success/10 rounded-lg">
                    <div>
                      <h4 className="font-medium text-jobblogg-text-strong">
                        Nødvendige cookies og lokal lagring
                      </h4>
                      <TextMuted className="text-sm">
                        Kreves for at tjenesten skal fungere. Inkluderer autentisering,
                        sesjonshåndtering og offline-funksjonalitet.
                      </TextMuted>
                      <div className="mt-2 text-xs text-jobblogg-text-muted">
                        <strong>Offline-lagring:</strong> Vi lagrer nødvendige data lokalt slik at JobbLogg
                        fungerer også uten internett. Dette inkluderer prosjektdata, logger og bilder
                        som lagres i nettleserens lokale lagring (IndexedDB/localStorage).
                        Dette er en del av kjernefunksjonaliteten og kan ikke skrus av.
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-jobblogg-success font-medium">Alltid på</span>
                      <div className="w-10 h-6 bg-jobblogg-success rounded-full flex items-center justify-end px-1">
                        <div className="w-4 h-4 bg-white rounded-full shadow-sm" />
                      </div>
                    </div>
                  </div>

                  {/* Functional cookies */}
                  <div className="flex items-center justify-between p-3 bg-jobblogg-neutral/5 border border-jobblogg-border rounded-lg">
                    <div>
                      <h4 className="font-medium text-jobblogg-text-strong">
                        Funksjonelle cookies
                      </h4>
                      <TextMuted className="text-sm">
                        Forbedrer funksjonalitet og brukeropplevelse
                      </TextMuted>
                    </div>
                    <button
                      onClick={() => handleTogglePreference('functional')}
                      className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                        preferences.functional 
                          ? 'bg-jobblogg-primary justify-end' 
                          : 'bg-jobblogg-neutral justify-start'
                      }`}
                    >
                      <div className="w-4 h-4 bg-white rounded-full shadow-sm mx-1" />
                    </button>
                  </div>

                  {/* Analytics cookies */}
                  <div className="flex items-center justify-between p-3 bg-jobblogg-neutral/5 border border-jobblogg-border rounded-lg">
                    <div>
                      <h4 className="font-medium text-jobblogg-text-strong">
                        Analyse-cookies
                      </h4>
                      <TextMuted className="text-sm">
                        Hjelper oss å forstå hvordan tjenesten brukes
                      </TextMuted>
                    </div>
                    <button
                      onClick={() => handleTogglePreference('analytics')}
                      className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                        preferences.analytics 
                          ? 'bg-jobblogg-primary justify-end' 
                          : 'bg-jobblogg-neutral justify-start'
                      }`}
                    >
                      <div className="w-4 h-4 bg-white rounded-full shadow-sm mx-1" />
                    </button>
                  </div>

                  {/* Marketing cookies */}
                  <div className="flex items-center justify-between p-3 bg-jobblogg-neutral/5 border border-jobblogg-border rounded-lg">
                    <div>
                      <h4 className="font-medium text-jobblogg-text-strong">
                        Markedsføring-cookies
                      </h4>
                      <TextMuted className="text-sm">
                        Brukes til å vise relevante annonser
                      </TextMuted>
                    </div>
                    <button
                      onClick={() => handleTogglePreference('marketing')}
                      className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                        preferences.marketing 
                          ? 'bg-jobblogg-primary justify-end' 
                          : 'bg-jobblogg-neutral justify-start'
                      }`}
                    >
                      <div className="w-4 h-4 bg-white rounded-full shadow-sm mx-1" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-sm pt-2">
                  <TextMuted>
                    Les mer i vår{' '}
                    <Link 
                      to="/cookie-policy" 
                      className="text-jobblogg-primary hover:underline"
                    >
                      cookie-erklæring
                    </Link>
                    {' '}og{' '}
                    <Link 
                      to="/privacy-policy" 
                      className="text-jobblogg-primary hover:underline"
                    >
                      personvernerklæring
                    </Link>
                  </TextMuted>
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            {!showDetails ? (
              <>
                <PrimaryButton 
                  onClick={acceptAll}
                  className="flex-1"
                >
                  Aksepter alle
                </PrimaryButton>
                <SecondaryButton 
                  onClick={() => setShowDetails(true)}
                  className="flex-1"
                >
                  Tilpass innstillinger
                </SecondaryButton>
                <SecondaryButton 
                  onClick={acceptNecessary}
                  className="flex-1"
                >
                  Kun nødvendige
                </SecondaryButton>
              </>
            ) : (
              <>
                <PrimaryButton 
                  onClick={handleSavePreferences}
                  className="flex-1"
                >
                  Lagre innstillinger
                </PrimaryButton>
                <SecondaryButton 
                  onClick={() => setShowDetails(false)}
                  className="flex-1"
                >
                  Tilbake
                </SecondaryButton>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieBanner;
