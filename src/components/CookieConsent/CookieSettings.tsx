import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useCookieConsent } from '../../hooks/useCookieConsent';
import { Heading2, TextMedium, TextMuted, PrimaryButton, SecondaryButton } from '../ui';

export const CookieSettings: React.FC = () => {
  const { 
    consent, 
    updateConsent, 
    resetConsent, 
    isAllowed,
    hasConsent 
  } = useCookieConsent();
  
  const [preferences, setPreferences] = useState({
    functional: false,
    analytics: false,
    marketing: false,
  });
  
  const [hasChanges, setHasChanges] = useState(false);

  // Update preferences when consent changes
  useEffect(() => {
    if (consent) {
      const newPreferences = {
        functional: consent.functional,
        analytics: consent.analytics,
        marketing: consent.marketing,
      };
      setPreferences(newPreferences);
      setHasChanges(false);
    }
  }, [consent]);

  const handleTogglePreference = (type: keyof typeof preferences) => {
    setPreferences(prev => {
      const newPrefs = {
        ...prev,
        [type]: !prev[type]
      };
      
      // Check if there are changes compared to current consent
      const hasChanges = consent ? (
        newPrefs.functional !== consent.functional ||
        newPrefs.analytics !== consent.analytics ||
        newPrefs.marketing !== consent.marketing
      ) : true;
      
      setHasChanges(hasChanges);
      return newPrefs;
    });
  };

  const handleSavePreferences = () => {
    updateConsent(preferences);
    setHasChanges(false);
  };

  const handleResetConsent = () => {
    if (window.confirm('Er du sikker på at du vil tilbakestille alle cookie-innstillinger? Du vil bli bedt om å gi samtykke på nytt.')) {
      resetConsent();
    }
  };

  if (!hasConsent) {
    return (
      <div className="space-y-6">
        <Heading2>Cookie-innstillinger</Heading2>
        
        <div className="bg-jobblogg-warning/5 border border-jobblogg-warning/10 rounded-xl p-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-jobblogg-warning rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="font-semibold text-jobblogg-warning mb-2">
                Ingen cookie-samtykke funnet
              </h3>
              <TextMedium>
                Du har ikke gitt samtykke til bruk av cookies ennå. 
                Refresh siden for å se cookie-banneret, eller klikk på knappen nedenfor.
              </TextMedium>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <Heading2>Cookie-innstillinger</Heading2>
        <TextMuted className="mt-2">
          Administrer dine preferanser for informasjonskapsler. 
          Endringer trer i kraft umiddelbart.
        </TextMuted>
      </div>

      {/* Current status */}
      <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-xl p-6">
        <h3 className="font-semibold text-jobblogg-primary mb-3">
          Nåværende innstillinger
        </h3>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${
              isAllowed('necessary') ? 'bg-jobblogg-success' : 'bg-jobblogg-neutral'
            }`} />
            <TextMuted>Nødvendige</TextMuted>
          </div>
          <div className="text-center">
            <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${
              isAllowed('functional') ? 'bg-jobblogg-success' : 'bg-jobblogg-neutral'
            }`} />
            <TextMuted>Funksjonelle</TextMuted>
          </div>
          <div className="text-center">
            <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${
              isAllowed('analytics') ? 'bg-jobblogg-success' : 'bg-jobblogg-neutral'
            }`} />
            <TextMuted>Analyse</TextMuted>
          </div>
          <div className="text-center">
            <div className={`w-3 h-3 rounded-full mx-auto mb-1 ${
              isAllowed('marketing') ? 'bg-jobblogg-success' : 'bg-jobblogg-neutral'
            }`} />
            <TextMuted>Markedsføring</TextMuted>
          </div>
        </div>
        
        {consent && (
          <TextMuted className="text-xs mt-3">
            Sist oppdatert: {new Date(consent.timestamp).toLocaleDateString('nb-NO', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </TextMuted>
        )}
      </div>

      {/* Cookie categories */}
      <div className="space-y-4">
        {/* Necessary cookies - always enabled */}
        <div className="border border-jobblogg-success/20 bg-jobblogg-success/5 rounded-xl p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-jobblogg-success">
              Nødvendige cookies
            </h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-jobblogg-success font-medium">Alltid på</span>
              <div className="w-10 h-6 bg-jobblogg-success rounded-full flex items-center justify-end px-1">
                <div className="w-4 h-4 bg-white rounded-full shadow-sm" />
              </div>
            </div>
          </div>
          <TextMuted className="text-sm">
            Disse cookies er essensielle for at tjenesten skal fungere og kan ikke deaktiveres. 
            De inkluderer autentisering, sikkerhet og grunnleggende funksjonalitet.
          </TextMuted>
        </div>

        {/* Functional cookies */}
        <div className="border border-jobblogg-border rounded-xl p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-jobblogg-text-strong">
              Funksjonelle cookies
            </h3>
            <button
              onClick={() => handleTogglePreference('functional')}
              className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                preferences.functional 
                  ? 'bg-jobblogg-primary justify-end' 
                  : 'bg-jobblogg-neutral justify-start'
              }`}
            >
              <div className="w-4 h-4 bg-white rounded-full shadow-sm mx-1" />
            </button>
          </div>
          <TextMuted className="text-sm">
            Forbedrer funksjonaliteten og personaliseringen av tjenesten. 
            Inkluderer brukerpreferanser, språkinnstillinger og chat-funksjonalitet.
          </TextMuted>
        </div>

        {/* Analytics cookies */}
        <div className="border border-jobblogg-border rounded-xl p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-jobblogg-text-strong">
              Analyse-cookies
            </h3>
            <button
              onClick={() => handleTogglePreference('analytics')}
              className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                preferences.analytics 
                  ? 'bg-jobblogg-primary justify-end' 
                  : 'bg-jobblogg-neutral justify-start'
              }`}
            >
              <div className="w-4 h-4 bg-white rounded-full shadow-sm mx-1" />
            </button>
          </div>
          <TextMuted className="text-sm">
            Hjelper oss å forstå hvordan tjenesten brukes og forbedre den. 
            All data anonymiseres og brukes kun til statistiske formål.
          </TextMuted>
        </div>

        {/* Marketing cookies */}
        <div className="border border-jobblogg-border rounded-xl p-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-jobblogg-text-strong">
              Markedsføring-cookies
            </h3>
            <button
              onClick={() => handleTogglePreference('marketing')}
              className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                preferences.marketing 
                  ? 'bg-jobblogg-primary justify-end' 
                  : 'bg-jobblogg-neutral justify-start'
              }`}
            >
              <div className="w-4 h-4 bg-white rounded-full shadow-sm mx-1" />
            </button>
          </div>
          <TextMuted className="text-sm">
            Brukes til å vise relevante annonser og markedsføringsinnhold. 
            Hjelper oss å måle effektiviteten av markedsføringskampanjer.
          </TextMuted>
        </div>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-jobblogg-border">
        <PrimaryButton 
          onClick={handleSavePreferences}
          disabled={!hasChanges}
          className="flex-1"
        >
          {hasChanges ? 'Lagre endringer' : 'Innstillinger lagret'}
        </PrimaryButton>
        
        <SecondaryButton 
          onClick={handleResetConsent}
          className="flex-1"
        >
          Tilbakestill alle
        </SecondaryButton>
      </div>

      {/* Additional info */}
      <div className="bg-jobblogg-neutral/5 border border-jobblogg-border rounded-xl p-6">
        <h3 className="font-semibold text-jobblogg-text-strong mb-3">
          Mer informasjon
        </h3>
        <div className="space-y-2 text-sm">
          <p>
            <TextMuted>
              Les mer om hvordan vi bruker cookies i vår{' '}
              <Link 
                to="/cookie-policy" 
                className="text-jobblogg-primary hover:underline"
              >
                cookie-erklæring
              </Link>
            </TextMuted>
          </p>
          <p>
            <TextMuted>
              Se vår{' '}
              <Link 
                to="/privacy-policy" 
                className="text-jobblogg-primary hover:underline"
              >
                personvernerklæring
              </Link>
              {' '}for informasjon om databehandling
            </TextMuted>
          </p>
        </div>
      </div>
    </div>
  );
};

export default CookieSettings;
