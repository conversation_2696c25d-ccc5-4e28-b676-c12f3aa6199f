import React from 'react';
import { TextMuted, BodyText } from '../ui';

interface RegistrationInvitationStatusProps {
  /** Company name that was invited */
  companyName: string;
  /** Email address the invitation was sent to */
  email?: string;
  /** Status of the registration invitation */
  status: 'sending' | 'sent' | 'registered' | 'expired' | 'error';
  /** When the invitation was sent */
  sentAt?: Date;
  /** When the invitation expires */
  expiresAt?: Date;
  /** Error message if status is 'error' */
  errorMessage?: string;
  /** Callback when user wants to resend invitation */
  onResend?: () => void;
  /** Callback when user wants to add the now-registered company */
  onAddToProject?: () => void;
}

/**
 * Status indicator for registration invitations sent to potential subcontractors
 * Shows the current state of the invitation and available actions
 */
export const RegistrationInvitationStatus: React.FC<RegistrationInvitationStatusProps> = ({
  companyName,
  email,
  status,
  sentAt,
  expiresAt,
  errorMessage,
  onResend,
  onAddToProject,
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'sending':
        return {
          icon: (
            <div className="animate-spin w-4 h-4 border-2 border-jobblogg-primary border-t-transparent rounded-full"></div>
          ),
          color: 'text-jobblogg-primary',
          bgColor: 'bg-jobblogg-primary/10',
          borderColor: 'border-jobblogg-primary/20',
          title: 'Sender invitasjon...',
          description: `Sender registreringsinvitasjon til ${companyName}`,
        };
      case 'sent':
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          ),
          color: 'text-jobblogg-warning',
          bgColor: 'bg-jobblogg-warning/10',
          borderColor: 'border-jobblogg-warning/20',
          title: 'Invitasjon sendt',
          description: `Venter på at ${companyName} registrerer seg`,
        };
      case 'registered':
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ),
          color: 'text-jobblogg-success',
          bgColor: 'bg-jobblogg-success/10',
          borderColor: 'border-jobblogg-success/20',
          title: 'Registrert!',
          description: `${companyName} er nå registrert og kan legges til prosjektet`,
        };
      case 'expired':
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          color: 'text-jobblogg-text-muted',
          bgColor: 'bg-jobblogg-text-muted/10',
          borderColor: 'border-jobblogg-text-muted/20',
          title: 'Invitasjon utløpt',
          description: `Invitasjonen til ${companyName} har utløpt`,
        };
      case 'error':
        return {
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ),
          color: 'text-jobblogg-error',
          bgColor: 'bg-jobblogg-error/10',
          borderColor: 'border-jobblogg-error/20',
          title: 'Feil ved sending',
          description: errorMessage || 'Kunne ikke sende invitasjon',
        };
      default:
        return {
          icon: null,
          color: 'text-jobblogg-text-muted',
          bgColor: 'bg-jobblogg-text-muted/10',
          borderColor: 'border-jobblogg-text-muted/20',
          title: 'Ukjent status',
          description: '',
        };
    }
  };

  const config = getStatusConfig();

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('nb-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className={`rounded-lg p-4 border ${config.bgColor} ${config.borderColor}`}>
      <div className="flex items-start gap-3">
        <div className={`${config.color} mt-0.5 flex-shrink-0`}>
          {config.icon}
        </div>
        
        <div className="flex-1 space-y-2">
          <div>
            <BodyText className={`font-medium ${config.color}`}>
              {config.title}
            </BodyText>
            <TextMuted className="text-sm">
              {config.description}
            </TextMuted>
          </div>

          {/* Additional details */}
          <div className="space-y-1">
            {email && (
              <TextMuted className="text-xs">
                📧 Sendt til: {email}
              </TextMuted>
            )}
            {sentAt && (
              <TextMuted className="text-xs">
                🕒 Sendt: {formatDate(sentAt)}
              </TextMuted>
            )}
            {expiresAt && status === 'sent' && (
              <TextMuted className="text-xs">
                ⏰ Utløper: {formatDate(expiresAt)}
              </TextMuted>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 pt-2">
            {status === 'registered' && onAddToProject && (
              <button
                onClick={onAddToProject}
                className="px-3 py-1.5 bg-jobblogg-success text-white text-sm font-medium rounded-lg hover:bg-jobblogg-success-dark transition-colors"
              >
                ✅ Legg til i prosjekt
              </button>
            )}
            {(status === 'expired' || status === 'error') && onResend && (
              <button
                onClick={onResend}
                className="px-3 py-1.5 bg-jobblogg-primary text-white text-sm font-medium rounded-lg hover:bg-jobblogg-primary-dark transition-colors"
              >
                🔄 Send på nytt
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
