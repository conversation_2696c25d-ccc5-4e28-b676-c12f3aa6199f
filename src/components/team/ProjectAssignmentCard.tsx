import React, { useState } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { getSpecializationById } from '../../utils/specializations';
import {
  BodyText,
  TextMuted,
  SecondaryButton,
  DangerButton,
  SelectInput
} from '../ui';

interface ProjectAssignment {
  _id: string;
  assignedUserId: string;
  assignedUserRole: string;
  assignedUserDisplayName?: string;
  assignedBy: string;
  assignedByRole: string;
  assignedByDisplayName?: string;
  assignedAt: number;
  accessLevel: string;
  notes?: string;
  // Enhanced subcontractor fields
  isSubcontractor?: boolean;
  subcontractorSpecialization?: string;
  assignedCompany?: {
    _id: string;
    name: string;
    contactPerson?: string;
  } | null;
}

interface ProjectAssignmentCardProps {
  assignment: ProjectAssignment;
  canManage: boolean;
  userAccessLevel?: string; // User's access level for permission checking
  onUpdate?: () => void;
}

export const ProjectAssignmentCard: React.FC<ProjectAssignmentCardProps> = ({
  assignment,
  canManage,
  userAccessLevel,
  onUpdate,
}) => {
  const { user } = useUser();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [newAccessLevel, setNewAccessLevel] = useState(assignment.accessLevel);

  // TODO: Re-enable when type instantiation issue is resolved
  // const updateAssignment = useMutation(api.teamManagement.updateProjectAssignment);
  const updateAssignment = async (args: any) => {
    console.log("⚠️ Update assignment temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation
  // TODO: Re-enable when type instantiation issue is resolved
  // const removeAssignment = useMutation(api.teamManagement.removeProjectAssignment);
  const removeAssignment = async (args: any) => {
    console.log("⚠️ Remove assignment temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Determine if user can remove this assignment
  // Subcontractors can only remove their own team members, not other subcontractors
  const canRemoveAssignment = canManage && !(
    userAccessLevel === 'subcontractor' && assignment.isSubcontractor
  );

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getAccessLevelLabel = (level: string) => {
    switch (level) {
      case 'owner':
        return 'Prosjektleder';
      case 'collaborator':
        return 'Utførende';
      case 'subcontractor':
        return 'Underleverandør';
      case 'viewer':
        return 'Observatør';
      default:
        return level;
    }
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'owner':
        return 'bg-jobblogg-primary/10 text-jobblogg-primary border-jobblogg-primary/20';
      case 'collaborator':
        return 'bg-jobblogg-success/10 text-jobblogg-success border-jobblogg-success/20';
      case 'subcontractor':
        return 'bg-jobblogg-accent/10 text-jobblogg-accent border-jobblogg-accent/20';
      case 'viewer':
        return 'bg-jobblogg-warning/10 text-jobblogg-warning border-jobblogg-warning/20';
      default:
        return 'bg-jobblogg-neutral/10 text-jobblogg-text-medium border-jobblogg-border';
    }
  };

  const handleUpdateAccessLevel = async () => {
    if (!user?.id || newAccessLevel === assignment.accessLevel) return;

    setIsUpdating(true);
    try {
      await updateAssignment({
        assignmentId: assignment._id as any,
        updatedBy: user.id,
        accessLevel: newAccessLevel as any,
      });
      onUpdate?.();
    } catch (error) {
      console.error('Failed to update assignment:', error);
      setNewAccessLevel(assignment.accessLevel); // Reset on error
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveAssignment = async () => {
    if (!user?.id) return;

    setIsRemoving(true);
    try {
      await removeAssignment({
        assignmentId: assignment._id as any,
        revokedBy: user.id,
      });
      onUpdate?.();
    } catch (error) {
      console.error('Failed to remove assignment:', error);
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 hover:shadow-medium transition-all duration-200">
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4 flex-1">
          {/* User avatar placeholder */}
          <div className="w-10 h-10 rounded-full bg-jobblogg-primary/10 flex items-center justify-center flex-shrink-0">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <BodyText className="font-medium">
                {assignment.assignedUserDisplayName ||
                 (assignment.assignedUserRole === 'administrator' ? 'Administrator' : 'Utførende')}
              </BodyText>

              {/* Access level badge */}
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getAccessLevelColor(assignment.accessLevel)}`}>
                {getAccessLevelLabel(assignment.accessLevel)}
              </span>
            </div>

            <div className="space-y-1">
              {/* Subcontractor company information */}
              {assignment.isSubcontractor && assignment.assignedCompany && (
                <TextMuted className="text-sm font-medium">
                  {assignment.assignedCompany.name}
                  {assignment.subcontractorSpecialization && (
                    <span className="text-jobblogg-accent"> • {getSpecializationById(assignment.subcontractorSpecialization)?.name || assignment.subcontractorSpecialization.toUpperCase()}</span>
                  )}
                </TextMuted>
              )}

              <TextMuted className="text-sm">
                Tildelt {formatDate(assignment.assignedAt)}
              </TextMuted>

              <TextMuted className="text-sm">
                Av {assignment.assignedByDisplayName || (assignment.assignedByRole === 'administrator' ? 'administrator' : 'utførende')}
              </TextMuted>

              {assignment.notes && (
                <TextMuted className="text-sm italic">
                  "{assignment.notes}"
                </TextMuted>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        {canManage && (
          <div className="flex items-center gap-2 ml-4">
            {/* Access level selector - dropdown for team members, static badge for subcontractors */}
            {assignment.isSubcontractor ? (
              // Subcontractors: Static badge - access level cannot be changed
              <span className="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                Underleverandør
              </span>
            ) : (
              // Team members: Dropdown with multiple options
              <SelectInput
                value={newAccessLevel}
                onChange={(e) => setNewAccessLevel(e.target.value)}
                disabled={isUpdating || isRemoving}
                className="text-sm"
                options={[
                  { value: 'collaborator', label: 'Utførende' },
                  { value: 'viewer', label: 'Observatør' },
                  { value: 'owner', label: 'Prosjektleder' }
                ]}
              />
            )}

            {/* Update button - only show for team members who can change access level */}
            {!assignment.isSubcontractor && newAccessLevel !== assignment.accessLevel && (
              <SecondaryButton
                size="sm"
                onClick={handleUpdateAccessLevel}
                disabled={isUpdating || isRemoving}
                icon={isUpdating ? (
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              >
                {isUpdating ? 'Oppdaterer...' : 'Oppdater'}
              </SecondaryButton>
            )}

            {/* Remove button - only show if user has permission */}
            {canRemoveAssignment && (
              <DangerButton
                size="sm"
                onClick={handleRemoveAssignment}
                disabled={isUpdating || isRemoving}
                icon={isRemoving ? (
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                )}
              >
                {isRemoving ? 'Fjerner...' : 'Fjern'}
              </DangerButton>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
