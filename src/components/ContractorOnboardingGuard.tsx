import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
// import { useQuery, useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser, useAuth } from '@clerk/clerk-react';
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved


interface ContractorOnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Route protection component that ensures contractors complete onboarding
 * before accessing the main application.
 * 
 * Flow:
 * 1. Check if user is authenticated (Clerk)
 * 2. Check contractor onboarding status from Convex
 * 3. Redirect to onboarding if incomplete
 * 4. Allow access to main app if complete
 */
export const ContractorOnboardingGuard: React.FC<ContractorOnboardingGuardProps> = ({ children }) => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const location = useLocation();
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [hasTriedCreatingUser, setHasTriedCreatingUser] = useState(false);

  // Mutation to create user if it doesn't exist (safe version)
  // TODO: Re-enable when type instantiation issue is resolved
  // const getOrCreateUserSafe = useMutation(api.contractorOnboardingSafe.getOrCreateUserSafe);
  const getOrCreateUserSafe = async (args: any) => {
    console.log("⚠️ Get or create user safe temporarily disabled due to type issues", args);
    return {
      onboardingStatus: 'not_started',
      authError: false,
      error: null
    };
  }; // Temporarily mock mutation

  // Only query contractor onboarding status when both Clerk and Convex auth are ready
  const shouldQueryOnboarding = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  // Use safe version that doesn't throw auth errors
  // TODO: Re-enable when type instantiation issue is resolved
  // const onboardingStatusResult = useQuery(
  //   api.contractorOnboardingSafe.getContractorOnboardingStatusSafe,
  //   shouldQueryOnboarding ? { clerkUserId: user.id } : "skip"
  // );
  const onboardingStatusResult = {
    status: 'not_started',
    authError: false,
    exists: false,
    contractorCompleted: false,
    contractorCompanyId: null
  }; // Temporarily provide fallback object due to type instantiation issues

  // Extract the actual status from the safe result
  const onboardingStatus = onboardingStatusResult && !onboardingStatusResult.authError ? {
    exists: onboardingStatusResult.exists,
    contractorCompleted: onboardingStatusResult.contractorCompleted,
    contractorCompanyId: onboardingStatusResult.contractorCompanyId
  } : undefined;

  // Handle loading states and onboarding check with retry mechanism and user creation
  useEffect(() => {
    if (isClerkLoaded && isAuthLoaded) {
      if (!isSignedIn || !user) {
        setIsCheckingOnboarding(false);
      } else if (onboardingStatusResult !== undefined) {
        // We got a result (either success or auth error)
        if (onboardingStatusResult.authError && retryCount < 2) {
          // Auth error, retry after delay
          const timer = setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, 1000 * (retryCount + 1)); // Exponential backoff: 1s, 2s

          return () => clearTimeout(timer);
        } else if (onboardingStatusResult.authError && retryCount >= 2 && !hasTriedCreatingUser && user?.id) {
          // After retries, try to create user record
          console.log("Attempting to create user record for", user.id);
          setHasTriedCreatingUser(true);
          getOrCreateUserSafe({ clerkUserId: user.id })
            .then((result) => {
              if (result.authError) {
                console.error("Failed to create user record:", result.error);
                setIsCheckingOnboarding(false);
              } else {
                console.log("User record created/retrieved successfully");
                setRetryCount(0);
              }
            })
            .catch((error) => {
              console.error("Failed to create user record:", error);
              setIsCheckingOnboarding(false);
            });
        } else {
          // Either success or exhausted all retry attempts
          setIsCheckingOnboarding(false);
          setRetryCount(0);
        }
      }
    }
  }, [isClerkLoaded, isAuthLoaded, isSignedIn, user, onboardingStatusResult, shouldQueryOnboarding, retryCount, hasTriedCreatingUser, getOrCreateUserSafe]);

  // Show loading state while checking authentication and onboarding
  if (!isClerkLoaded || !isAuthLoaded || isCheckingOnboarding) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Laster JobbLogg...
            </h2>
            <p className="text-jobblogg-text-muted">
              {!isClerkLoaded ? 'Starter autentisering...' :
               !isAuthLoaded ? 'Synkroniserer med server...' :
               hasTriedCreatingUser ? 'Oppretter brukeroppføring...' :
               retryCount > 0 ? `Prøver igjen (${retryCount}/2)...` :
               'Sjekker brukerinformasjon...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If user is not authenticated, let Clerk handle the redirect
  if (!isSignedIn || !user) {
    return <Navigate to="/sign-in" replace />;
  }

  // If onboarding status is not loaded yet and we haven't exhausted retries, show loading
  if (onboardingStatusResult === undefined || (onboardingStatusResult.authError && (retryCount < 2 || hasTriedCreatingUser))) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Sjekker bedriftsinformasjon...
            </h2>
            <p className="text-jobblogg-text-muted">
              {hasTriedCreatingUser ? 'Oppretter brukeroppføring...' :
               retryCount > 0 ? `Prøver igjen (${retryCount}/2)...` :
               'Vent litt mens vi henter dine data'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If contractor onboarding is not completed (or we couldn't determine status), redirect to onboarding
  if (!onboardingStatus?.contractorCompleted) {
    // Preserve the intended destination for redirect after onboarding
    const redirectTo = location.pathname !== '/contractor-onboarding' ? location.pathname : '/';
    const onboardingUrl = `/contractor-onboarding${redirectTo !== '/' ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`;

    return <Navigate to={onboardingUrl} replace />;
  }

  // Contractor onboarding is complete, render the protected content
  return <>{children}</>;
};


