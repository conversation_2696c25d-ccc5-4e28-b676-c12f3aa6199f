import React, { useState, useEffect, useRef } from 'react';
import { generateStaticMapUrl, isGoogleMapsConfigured, isAddressComplete, getFallbackMapImage } from '../../utils/googleMaps';
import { TextMuted } from '../ui';
import { DirectionsButton } from './DirectionsButton';

interface AddressMapPreviewProps {
  streetAddress: string;
  postalCode: string;
  city: string;
  className?: string;
  width?: number;
  height?: number;
  zoom?: number;
}

/**
 * Address Map Preview Component
 * Shows a Google Maps static image preview of the entered address
 * Falls back to placeholder when Maps is not configured or address is incomplete
 */
export const AddressMapPreview: React.FC<AddressMapPreviewProps> = ({
  streetAddress,
  postalCode,
  city,
  className = '',
  width = 400,
  height = 200,
  zoom = 15
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxRetries = 2;
  const loadingTimeout = 10000; // 10 seconds

  // Reset error state when address changes
  useEffect(() => {
    setImageError(false);
    setIsLoading(true);
    setRetryCount(0);

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set timeout for loading
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false);
      setImageError(true);
    }, loadingTimeout);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [streetAddress, postalCode, city]);

  const isConfigured = isGoogleMapsConfigured();
  const isComplete = isAddressComplete(streetAddress, postalCode, city);
  const shouldShowMap = isConfigured && isComplete && !imageError;

  const mapUrl = shouldShowMap
    ? generateStaticMapUrl(streetAddress, postalCode, city, {
        width,
        height,
        zoom
      })
    : getFallbackMapImage();

  const handleImageLoad = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsLoading(false);
    setImageError(false);
    setRetryCount(0);
  };

  const handleImageError = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (retryCount < maxRetries) {
      // Retry loading after a short delay
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setIsLoading(true);

        // Set new timeout for retry
        timeoutRef.current = setTimeout(() => {
          setIsLoading(false);
          setImageError(true);
        }, loadingTimeout);
      }, 1000 * (retryCount + 1)); // Exponential backoff: 1s, 2s, 3s
    } else {
      setImageError(true);
      setIsLoading(false);
    }
  };

  if (!isComplete) {
    return (
      <div className={`bg-jobblogg-neutral rounded-xl p-6 text-center ${className}`}>
        <div className="w-16 h-16 mx-auto mb-3 bg-jobblogg-text-muted/20 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <TextMuted className="text-sm">
          Fyll ut adressefelter for å se kartforhåndsvisning
        </TextMuted>
      </div>
    );
  }

  return (
    <div className={`bg-jobblogg-neutral rounded-xl overflow-hidden ${className}`}>
      {/* Map Image */}
      <div className="relative" style={{ height: `${height}px` }}>
        {isLoading && (
          <div className="absolute inset-0 bg-jobblogg-neutral animate-pulse flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        
        <img
          src={mapUrl}
          alt={`Kart over ${streetAddress}, ${postalCode} ${city}`}
          className="w-full h-full object-cover"
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ display: isLoading ? 'none' : 'block' }}
        />

        {/* Error State */}
        {imageError && (
          <div className="absolute inset-0 bg-jobblogg-neutral flex items-center justify-center">
            <div className="text-center p-4">
              <div className="w-12 h-12 mx-auto mb-2 bg-jobblogg-text-muted/20 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <TextMuted className="text-xs mb-2">
                Kunne ikke laste kart
              </TextMuted>
              <TextMuted className="text-xs text-jobblogg-text-muted/80">
                {retryCount > 0 ? `Forsøkte ${retryCount + 1} ganger` : 'Sjekk internettforbindelsen'}
              </TextMuted>
            </div>
          </div>
        )}
      </div>

      {/* Address Info */}
      <div className="p-3 border-t border-jobblogg-border">
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <svg className="w-4 h-4 text-jobblogg-text-medium flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <TextMuted className="text-sm truncate">
              {streetAddress}, {postalCode} {city}
            </TextMuted>
          </div>

          {/* Directions Button */}
          <DirectionsButton
            streetAddress={streetAddress}
            postalCode={postalCode}
            city={city}
            size="sm"
            className="flex-shrink-0"
          />
        </div>

        {!isConfigured && (
          <div className="mt-2 text-xs text-jobblogg-warning">
            Google Maps API ikke konfigurert
          </div>
        )}
      </div>
    </div>
  );
};

export default AddressMapPreview;
