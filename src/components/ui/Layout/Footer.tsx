import React from 'react';
import { Link } from 'react-router-dom';
import { useCookieConsent } from '../../../hooks/useCookieConsent';
import { TextMuted } from '../Typography';

interface FooterProps {
  /** Show minimal footer (fewer links) */
  minimal?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Footer component with legal links and cookie management
 * 
 * @example
 * ```tsx
 * <Footer />
 * <Footer minimal />
 * ```
 */
export const Footer: React.FC<FooterProps> = ({ 
  minimal = false, 
  className = '' 
}) => {
  const { showConsentModal } = useCookieConsent();

  const currentYear = new Date().getFullYear();

  return (
    <footer className={`bg-jobblogg-neutral-secondary border-t border-jobblogg-border mt-auto ${className}`.trim()}>
      <div className="container-wide py-8">
        <div className="space-y-6">
          {/* Main footer content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Company info */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span className="font-semibold text-jobblogg-text-strong">JobbLogg</span>
              </div>
              <TextMuted className="text-sm">
                Dokumenter arbeidet ditt enkelt og profesjonelt. 
                Transparent prosjektoppfølging for håndverkere og kunder.
              </TextMuted>
            </div>

            {/* Legal links */}
            <div className="space-y-3">
              <h3 className="font-semibold text-jobblogg-text-strong">Juridisk</h3>
              <div className="space-y-2">
                <Link 
                  to="/privacy-policy" 
                  className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                >
                  Personvernerklæring
                </Link>
                <Link 
                  to="/cookie-policy" 
                  className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                >
                  Cookie-erklæring
                </Link>
                <Link
                  to="/terms-of-service"
                  className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                >
                  Brukervilkår
                </Link>
                <Link
                  to="/privacy-settings"
                  className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                >
                  Personverninnstillinger
                </Link>
                <button
                  onClick={showConsentModal}
                  className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors text-left"
                >
                  Administrer cookies
                </button>
              </div>
            </div>

            {/* Support links */}
            {!minimal && (
              <div className="space-y-3">
                <h3 className="font-semibold text-jobblogg-text-strong">Support</h3>
                <div className="space-y-2">
                  <a 
                    href="mailto:<EMAIL>" 
                    className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                  >
                    Kundeservice
                  </a>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                  >
                    Personvern
                  </a>
                  <Link 
                    to="/help" 
                    className="block text-sm text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                  >
                    Hjelp og FAQ
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Bottom bar */}
          <div className="pt-6 border-t border-jobblogg-border">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <TextMuted className="text-sm">
                © {currentYear} JobbLogg. Alle rettigheter forbeholdt.
              </TextMuted>
              
              <div className="flex items-center gap-4">
                <TextMuted className="text-xs">
                  Versjon 1.0.0
                </TextMuted>
                
                {/* Status indicator */}
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-jobblogg-success rounded-full animate-pulse" />
                  <TextMuted className="text-xs">
                    Alle systemer operative
                  </TextMuted>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
