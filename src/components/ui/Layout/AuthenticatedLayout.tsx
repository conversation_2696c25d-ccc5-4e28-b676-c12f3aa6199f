import React, { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { UserButton } from '@clerk/clerk-react';
import { JobbLoggLogo } from '../Logo';
import { NotificationBell } from '../../notifications/NotificationBell';
import { CompanyProfileModal } from '../../CompanyProfileModal';
import { Footer } from './Footer';
import { NetworkStatusIndicator } from '../../NetworkStatusIndicator';
import { useUserRole } from '../../../hooks/useUserRole';
import { useRoleChangeHandler, useRoleChangeListener } from '../../../hooks/useRoleChangeHandler';

interface AuthenticatedLayoutProps {
  /** Page content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Hide the navigation header (for special pages) */
  hideNavigation?: boolean;
}

/**
 * Authenticated layout component that provides consistent navigation header
 * with JobbLogg logo across all authenticated pages
 * 
 * @example
 * ```tsx
 * <AuthenticatedLayout>
 *   <DashboardLayout title="Dashboard">
 *     <ProjectGrid />
 *   </DashboardLayout>
 * </AuthenticatedLayout>
 * ```
 */
export const AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({
  children,
  className = '',
  hideNavigation = false,
}) => {
  const navigate = useNavigate();
  const [showCompanyModal, setShowCompanyModal] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isAdministrator } = useUserRole();
  const [forceUpdate, setForceUpdate] = useState(0);

  // Enable role change detection and handling
  useRoleChangeHandler();

  // Listen for role changes and force re-render of navigation
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin || event.detail.lostAdmin) {
      console.log('AuthenticatedLayout: Role change detected, updating navigation');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  return (
    <div className={`min-h-screen bg-white flex flex-col ${className}`.trim()}>
      {/* Navigation Header */}
      {!hideNavigation && (
        <header className="sticky top-0 z-40 bg-white border-b border-jobblogg-border shadow-sm">
          <div className="container-wide">
            <div className="flex items-center justify-between h-16 px-4 sm:px-6">
              {/* Logo */}
              <Link
                to="/"
                className="hover:opacity-80 transition-opacity focus-ring rounded-lg"
                aria-label="JobbLogg hjem"
              >
                <JobbLoggLogo
                  variant="horizontal"
                  size="md"
                  className="hover:scale-105 transition-transform duration-200"
                />
              </Link>

              {/* Desktop Navigation */}
              <nav className="hidden md:flex items-center space-x-6" role="navigation" aria-label="Hovednavigasjon">
                <Link
                  to="/"
                  className="text-jobblogg-text-medium hover:text-jobblogg-primary font-medium transition-all duration-300 focus-ring rounded-lg px-3 py-2 relative group"
                  aria-label="Gå til Dashboard"
                >
                  Dashboard
                  <span className="absolute bottom-0 left-3 right-3 h-0.5 bg-jobblogg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full" />
                </Link>
                <Link
                  to="/conversations"
                  className="text-jobblogg-text-medium hover:text-jobblogg-primary font-medium transition-all duration-300 focus-ring rounded-lg px-3 py-2 relative group"
                  aria-label="Gå til prosjektsamtaler"
                >
                  Prosjektsamtaler
                  <span className="absolute bottom-0 left-3 right-3 h-0.5 bg-jobblogg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full" />
                </Link>
                <Link
                  to="/invitations"
                  className="text-jobblogg-text-medium hover:text-jobblogg-primary font-medium transition-all duration-300 focus-ring rounded-lg px-3 py-2 relative group"
                  aria-label="Gå til prosjektinvitasjoner"
                >
                  Prosjektinvitasjoner
                  <span className="absolute bottom-0 left-3 right-3 h-0.5 bg-jobblogg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full" />
                </Link>
              </nav>

              {/* Mobile Navigation Menu Button */}
              <div className="flex md:hidden items-center gap-3">
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="w-10 h-10 rounded-lg bg-jobblogg-neutral hover:bg-jobblogg-primary-soft text-jobblogg-text-medium hover:text-jobblogg-primary transition-all duration-200 flex items-center justify-center focus-ring"
                  aria-label={isMobileMenuOpen ? 'Lukk meny' : 'Åpne navigasjonsmeny'}
                  aria-expanded={isMobileMenuOpen}
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    {isMobileMenuOpen ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    )}
                  </svg>
                </button>
              </div>

              {/* User Actions */}
              <div className="flex items-center gap-2">
                <NotificationBell />
                <UserButton
                  key={`userbutton-${forceUpdate}-${isAdministrator}`}
                  afterSignOutUrl="/"
                  appearance={{
                    elements: {
                      avatarBox: "w-10 h-10 rounded-full ring-2 ring-jobblogg-primary/20 hover:ring-jobblogg-primary/40 transition-all duration-200"
                    }
                  }}
                >
                  <UserButton.MenuItems>
                    {/* Subscription menu item - Administrator only */}
                    {isAdministrator && (
                      <UserButton.Action
                        label="Abonnement"
                        labelIcon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        }
                        onClick={() => navigate('/subscription')}
                      />
                    )}
                    {/* Company Profile menu item - Administrator only */}
                    {isAdministrator && (
                      <UserButton.Action
                        label="Bedriftsprofil"
                        labelIcon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                        }
                        onClick={() => setShowCompanyModal(true)}
                      />
                    )}
                  </UserButton.MenuItems>
                </UserButton>

                {/* Network Status Indicator */}
                <NetworkStatusIndicator />
              </div>
            </div>
          </div>

          {/* Mobile Navigation Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden bg-white border-t border-jobblogg-border shadow-lg">
              <nav className="px-4 py-3 space-y-1" role="navigation" aria-label="Mobil navigasjon">
                <Link
                  to="/"
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="flex items-center px-3 py-3 rounded-lg text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10 transition-all duration-200 min-h-[44px]"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5v4m8-4v4" />
                  </svg>
                  Dashboard
                </Link>
                <Link
                  to="/conversations"
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="flex items-center px-3 py-3 rounded-lg text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10 transition-all duration-200 min-h-[44px]"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Prosjektsamtaler
                </Link>
                <Link
                  to="/invitations"
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="flex items-center px-3 py-3 rounded-lg text-jobblogg-text-medium hover:text-jobblogg-primary hover:bg-jobblogg-primary/10 transition-all duration-200 min-h-[44px]"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Prosjektinvitasjoner
                </Link>
              </nav>
            </div>
          )}
        </header>
      )}

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <Footer />

      {/* Company Profile Modal */}
      {showCompanyModal && (
        <CompanyProfileModal
          isOpen={showCompanyModal}
          onClose={() => setShowCompanyModal(false)}
        />
      )}
    </div>
  );
};

export default AuthenticatedLayout;
