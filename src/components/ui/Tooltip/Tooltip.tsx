import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

export interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  trigger?: 'hover' | 'click' | 'both';
  disabled?: boolean;
  className?: string;
  maxWidth?: string;
  showOnMobile?: boolean;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  placement = 'top',
  trigger = 'hover',
  disabled = false,
  className = '',
  maxWidth = '280px',
  showOnMobile: _showOnMobile = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle click outside to close on mobile
  useEffect(() => {
    if (!isVisible || !isMobile) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        tooltipRef.current &&
        triggerRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isVisible, isMobile]);

  const handleMouseEnter = () => {
    if (disabled) return;
    if (trigger === 'hover' || trigger === 'both') {
      if (!isMobile) setIsVisible(true);
    }
  };

  const handleMouseLeave = () => {
    if (disabled) return;
    if (trigger === 'hover' || trigger === 'both') {
      if (!isMobile) setIsVisible(false);
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (disabled) return;
    if (trigger === 'click' || trigger === 'both') {
      setIsVisible(!isVisible);
    }
  };

  const handleFocus = () => {
    if (disabled) return;
    if (trigger === 'hover' || trigger === 'both') {
      setIsVisible(true);
    }
  };

  const handleBlur = () => {
    if (disabled) return;
    if (trigger === 'hover' || trigger === 'both') {
      setIsVisible(false);
    }
  };

  const getTooltipPosition = () => {
    if (!triggerRef.current) return { top: 0, left: 0 };

    const rect = triggerRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    // Add some padding to prevent tooltip from going off-screen
    const padding = 8;

    const positions = {
      top: {
        top: rect.top + scrollTop - padding,
        left: rect.left + scrollLeft + rect.width / 2,
        transform: 'translate(-50%, -100%)'
      },
      bottom: {
        top: rect.bottom + scrollTop + padding,
        left: rect.left + scrollLeft + rect.width / 2,
        transform: 'translate(-50%, 0)'
      },
      left: {
        top: rect.top + scrollTop + rect.height / 2,
        left: rect.left + scrollLeft - padding,
        transform: 'translate(-100%, -50%)'
      },
      right: {
        top: rect.top + scrollTop + rect.height / 2,
        left: rect.right + scrollLeft + padding,
        transform: 'translate(0, -50%)'
      }
    };

    return positions[placement] || positions.top;
  };

  const tooltipContent = (
    <div
      ref={tooltipRef}
      className={`
        fixed px-3 py-2 text-xs sm:text-sm text-white bg-gray-900 rounded-lg shadow-lg border border-gray-700
        pointer-events-auto transition-all duration-200
        ${isVisible ? 'opacity-100 visible' : 'opacity-0 invisible pointer-events-none'}
        ${isMobile ? 'max-w-[280px]' : 'max-w-[240px]'}
        ${className}
      `}
      style={{
        ...getTooltipPosition(),
        maxWidth: isMobile ? '280px' : maxWidth,
        zIndex: 999999
      }}
      role="tooltip"
      aria-live="polite"
    >
      {content}
      
      {/* Close button for mobile */}
      {isMobile && (
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsVisible(false);
          }}
          className="absolute top-1 right-1 w-5 h-5 flex items-center justify-center text-white/70 hover:text-white min-h-[44px] min-w-[44px] -m-2"
          aria-label="Lukk hjelpetekst"
          type="button"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}

      {/* Arrow */}
      <div
        className={`
          absolute w-2 h-2 bg-gray-900 transform rotate-45
          ${placement === 'top' ? 'bottom-[-4px] left-1/2 -translate-x-1/2' : ''}
          ${placement === 'bottom' ? 'top-[-4px] left-1/2 -translate-x-1/2' : ''}
          ${placement === 'left' ? 'right-[-4px] top-1/2 -translate-y-1/2' : ''}
          ${placement === 'right' ? 'left-[-4px] top-1/2 -translate-y-1/2' : ''}
        `}
      />
    </div>
  );

  const tooltipId = `tooltip-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className="inline-block"
        aria-describedby={isVisible ? tooltipId : undefined}
      >
        {children}
      </div>

      {typeof document !== 'undefined' && createPortal(
        <div id={tooltipId}>
          {tooltipContent}
        </div>,
        document.body
      )}
    </>
  );
};
