import React, { useState } from 'react';
import { TextStrong, TextMuted } from '../Typography';
import { SecondaryButton } from '../Button';

export interface FilterOptions {
  projectStatus: 'all' | 'active' | 'archived';
  customerType: 'all' | 'privat' | 'bedrift';
  accessLevel: 'all' | 'owner' | 'collaborator' | 'subcontractor' | 'viewer';
  invitationStatus: 'all' | 'pending' | 'accepted' | 'declined' | 'expired';
  dateRange: {
    type: 'all' | 'created' | 'updated';
    period: 'all' | 'today' | 'week' | 'month' | 'quarter' | 'year';
    custom?: {
      from?: string;
      to?: string;
    };
  };
  location: {
    city: string;
  };
}

interface FilterPanelProps {
  /** Current filter values */
  filters: FilterOptions;
  /** Filter change handler */
  onChange: (filters: FilterOptions) => void;
  /** Whether panel is expanded on mobile */
  isExpanded: boolean;
  /** Toggle expansion handler */
  onToggleExpanded: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Available cities for location filter */
  availableCities?: string[];
  /** Active filter count for badge */
  activeFilterCount?: number;
}

/**
 * Mobile-first filter panel with collapsible sections
 * Follows JobbLogg design system with touch-friendly controls
 * 
 * @example
 * ```tsx
 * <FilterPanel
 *   filters={currentFilters}
 *   onChange={setFilters}
 *   isExpanded={showFilters}
 *   onToggleExpanded={() => setShowFilters(!showFilters)}
 *   availableCities={['Oslo', 'Bergen', 'Trondheim']}
 * />
 * ```
 */
export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onChange,
  isExpanded,
  onToggleExpanded,
  className = '',
  availableCities: _availableCities = [],
  activeFilterCount = 0,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const updateFilter = <K extends keyof FilterOptions>(
    key: K,
    value: FilterOptions[K]
  ) => {
    onChange({
      ...filters,
      [key]: value,
    });
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const clearAllFilters = () => {
    onChange({
      projectStatus: 'all',
      customerType: 'all',
      accessLevel: 'all',
      invitationStatus: 'all',
      dateRange: {
        type: 'all',
        period: 'all',
      },
      location: {
        city: '',
      },
    });
  };

  const hasActiveFilters = activeFilterCount > 0;

  return (
    <div className={`bg-white border border-jobblogg-border rounded-xl overflow-hidden ${className}`}>
      {/* Filter Toggle Header */}
      <button
        onClick={onToggleExpanded}
        className="w-full px-4 py-3 flex items-center justify-between bg-jobblogg-neutral-light hover:bg-jobblogg-neutral transition-colors duration-200 touch-target mobile-focus"
        aria-expanded={isExpanded}
        aria-controls="filter-panel-content"
      >
        <div className="flex items-center gap-3">
          <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
          </svg>
          <TextStrong className="text-sm">Filtrer</TextStrong>
          {hasActiveFilters && (
            <span className="inline-flex items-center justify-center w-5 h-5 bg-jobblogg-primary text-white text-xs font-bold rounded-full">
              {activeFilterCount}
            </span>
          )}
        </div>
        <svg 
          className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
            isExpanded ? 'rotate-180' : ''
          }`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Filter Content */}
      {isExpanded && (
        <div id="filter-panel-content" className="p-4 space-y-4 border-t border-jobblogg-border">
          {/* Clear All Filters */}
          {hasActiveFilters && (
            <div className="flex justify-end">
              <SecondaryButton
                onClick={clearAllFilters}
                size="sm"
                variant="outline"
                className="text-xs"
              >
                Nullstill alle
              </SecondaryButton>
            </div>
          )}

          {/* Project Status Filter */}
          <FilterSection
            title="Prosjektstatus"
            isExpanded={expandedSections.has('status')}
            onToggle={() => toggleSection('status')}
          >
            <RadioGroup
              name="projectStatus"
              value={filters.projectStatus}
              onChange={(value) => updateFilter('projectStatus', value as FilterOptions['projectStatus'])}
              options={[
                { value: 'all', label: 'Alle prosjekter' },
                { value: 'active', label: 'Aktive prosjekter' },
                { value: 'archived', label: 'Arkiverte prosjekter' },
              ]}
            />
          </FilterSection>

          {/* Customer Type Filter */}
          <FilterSection
            title="Kundetype"
            isExpanded={expandedSections.has('customer')}
            onToggle={() => toggleSection('customer')}
          >
            <RadioGroup
              name="customerType"
              value={filters.customerType}
              onChange={(value) => updateFilter('customerType', value as FilterOptions['customerType'])}
              options={[
                { value: 'all', label: 'Alle kunder' },
                { value: 'privat', label: 'Privatkunder' },
                { value: 'bedrift', label: 'Bedriftskunder' },
              ]}
            />
          </FilterSection>

          {/* Access Level Filter */}
          <FilterSection
            title="Tilgangsnivå"
            isExpanded={expandedSections.has('access')}
            onToggle={() => toggleSection('access')}
          >
            <RadioGroup
              name="accessLevel"
              value={filters.accessLevel}
              onChange={(value) => updateFilter('accessLevel', value as FilterOptions['accessLevel'])}
              options={[
                { value: 'all', label: 'Alle roller' },
                { value: 'owner', label: 'Prosjektleder' },
                { value: 'collaborator', label: 'Utførende' },
                { value: 'subcontractor', label: 'Underleverandør' },
                { value: 'viewer', label: 'Leser' },
              ]}
            />
          </FilterSection>

          {/* Date Range Filter */}
          <FilterSection
            title="Tidsperiode"
            isExpanded={expandedSections.has('date')}
            onToggle={() => toggleSection('date')}
          >
            <div className="space-y-3">
              <RadioGroup
                name="dateType"
                value={filters.dateRange.type}
                onChange={(value) => updateFilter('dateRange', {
                  ...filters.dateRange,
                  type: value as FilterOptions['dateRange']['type']
                })}
                options={[
                  { value: 'all', label: 'Alle datoer' },
                  { value: 'created', label: 'Opprettelsesdato' },
                  { value: 'updated', label: 'Sist oppdatert' },
                ]}
              />
              
              {filters.dateRange.type !== 'all' && (
                <RadioGroup
                  name="datePeriod"
                  value={filters.dateRange.period}
                  onChange={(value) => updateFilter('dateRange', {
                    ...filters.dateRange,
                    period: value as FilterOptions['dateRange']['period']
                  })}
                  options={[
                    { value: 'all', label: 'Alle perioder' },
                    { value: 'today', label: 'I dag' },
                    { value: 'week', label: 'Denne uken' },
                    { value: 'month', label: 'Denne måneden' },
                    { value: 'quarter', label: 'Dette kvartalet' },
                    { value: 'year', label: 'Dette året' },
                  ]}
                />
              )}
            </div>
          </FilterSection>
        </div>
      )}
    </div>
  );
};

// Helper Components
interface FilterSectionProps {
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

const FilterSection: React.FC<FilterSectionProps> = ({
  title,
  isExpanded,
  onToggle,
  children,
}) => (
  <div className="border border-jobblogg-border rounded-lg overflow-hidden">
    <button
      onClick={onToggle}
      className="w-full px-3 py-2 flex items-center justify-between bg-jobblogg-neutral-light hover:bg-jobblogg-neutral transition-colors duration-200 touch-target"
      aria-expanded={isExpanded}
    >
      <TextStrong className="text-sm">{title}</TextStrong>
      <svg 
        className={`w-4 h-4 text-jobblogg-text-medium transition-transform duration-200 ${
          isExpanded ? 'rotate-180' : ''
        }`} 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    </button>
    {isExpanded && (
      <div className="p-3 border-t border-jobblogg-border">
        {children}
      </div>
    )}
  </div>
);

interface RadioGroupProps {
  name: string;
  value: string;
  onChange: (value: string) => void;
  options: Array<{ value: string; label: string }>;
}

const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  value,
  onChange,
  options,
}) => (
  <div className="space-y-2">
    {options.map((option) => (
      <label
        key={option.value}
        className="flex items-center gap-3 cursor-pointer touch-target mobile-focus p-1 rounded-lg hover:bg-jobblogg-neutral-light transition-colors duration-200"
      >
        <input
          type="radio"
          name={name}
          value={option.value}
          checked={value === option.value}
          onChange={(e) => onChange(e.target.value)}
          className="w-4 h-4 text-jobblogg-primary border-jobblogg-border focus:ring-jobblogg-primary/20 focus:ring-2"
        />
        <TextMuted className="text-sm flex-1">{option.label}</TextMuted>
      </label>
    ))}
  </div>
);

export default FilterPanel;
