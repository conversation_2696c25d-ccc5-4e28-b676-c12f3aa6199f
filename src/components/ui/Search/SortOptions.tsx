import React, { useState } from 'react';
import { TextStrong, TextMuted } from '../Typography';

export interface SortOption {
  field: 'name' | 'customerName' | 'createdAt' | 'updatedAt' | 'invitationStatus';
  direction: 'asc' | 'desc';
  label: string;
}

interface SortOptionsProps {
  /** Current sort option */
  currentSort: SortOption;
  /** Sort change handler */
  onChange: (sort: SortOption) => void;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show as dropdown or inline buttons */
  variant?: 'dropdown' | 'buttons';
}

const SORT_OPTIONS: SortOption[] = [
  { field: 'createdAt', direction: 'desc', label: 'Nyeste først' },
  { field: 'createdAt', direction: 'asc', label: 'Eldste først' },
  { field: 'updatedAt', direction: 'desc', label: 'Sist oppdatert' },
  { field: 'name', direction: 'asc', label: 'Prosjektnavn A-Å' },
  { field: 'name', direction: 'desc', label: 'Prosjektnavn Å-A' },
  { field: 'customerName', direction: 'asc', label: 'Kunde A-Å' },
  { field: 'customerName', direction: 'desc', label: 'Kunde Å-A' },
  { field: 'invitationStatus', direction: 'desc', label: 'Invitasjonsstatus' },
];

/**
 * Mobile-first sort options component
 * Provides dropdown and button variants for different layouts
 * 
 * @example
 * ```tsx
 * <SortOptions
 *   currentSort={sortOption}
 *   onChange={setSortOption}
 *   variant="dropdown"
 * />
 * ```
 */
export const SortOptions: React.FC<SortOptionsProps> = ({
  currentSort,
  onChange,
  className = '',
  variant = 'dropdown',
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const currentSortLabel = SORT_OPTIONS.find(
    option => option.field === currentSort.field && option.direction === currentSort.direction
  )?.label || 'Nyeste først';

  const handleSortChange = (option: SortOption) => {
    onChange(option);
    setIsDropdownOpen(false);
  };

  if (variant === 'buttons') {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {SORT_OPTIONS.map((option) => {
          const isActive = option.field === currentSort.field && option.direction === currentSort.direction;
          return (
            <button
              key={`${option.field}-${option.direction}`}
              onClick={() => handleSortChange(option)}
              className={`
                px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 touch-target mobile-focus
                ${isActive
                  ? 'bg-jobblogg-primary text-white shadow-sm'
                  : 'bg-jobblogg-neutral-light text-jobblogg-text-medium hover:bg-jobblogg-neutral hover:text-jobblogg-text-strong'
                }
              `}
              aria-pressed={isActive}
            >
              {option.label}
            </button>
          );
        })}
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Dropdown Toggle */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center gap-2 px-4 py-3 sm:py-2.5 bg-white border border-jobblogg-border rounded-xl hover:bg-jobblogg-neutral-light transition-colors duration-200 touch-target mobile-focus"
        aria-expanded={isDropdownOpen}
        aria-haspopup="listbox"
        aria-label="Velg sortering"
      >
        <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
        </svg>
        <TextStrong className="text-sm flex-1 text-left">{currentSortLabel}</TextStrong>
        <svg 
          className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
            isDropdownOpen ? 'rotate-180' : ''
          }`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsDropdownOpen(false)}
            aria-hidden="true"
          />
          
          {/* Menu */}
          <div 
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-jobblogg-border rounded-xl shadow-medium z-20 overflow-hidden"
            role="listbox"
            aria-label="Sorteringsalternativer"
          >
            {SORT_OPTIONS.map((option) => {
              const isActive = option.field === currentSort.field && option.direction === currentSort.direction;
              return (
                <button
                  key={`${option.field}-${option.direction}`}
                  onClick={() => handleSortChange(option)}
                  className={`
                    w-full px-4 py-3 text-left flex items-center justify-between transition-colors duration-200 touch-target
                    ${isActive
                      ? 'bg-jobblogg-primary text-white'
                      : 'text-jobblogg-text-strong hover:bg-jobblogg-neutral-light'
                    }
                  `}
                  role="option"
                  aria-selected={isActive}
                >
                  <TextMuted className={`text-sm ${isActive ? 'text-white' : ''}`}>
                    {option.label}
                  </TextMuted>
                  {isActive && (
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};

export default SortOptions;
