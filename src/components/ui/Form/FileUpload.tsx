import React, { useRef, useState, forwardRef } from 'react';
import { PrimaryButton } from '../Button';
import { TextMedium, TextMuted, TextStrong } from '../Typography';

/**
 * Props for the FileUpload component
 */
export interface FileUploadProps {
  /** Label for the file upload area */
  label?: string;
  /** Helper text to display */
  helperText?: string;
  /** Error message to display */
  error?: string;
  /** Whether the field is required */
  required?: boolean;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Accepted file types (e.g., '.jpg,.png,.pdf') */
  accept?: string;
  /** Maximum file size in bytes */
  maxSize?: number;
  /** Maximum number of files */
  maxFiles?: number;
  /** Whether to allow multiple file selection */
  multiple?: boolean;
  /** Current selected files */
  files?: File[];
  /** Callback when files are selected */
  onFilesChange?: (files: File[]) => void;
  /** Callback when files are removed */
  onFilesRemove?: (index: number) => void;
  /** Custom validation function */
  validateFile?: (file: File) => string | null;
  /** Custom preview component */
  renderPreview?: (file: File, index: number, onRemove: () => void) => React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Unique ID for the component */
  id?: string;
}

/**
 * FileUpload component with drag-and-drop support and WCAG AA accessibility
 * 
 * Features:
 * - Drag-and-drop file upload with visual feedback
 * - File type and size validation
 * - Multiple file support with preview
 * - WCAG AA compliant with proper contrast ratios
 * - Keyboard navigation support
 * - Screen reader friendly with ARIA attributes
 * - Error state handling with proper announcements
 * - Consistent styling with JobbLogg design system
 * 
 * @example
 * ```tsx
 * <FileUpload
 *   label="Prosjektbilder"
 *   accept=".jpg,.jpeg,.png,.webp"
 *   maxSize={10 * 1024 * 1024} // 10MB
 *   multiple
 *   files={selectedFiles}
 *   onFilesChange={setSelectedFiles}
 *   helperText="Last opp bilder av prosjektet ditt"
 * />
 * ```
 */
export const FileUpload = forwardRef<HTMLInputElement, FileUploadProps>(
  (
    {
      label,
      helperText,
      error,
      required = false,
      disabled = false,
      accept = '.jpg,.jpeg,.png,.webp',
      maxSize = 10 * 1024 * 1024, // 10MB default
      maxFiles = 1,
      multiple = false,
      files = [],
      onFilesChange,
      onFilesRemove,
      validateFile,
      renderPreview,
      className = '',
      id,
      ...props
    },
    _ref
  ) => {
    const [isDragOver, setIsDragOver] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const uploadId = id || `file-upload-${Math.random().toString(36).substr(2, 9)}`;

    // Default file validation
    const defaultValidateFile = (file: File): string | null => {
      // Check file size
      if (file.size > maxSize) {
        const maxSizeMB = (maxSize / 1024 / 1024).toFixed(1);
        return `Filen kan ikke være større enn ${maxSizeMB}MB`;
      }

      // Check file type if accept is specified
      if (accept) {
        const acceptedTypes = accept.split(',').map(type => type.trim().toLowerCase());
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        const mimeType = file.type.toLowerCase();
        
        const isAccepted = acceptedTypes.some(acceptedType => {
          if (acceptedType.startsWith('.')) {
            return fileExtension === acceptedType;
          }
          return mimeType.includes(acceptedType.replace('*', ''));
        });

        if (!isAccepted) {
          return `Kun følgende filtyper er tillatt: ${accept}`;
        }
      }

      return null;
    };

    const validateAndAddFiles = (newFiles: FileList | File[]) => {
      const fileArray = Array.from(newFiles);
      const validFiles: File[] = [];
      const errors: string[] = [];

      // Check if adding these files would exceed maxFiles
      if (files.length + fileArray.length > maxFiles) {
        errors.push(`Du kan maksimalt laste opp ${maxFiles} fil${maxFiles > 1 ? 'er' : ''}`);
        return;
      }

      // Validate each file
      fileArray.forEach(file => {
        const validationError = validateFile ? validateFile(file) : defaultValidateFile(file);
        if (validationError) {
          errors.push(`${file.name}: ${validationError}`);
        } else {
          validFiles.push(file);
        }
      });

      // Show errors if any
      if (errors.length > 0) {
        alert(errors.join('\n'));
        return;
      }

      // Add valid files
      if (validFiles.length > 0) {
        const updatedFiles = multiple ? [...files, ...validFiles] : validFiles;
        onFilesChange?.(updatedFiles);
      }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        validateAndAddFiles(selectedFiles);
      }
    };

    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragOver(true);
      }
    };

    const handleDragLeave = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      
      if (disabled) return;

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles.length > 0) {
        validateAndAddFiles(droppedFiles);
      }
    };

    const removeFile = (index: number) => {
      onFilesRemove?.(index);
      const updatedFiles = files.filter((_, i) => i !== index);
      onFilesChange?.(updatedFiles);
    };

    const openFileDialog = () => {
      if (!disabled) {
        fileInputRef.current?.click();
      }
    };

    // Default preview renderer
    const defaultRenderPreview = (file: File, index: number, onRemove: () => void) => {
      const isImage = file.type.startsWith('image/');
      const fileUrl = isImage ? URL.createObjectURL(file) : null;

      return (
        <div key={index} className="relative rounded-xl overflow-hidden bg-jobblogg-neutral animate-scale-in">
          {isImage && fileUrl ? (
            <img
              src={fileUrl}
              alt={`Preview ${index + 1}`}
              className="w-full h-32 object-cover"
              onLoad={() => URL.revokeObjectURL(fileUrl)}
            />
          ) : (
            <div className="w-full h-32 flex items-center justify-center bg-jobblogg-neutral">
              <div className="text-center">
                <svg className="w-8 h-8 mx-auto mb-2 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <TextMuted className="text-xs">{file.name}</TextMuted>
              </div>
            </div>
          )}
          
          <div className="absolute inset-0 bg-jobblogg-text-strong bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <PrimaryButton
              onClick={onRemove}
              variant="danger"
              size="sm"
              className="shadow-lg rounded-full w-8 h-8 p-0"
              aria-label={`Fjern ${file.name}`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </PrimaryButton>
          </div>
          
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-jobblogg-text-strong to-transparent p-2">
            <p className="text-white text-xs font-medium truncate">
              ✅ {file.name}
            </p>
            <p className="text-jobblogg-neutral-light text-xs">
              {(file.size / 1024 / 1024).toFixed(1)} MB
            </p>
          </div>
        </div>
      );
    };

    return (
      <div className={`${className}`}>
        {/* Label */}
        {label && (
          <div className="flex items-center justify-between mb-3">
            <TextMedium className="font-semibold">
              {label}
              {required && (
                <span className="text-jobblogg-error ml-1" aria-label="required">
                  *
                </span>
              )}
            </TextMedium>
            {!required && (
              <TextMuted className="text-sm">(valgfritt)</TextMuted>
            )}
          </div>
        )}

        {/* Upload Area */}
        {files.length === 0 && (
          <div
            className={`
              border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
              ${isDragOver
                ? 'border-jobblogg-primary bg-jobblogg-primary-soft scale-105'
                : error
                  ? 'border-jobblogg-error hover:border-jobblogg-error hover:bg-jobblogg-error-soft'
                  : 'border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-neutral'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={openFileDialog}
            role="button"
            tabIndex={disabled ? -1 : 0}
            aria-label={`Last opp fil${multiple ? 'er' : ''}`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                openFileDialog();
              }
            }}
          >
            <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <TextStrong as="h3" className="text-lg mb-2">
              {isDragOver ? '📁 Slipp fil' + (multiple ? 'ene' : 'en') + ' her!' : '📎 Last opp fil' + (multiple ? 'er' : '')}
            </TextStrong>
            <TextMedium className="mb-4">
              Dra og slipp {multiple ? 'filer' : 'en fil'} hit, eller klikk for å velge
            </TextMedium>
            {accept && (
              <div className="flex items-center justify-center gap-2 text-caption">
                {accept.split(',').map((type, index) => (
                  <React.Fragment key={type.trim()}>
                    {index > 0 && <span>•</span>}
                    <span>{type.trim().toUpperCase().replace('.', '')}</span>
                  </React.Fragment>
                ))}
                <span>•</span>
                <span>Maks {(maxSize / 1024 / 1024).toFixed(0)}MB</span>
              </div>
            )}
          </div>
        )}

        {/* File Previews */}
        {files.length > 0 && (
          <div className={`grid gap-4 ${multiple ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
            {files.map((file, index) => 
              renderPreview 
                ? renderPreview(file, index, () => removeFile(index))
                : defaultRenderPreview(file, index, () => removeFile(index))
            )}
          </div>
        )}

        {/* Add More Button (for multiple files) */}
        {multiple && files.length > 0 && files.length < maxFiles && (
          <div className="mt-4">
            <PrimaryButton
              onClick={openFileDialog}
              variant="secondary"
              disabled={disabled}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              }
            >
              Legg til flere filer
            </PrimaryButton>
          </div>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileChange}
          className="hidden"
          disabled={disabled}
          id={uploadId}
          aria-describedby={error ? `${uploadId}-error` : helperText ? `${uploadId}-helper` : undefined}
          {...props}
        />

        {/* Helper Text */}
        {helperText && !error && (
          <p 
            id={`${uploadId}-helper`}
            className="mt-2 text-sm text-jobblogg-text-muted"
          >
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {error && (
          <p 
            id={`${uploadId}-error`}
            className="mt-2 text-sm text-jobblogg-error flex items-center gap-1"
            role="alert"
            aria-live="polite"
          >
            <svg 
              className="w-4 h-4 flex-shrink-0" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
            {error}
          </p>
        )}
      </div>
    );
  }
);

FileUpload.displayName = 'FileUpload';
