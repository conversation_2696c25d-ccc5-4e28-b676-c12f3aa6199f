import React, { useState } from 'react';

interface AccordionProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  className?: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

/**
 * Accordion component following JobbLogg design system
 * 
 * Features:
 * - WCAG AA accessibility compliance with proper focus states
 * - Keyboard navigation support (Space/Enter to toggle)
 * - Screen reader friendly with proper ARIA attributes
 * - Smooth animations and visual feedback
 * - Consistent styling with JobbLogg design tokens
 * 
 * @example
 * ```tsx
 * <Accordion
 *   title="Detaljerte arbeidsinstruksjoner"
 *   defaultExpanded={false}
 *   icon={<DocumentIcon />}
 * >
 *   <TextArea
 *     placeholder="Beskriv detaljerte instruksjoner for arbeidet..."
 *     value={jobDescription}
 *     onChange={setJobDescription}
 *   />
 * </Accordion>
 * ```
 */
export const Accordion: React.FC<AccordionProps> = ({
  title,
  children,
  defaultExpanded = false,
  className = '',
  disabled = false,
  icon
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const handleToggle = () => {
    if (!disabled) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggle();
    }
  };

  return (
    <div className={`bg-white rounded-xl border border-jobblogg-border shadow-soft ${className}`}>
      {/* Accordion Header */}
      <button
        type="button"
        className={`
          w-full px-6 py-4 flex items-center justify-between text-left
          transition-all duration-200 rounded-xl
          focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
          hover:bg-jobblogg-neutral/50
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isExpanded ? 'rounded-b-none border-b border-jobblogg-border' : ''}
        `}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        aria-expanded={isExpanded}
        aria-controls={`accordion-content-${title.replace(/\s+/g, '-').toLowerCase()}`}
        aria-label={`${isExpanded ? 'Lukk' : 'Åpne'} ${title}`}
      >
        <div className="flex items-center gap-3">
          {icon && (
            <div className="w-5 h-5 text-jobblogg-primary flex-shrink-0">
              {icon}
            </div>
          )}
          <span className="text-jobblogg-text-strong font-medium text-lg">
            {title}
          </span>
        </div>
        
        {/* Chevron Icon */}
        <div className={`
          w-5 h-5 text-jobblogg-text-muted transition-transform duration-200
          ${isExpanded ? 'rotate-180' : 'rotate-0'}
        `}>
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      {/* Accordion Content */}
      <div
        id={`accordion-content-${title.replace(/\s+/g, '-').toLowerCase()}`}
        className={`
          overflow-hidden transition-all duration-300 ease-in-out
          ${isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}
        `}
        aria-hidden={!isExpanded}
      >
        <div className="px-6 pb-6 pt-2">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Accordion;
