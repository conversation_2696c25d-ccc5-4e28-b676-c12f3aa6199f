import React, { forwardRef, useState, useEffect } from 'react';
import { validateNorwegianPhone, formatNorwegianPhone, extractDigits } from '../../../utils/phoneValidation';

// Re-export validation function for external use
export { validateNorwegianPhone } from '../../../utils/phoneValidation';

/**
 * Props for the PhoneInput component
 */
export interface PhoneInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange' | 'size'> {
  /** Input label text */
  label?: string;
  /** Error message to display */
  error?: string;
  /** Helper text to display below the input */
  helperText?: string;
  /** Whether the input is required */
  required?: boolean;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Size variant of the input */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show the input in full width */
  fullWidth?: boolean;
  /** Phone number value (without +47 prefix) */
  value: string;
  /** Change handler that receives the formatted phone number */
  onChange: (value: string) => void;
  /** Phone type for validation */
  phoneType?: 'mobile' | 'landline';
  /** Enable real-time validation */
  enableValidation?: boolean;
  /** Validation callback */
  onValidation?: (isValid: boolean, error?: string) => void;
}





/**
 * PhoneInput component with Norwegian phone number formatting
 * 
 * Features:
 * - Fixed +47 prefix that cannot be edited
 * - Progressive formatting (XXX XX XXX pattern)
 * - WCAG AA accessibility compliance
 * - Keyboard navigation support
 * - Screen reader friendly with ARIA attributes
 * - Error state handling with proper announcements
 * - Focus management and visual indicators
 * - Consistent styling with JobbLogg design system
 * 
 * @example
 * ```tsx
 * <PhoneInput
 *   label="Telefon"
 *   value={phoneNumber}
 *   onChange={setPhoneNumber}
 *   required
 *   error={errors.phone}
 *   helperText="Norsk mobilnummer"
 * />
 * ```
 */
export const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  (
    {
      label,
      error,
      helperText,
      required = false,
      disabled = false,
      size = 'medium',
      fullWidth = false,
      value,
      onChange,
      phoneType = 'mobile',
      enableValidation = false,
      onValidation,
      className = '',
      id,
      ...props
    },
    ref
  ) => {
    const [displayValue, setDisplayValue] = useState('');
    const [_isFocused, setIsFocused] = useState(false);

    // Generate unique ID for accessibility
    const inputId = id || `phone-input-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = `${inputId}-error`;
    const helperId = `${inputId}-helper`;

    // Build describedBy for accessibility
    const describedBy = [
      error ? errorId : null,
      helperText ? helperId : null
    ].filter(Boolean).join(' ') || undefined;

    // Size classes
    const sizeClasses = {
      small: 'text-sm px-3 py-2 min-h-[36px]',
      medium: 'text-base px-4 py-3 min-h-[44px]',
      large: 'text-lg px-5 py-4 min-h-[52px]'
    };

    // Update display value when value prop changes
    useEffect(() => {
      const formatted = formatNorwegianPhone(value);
      setDisplayValue(formatted);
    }, [value]);

    // Handle input change
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const digits = extractDigits(inputValue);

      // Limit to exactly 8 digits for Norwegian phone numbers
      const limitedDigits = digits.slice(0, 8);
      const formatted = formatNorwegianPhone(limitedDigits);

      // Perform validation if enabled
      if (enableValidation && onValidation) {
        const validation = validateNorwegianPhone(limitedDigits, phoneType);
        onValidation(validation.isValid, validation.error);
      }

      // Debug logging
      console.log('📱 PhoneInput change:', {
        inputValue,
        digits,
        limitedDigits,
        digitsLength: limitedDigits.length,
        formatted
      });

      setDisplayValue(formatted);
      onChange(limitedDigits); // Pass limited raw digits to parent
    };

    // Handle focus
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      if (props.onFocus) {
        props.onFocus(e);
      }
    };

    // Handle blur
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      if (props.onBlur) {
        props.onBlur(e);
      }
    };

    // Handle input to prevent typing beyond 8 digits
    const handleInput = (e: React.FormEvent<HTMLInputElement>) => {
      const target = e.target as HTMLInputElement;
      const digits = extractDigits(target.value);

      // If user tries to type more than 8 digits, prevent it
      if (digits.length > 8) {
        const limitedDigits = digits.slice(0, 8);
        const formatted = formatNorwegianPhone(limitedDigits);
        target.value = formatted;
        setDisplayValue(formatted);
        onChange(limitedDigits);
      }
    };

    // Modern input classes with enhanced flat design and mobile-optimized interactions
    const baseClasses = `
      input-modern focus-ring-enhanced interactive-press mobile-focus
      ${error
        ? 'border-jobblogg-error focus:ring-jobblogg-error focus:border-jobblogg-error animate-shake'
        : 'hover:border-jobblogg-primary-light hover:shadow-soft'
      }
      ${sizeClasses[size]}
      ${fullWidth ? 'w-full' : ''}
      pl-16
    `.trim().replace(/\s+/g, ' ');

    return (
      <div className={`space-y-2 ${fullWidth ? 'w-full' : ''}`}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-jobblogg-text-strong"
          >
            {label}
            {required && <span className="text-jobblogg-error ml-1" aria-label="påkrevd">*</span>}
          </label>
        )}

        {/* Input Container */}
        <div className="relative group">
          {/* Fixed +47 Prefix */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-jobblogg-text-strong font-medium pointer-events-none z-10">
            +47
          </div>

          {/* Input */}
          <input
            ref={ref}
            id={inputId}
            type="tel"
            className={`${baseClasses} ${className}`}
            disabled={disabled}
            required={required}
            value={displayValue}
            onChange={handleChange}
            onInput={handleInput}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder="XXX XX XXX"
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={describedBy}
            inputMode="numeric"
            pattern="[0-9\s]*"
            maxLength={11} // "XXX XX XXX" = 11 characters with spaces
            {...props}
          />
        </div>

        {/* Helper Text */}
        {helperText && !error && (
          <p 
            id={helperId}
            className="text-sm text-jobblogg-text-muted"
          >
            {helperText}
          </p>
        )}

        {/* Error Message */}
        {error && (
          <p 
            id={errorId}
            className="text-sm text-jobblogg-error flex items-center gap-1"
            role="alert"
            aria-live="polite"
          >
            <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </p>
        )}
      </div>
    );
  }
);

PhoneInput.displayName = 'PhoneInput';

export default PhoneInput;
