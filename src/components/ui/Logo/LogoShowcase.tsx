import React from 'react';
import { Jobb<PERSON>ogg<PERSON><PERSON>, Favi<PERSON><PERSON>ogo } from './';

/**
 * LogoShowcase component for demonstrating all logo variations
 * This component is useful for design documentation and testing
 */
export const LogoShowcase: React.FC = () => {
  return (
    <div className="p-8 space-y-12 bg-jobblogg-neutral-secondary min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-jobblogg-text-strong mb-8">
          JobbLogg Logo Showcase
        </h1>

        {/* Horizontal Logos */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Horizontal Logos
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Small */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-sm font-medium text-jobblogg-text-medium mb-4">
                Small (24px icon)
              </h3>
              <JobbLoggLogo variant="horizontal" size="sm" />
            </div>

            {/* Medium */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-sm font-medium text-jobblogg-text-medium mb-4">
                Medium (32px icon)
              </h3>
              <JobbLoggLogo variant="horizontal" size="md" />
            </div>

            {/* Large */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-sm font-medium text-jobblogg-text-medium mb-4">
                Large (40px icon)
              </h3>
              <JobbLoggLogo variant="horizontal" size="lg" />
            </div>

            {/* Extra Large */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-sm font-medium text-jobblogg-text-medium mb-4">
                Extra Large (48px icon)
              </h3>
              <JobbLoggLogo variant="horizontal" size="xl" />
            </div>
          </div>
        </section>

        {/* Compact Logos */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Compact Logos (JL)
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">Small</h3>
              <JobbLoggLogo variant="compact" size="sm" />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">Medium</h3>
              <JobbLoggLogo variant="compact" size="md" />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">Large</h3>
              <JobbLoggLogo variant="compact" size="lg" />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">XL</h3>
              <JobbLoggLogo variant="compact" size="xl" />
            </div>
          </div>
        </section>

        {/* Icon Only */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Icon Only
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">Small</h3>
              <JobbLoggLogo variant="icon-only" size="sm" />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">Medium</h3>
              <JobbLoggLogo variant="icon-only" size="md" />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">Large</h3>
              <JobbLoggLogo variant="icon-only" size="lg" />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">XL</h3>
              <JobbLoggLogo variant="icon-only" size="xl" />
            </div>
          </div>
        </section>

        {/* Favicon */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Favicon Versions
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">16px</h3>
              <FaviconLogo size={16} />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">24px</h3>
              <FaviconLogo size={24} />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">32px</h3>
              <FaviconLogo size={32} />
            </div>
            <div className="bg-white p-4 rounded-xl border border-jobblogg-border text-center">
              <h3 className="text-xs font-medium text-jobblogg-text-medium mb-3">48px</h3>
              <FaviconLogo size={48} />
            </div>
          </div>
        </section>

        {/* Dark Background Test */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Dark Background Test
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-jobblogg-text-strong p-6 rounded-xl">
              <h3 className="text-sm font-medium text-white mb-4">
                Horizontal on Dark
              </h3>
              <JobbLoggLogo variant="horizontal" size="lg" />
            </div>
            <div className="bg-jobblogg-primary p-6 rounded-xl">
              <h3 className="text-sm font-medium text-white mb-4">
                Icon Only on Primary
              </h3>
              <JobbLoggLogo variant="icon-only" size="lg" />
            </div>
          </div>
        </section>

        {/* Usage Guidelines */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Usage Guidelines
          </h2>
          
          <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
            <div className="prose prose-sm max-w-none">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Logo Specifications
              </h3>
              
              <ul className="space-y-2 text-jobblogg-text-medium">
                <li><strong>Typography:</strong> Inter font, SemiBold weight</li>
                <li><strong>Colors:</strong> "Jobb" in #1E1E1E, "Logg" in #1D4ED8</li>
                <li><strong>Icon:</strong> Notepad (#F9FAFB) + Chat bubble (#10B981)</li>
                <li><strong>Minimum size:</strong> 24px icon height</li>
                <li><strong>Clear space:</strong> Minimum 8px around logo</li>
                <li><strong>Variants:</strong> Horizontal (default), Compact (JL), Icon-only</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};
