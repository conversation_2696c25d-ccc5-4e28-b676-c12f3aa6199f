import React from 'react';

interface JobbLoggLogoProps {
  variant?: 'horizontal' | 'compact' | 'icon-only';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const JobbLoggLogo: React.FC<JobbLoggLogoProps> = ({ 
  variant = 'horizontal', 
  size = 'md',
  className = '' 
}) => {
  // Size configurations
  const sizeConfig = {
    sm: {
      icon: { width: 24, height: 24 },
      text: 'text-lg',
      spacing: 'gap-2'
    },
    md: {
      icon: { width: 32, height: 32 },
      text: 'text-xl',
      spacing: 'gap-3'
    },
    lg: {
      icon: { width: 40, height: 40 },
      text: 'text-2xl',
      spacing: 'gap-4'
    },
    xl: {
      icon: { width: 48, height: 48 },
      text: 'text-3xl',
      spacing: 'gap-4'
    }
  };

  const config = sizeConfig[size];

  // Logo icon component
  const LogoIcon = ({ width, height }: { width: number; height: number }) => (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="flex-shrink-0"
    >
      {/* Notatblokk (notepad) - main rectangle */}
      <rect
        x="4"
        y="4"
        width="20"
        height="24"
        rx="3"
        ry="3"
        fill="#F9FAFB"
        stroke="#E5E7EB"
        strokeWidth="1"
      />
      
      {/* Tekstlinjer (text lines) */}
      <line
        x1="8"
        y1="10"
        x2="20"
        y2="10"
        stroke="#1D4ED8"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <line
        x1="8"
        y1="14"
        x2="18"
        y2="14"
        stroke="#1D4ED8"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <line
        x1="8"
        y1="18"
        x2="16"
        y2="18"
        stroke="#1D4ED8"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      
      {/* Chat-boble (chat bubble) */}
      <g transform="translate(18, 20)">
        <path
          d="M0 4C0 1.79086 1.79086 0 4 0H8C10.2091 0 12 1.79086 12 4V6C12 8.20914 10.2091 10 8 10H4.5L1.5 12V4Z"
          fill="#10B981"
        />
        {/* Hvit prikk i chat-boblen */}
        <circle
          cx="6"
          cy="4"
          r="1"
          fill="white"
        />
      </g>
    </svg>
  );

  // Logo text component
  const LogoText = () => (
    <div className={`font-semibold ${config.text} leading-none`}>
      <span className="text-jobblogg-text-strong">Jobb</span>
      <span className="text-jobblogg-primary">Logg</span>
    </div>
  );

  // Render based on variant
  if (variant === 'icon-only') {
    return (
      <div className={className}>
        <LogoIcon width={config.icon.width} height={config.icon.height} />
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center ${config.spacing} ${className}`}>
        <LogoIcon width={config.icon.width} height={config.icon.height} />
        <div className={`font-semibold ${config.text} leading-none`}>
          <span className="text-jobblogg-text-strong">J</span>
          <span className="text-jobblogg-primary">L</span>
        </div>
      </div>
    );
  }

  // Default horizontal variant
  return (
    <div className={`flex items-center ${config.spacing} ${className}`}>
      <LogoIcon width={config.icon.width} height={config.icon.height} />
      <LogoText />
    </div>
  );
};

export default JobbLoggLogo;
