import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { generateStaticMapUrl, isGoogleMapsConfigured, isAddressComplete, getFallbackMapImage, formatAddress } from '../../../utils/googleMaps';
import { TextStrong, TextMuted } from '../Typography';
import { DirectionsButton } from '../../GoogleMaps/DirectionsButton';

interface SubcontractorProjectCardProps {
  /** Project title */
  title: string;
  /** Project description */
  description: string;
  /** Project ID for navigation */
  projectId: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Click handler for card interaction */
  onClick: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Animation delay for staggered animations */
  animationDelay?: string;
  /** Customer information */
  customer?: {
    name: string;
    type: 'privat' | 'bedrift';
    address?: string;
    streetAddress?: string;
    postalCode?: string;
    city?: string;
    entrance?: string;
    contactPerson?: string;
  } | null;
  /** Invitation status for subcontractor projects */
  invitationStatus?: 'pending' | 'accepted' | 'declined' | 'expired';
  /** Who assigned this project */
  assignedBy?: string;
  /** When the assignment was made */
  assignedAt?: number;
  /** Subcontractor specialization */
  specialization?: string;
  /** Main contractor company name */
  mainContractorCompany?: string;
  /** User's access level */
  userAccessLevel?: string;
}

/**
 * Enhanced project card for subcontractor projects with invitation status
 * Shows invitation status, assignment details, and main contractor information
 */
export const SubcontractorProjectCard: React.FC<SubcontractorProjectCardProps> = ({
  title,
  description: _description,
  projectId: _projectId,
  updatedAt: _updatedAt,
  onClick,
  className = '',
  animationDelay = '0s',
  customer,
  invitationStatus,
  assignedBy: _assignedBy,
  assignedAt: _assignedAt,
  specialization: _specialization,
  mainContractorCompany: _mainContractorCompany,
  userAccessLevel: _userAccessLevel,
}) => {
  const { user: _user } = useUser();
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleClick = () => {
    onClick();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick();
    }
  };

  const getStatusBadge = () => {
    switch (invitationStatus) {
      case 'pending':
        return (
          <div className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-jobblogg-warning text-white shadow-sm">
            Underleverandør
          </div>
        );
      case 'accepted':
        return (
          <div className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-jobblogg-accent text-white shadow-sm">
            Underleverandør
          </div>
        );
      case 'declined':
        return (
          <div className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-jobblogg-error text-white shadow-sm">
            Avslått
          </div>
        );
      case 'expired':
        return (
          <div className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-jobblogg-text-medium text-white shadow-sm">
            Utløpt
          </div>
        );
      default:
        return (
          <div className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-jobblogg-accent text-white shadow-sm">
            Underleverandør
          </div>
        );
    }
  };

  // const _isPending = invitationStatus === 'pending'; // TODO: Use for pending status styling

  // Map integration logic - same as regular ProjectCard
  const hasCompleteAddress = customer && isAddressComplete(
    customer.streetAddress,
    customer.postalCode,
    customer.city
  );

  const isConfigured = isGoogleMapsConfigured();
  const shouldShowMap = isConfigured && hasCompleteAddress && !imageError;

  // Generate map URL if we have complete address
  let mapUrl = getFallbackMapImage();
  if (shouldShowMap && customer?.streetAddress && customer?.postalCode && customer?.city) {
    mapUrl = generateStaticMapUrl(customer.streetAddress, customer.postalCode, customer.city, {
      width: 300,
      height: 192, // h-48 = 192px
      zoom: 15
    });
  }

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  // Format display address like ProjectMapCard
  const displayAddress = customer?.streetAddress && customer?.postalCode && customer?.city
    ? formatAddress(customer.streetAddress, customer.postalCode, customer.city, customer.entrance)
    : customer?.address || 'Ingen adresse';

  const hasValidAddress = customer && isAddressComplete(
    customer.streetAddress,
    customer.postalCode,
    customer.city
  );

  const handleDirectionsClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
  };

  return (
    <div
      className={`bg-white rounded-xl shadow-medium border border-jobblogg-border overflow-hidden hover-lift transition-all duration-200 cursor-pointer relative ${className}`}
      style={{ animationDelay }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Åpne prosjekt: ${title}`}
    >
      {/* Subcontractor Indicator - Left border accent */}
      <div className="absolute left-0 top-0 bottom-0 w-1 bg-jobblogg-accent rounded-l-xl z-10"></div>

      {/* Status Badge Overlay - positioned like ProjectMapCard */}
      <div className="absolute top-4 right-4 z-20">
        {getStatusBadge()}
      </div>

      {/* Map Image - Same structure as ProjectMapCard */}
      <div className="relative" style={{ height: '200px' }}>
        {isLoading && shouldShowMap && (
          <div className="absolute inset-0 bg-jobblogg-neutral animate-pulse flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        <img
          src={mapUrl}
          alt={`Kart over ${displayAddress}`}
          className="w-full h-full object-cover"
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ display: (isLoading && shouldShowMap) ? 'none' : 'block' }}
        />

        {/* Fallback/Error State */}
        {(!shouldShowMap || imageError) && (
          <div className="absolute inset-0 bg-jobblogg-neutral flex items-center justify-center">
            <div className="text-center">
              <div className="w-12 h-12 mx-auto mb-2 bg-jobblogg-text-muted/20 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <TextMuted className="text-xs">
                {!isConfigured ? 'Kart ikke tilgjengelig' : 'Kartbilde'}
              </TextMuted>
            </div>
          </div>
        )}

        {/* Project Name Overlay - Same as ProjectMapCard */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
          <TextStrong className="text-white text-lg font-semibold">
            {title}
          </TextStrong>
        </div>
      </div>

      {/* Project Info - Same structure as ProjectMapCard */}
      <div className="p-4">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <TextStrong className="text-sm font-medium mb-1">
              {customer?.name || 'Ukjent kunde'}
            </TextStrong>
            <div className="flex items-start gap-1">
              <svg className="w-3 h-3 text-jobblogg-text-medium mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <TextMuted className="text-xs leading-tight">
                {displayAddress}
              </TextMuted>
            </div>
          </div>

          {/* Directions Button - Same as ProjectMapCard */}
          {hasValidAddress && customer?.streetAddress && customer?.postalCode && customer?.city && (
            <div onClick={handleDirectionsClick}>
              <DirectionsButton
                streetAddress={customer.streetAddress}
                postalCode={customer.postalCode}
                city={customer.city}
                size="sm"
                className="flex-shrink-0"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubcontractorProjectCard;
