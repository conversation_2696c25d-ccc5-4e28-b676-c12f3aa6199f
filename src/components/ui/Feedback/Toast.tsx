import React, { useEffect, useState } from 'react';

interface ToastProps {
  /** Toast message */
  message: string;
  /** Toast type */
  type?: 'success' | 'error' | 'warning' | 'info';
  /** Duration in milliseconds */
  duration?: number;
  /** Show/hide state */
  show: boolean;
  /** Close handler */
  onClose: () => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Enhanced toast notification component with micro-interactions
 * 
 * @example
 * ```tsx
 * <Toast
 *   message="Prosjekt lagret!"
 *   type="success"
 *   show={showToast}
 *   onClose={() => setShowToast(false)}
 * />
 * ```
 */
export const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  duration = 6000, // Extended default duration to 6 seconds
  show,
  onClose,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      setIsExiting(false);
      
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [show, duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onClose();
    }, 300); // Match exit animation duration
  };

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-jobblogg-success text-white border-jobblogg-success';
      case 'error':
        return 'bg-jobblogg-error text-white border-jobblogg-error';
      case 'warning':
        return 'bg-jobblogg-warning text-jobblogg-text-strong border-jobblogg-warning';
      default:
        return 'bg-jobblogg-primary text-white border-jobblogg-primary';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-5 h-5 animate-success-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5 animate-shake" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5 animate-pulse-soft" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 animate-pulse-soft" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  if (!isVisible && !show) return null;

  return (
    <div
      className={`
        fixed top-4 right-4 z-50 max-w-sm w-full
        ${isExiting ? 'animate-slide-out-right' : 'animate-slide-in-right'}
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      <div
        className={`
          ${getTypeStyles()}
          rounded-xl shadow-lg border-2 p-4 flex items-center gap-3
          hover-lift interactive-press group cursor-pointer
        `.trim().replace(/\s+/g, ' ')}
        onClick={handleClose}
      >
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">
            {message}
          </p>
        </div>

        <button
          onClick={(e) => {
            e.stopPropagation();
            handleClose();
          }}
          className="flex-shrink-0 p-1 rounded-full hover:bg-white/20 transition-colors duration-200 focus-ring-enhanced"
          aria-label="Lukk melding"
        >
          <svg className="w-4 h-4 hover-rotate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Toast;
