import React from 'react';

export interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message?: string;
  children?: React.ReactNode;
  className?: string;
}

/**
 * Alert component for displaying contextual feedback messages
 * 
 * Features:
 * - Multiple alert types with appropriate styling
 * - Optional title and message
 * - Consistent with JobbLogg design system
 * - Accessible with proper ARIA attributes
 */
export const Alert: React.FC<AlertProps> = ({
  type,
  title,
  message,
  className = ''
}) => {
  const getAlertStyles = () => {
    switch (type) {
      case 'success':
        return {
          container: 'bg-jobblogg-success-soft border-jobblogg-success-border text-jobblogg-success-text',
          icon: 'text-jobblogg-success',
          iconPath: 'M5 13l4 4L19 7'
        };
      case 'error':
        return {
          container: 'bg-jobblogg-error-soft border-jobblogg-error-border text-jobblogg-error-text',
          icon: 'text-jobblogg-error',
          iconPath: 'M6 18L18 6M6 6l12 12'
        };
      case 'warning':
        return {
          container: 'bg-jobblogg-warning-soft border-jobblogg-warning-border text-jobblogg-warning-text',
          icon: 'text-jobblogg-warning',
          iconPath: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z'
        };
      case 'info':
      default:
        return {
          container: 'bg-jobblogg-primary-soft border-jobblogg-primary-border text-jobblogg-primary-text',
          icon: 'text-jobblogg-primary',
          iconPath: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
        };
    }
  };

  const styles = getAlertStyles();

  const getAriaRole = () => {
    switch (type) {
      case 'error':
        return 'alert';
      case 'warning':
        return 'alert';
      default:
        return 'status';
    }
  };

  return (
    <div
      className={`
        rounded-lg border p-4 flex items-start gap-3
        ${styles.container}
        ${className}
      `}
      role={getAriaRole()}
      aria-live={type === 'error' || type === 'warning' ? 'assertive' : 'polite'}
    >
      {/* Icon */}
      <div className="flex-shrink-0">
        <svg
          className={`w-5 h-5 ${styles.icon}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d={styles.iconPath}
          />
        </svg>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {title && (
          <h3 className="text-sm font-semibold mb-1">
            {title}
          </h3>
        )}
        <p className="text-sm">
          {message}
        </p>
      </div>
    </div>
  );
};

export default Alert;
