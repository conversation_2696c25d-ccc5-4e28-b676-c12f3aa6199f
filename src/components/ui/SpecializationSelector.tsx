import React, { useState } from 'react';
import { 
  ALL_SPECIALIZATIONS, 
  SPECIALIZATION_CATEGORIES, 
  getSpecializationsByCategory,
  getSpecializationById,
  type Specialization 
} from '../../utils/specializations';
import { 
  SelectInput, 
  SecondaryButton, 
  BodyText, 
  TextMuted, 
  Alert 
} from './index';

interface SpecializationSelectorProps {
  primarySpecialization?: string;
  secondarySpecializations?: string[];
  onPrimaryChange: (specialization: string | undefined) => void;
  onSecondaryChange: (specializations: string[]) => void;
  suggestedSpecialization?: Specialization | null;
  showSuggestion?: boolean;
  maxSecondary?: number;
  error?: string;
  disabled?: boolean;
}

export const SpecializationSelector: React.FC<SpecializationSelectorProps> = ({
  primarySpecialization,
  secondarySpecializations = [],
  onPrimaryChange,
  onSecondaryChange,
  suggestedSpecialization,
  showSuggestion = true,
  maxSecondary = 3,
  error,
  disabled = false
}) => {
  const [showSecondarySelector, setShowSecondarySelector] = useState(false);

  // Group specializations by category for better UX
  const specializationOptions = Object.entries(SPECIALIZATION_CATEGORIES).map(([category, label]) => ({
    label,
    options: getSpecializationsByCategory(category as Specialization['category']).map(spec => ({
      value: spec.id,
      label: spec.name,
      description: spec.description
    }))
  }));

  // Handle primary specialization change
  const handlePrimaryChange = (value: string) => {
    if (value === '') {
      onPrimaryChange(undefined);
    } else {
      onPrimaryChange(value);
    }
  };

  // Handle secondary specialization addition
  const handleAddSecondary = (value: string) => {
    if (value && !secondarySpecializations.includes(value) && value !== primarySpecialization) {
      const newSecondary = [...secondarySpecializations, value];
      onSecondaryChange(newSecondary);
    }
  };

  // Handle secondary specialization removal
  const handleRemoveSecondary = (specializationId: string) => {
    const newSecondary = secondarySpecializations.filter(id => id !== specializationId);
    onSecondaryChange(newSecondary);
  };

  // Handle suggestion acceptance
  const handleAcceptSuggestion = () => {
    if (suggestedSpecialization) {
      onPrimaryChange(suggestedSpecialization.id);
    }
  };

  // Get available secondary options (exclude primary and already selected)
  const availableSecondaryOptions = ALL_SPECIALIZATIONS
    .filter(spec => 
      spec.id !== primarySpecialization && 
      !secondarySpecializations.includes(spec.id)
    )
    .map(spec => ({
      value: spec.id,
      label: spec.name
    }));

  return (
    <div className="space-y-4">
      {/* Suggestion from Brønnøysundregisteret */}
      {showSuggestion && suggestedSpecialization && !primarySpecialization && (
        <Alert 
          type="info" 
          title="Forslag basert på næringskode"
          className="mb-4"
        >
          <div className="space-y-3">
            <BodyText>
              Basert på bedriftens næringskode foreslår vi: <strong>{suggestedSpecialization.name}</strong>
            </BodyText>
            {suggestedSpecialization.description && (
              <TextMuted className="text-sm">
                {suggestedSpecialization.description}
              </TextMuted>
            )}
            <div className="flex gap-2">
              <SecondaryButton
                size="sm"
                onClick={handleAcceptSuggestion}
                disabled={disabled}
              >
                Bruk forslag
              </SecondaryButton>
            </div>
          </div>
        </Alert>
      )}

      {/* Primary Specialization */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-jobblogg-text-strong">
          Hovedfagområde *
        </label>
        <SelectInput
          value={primarySpecialization || ''}
          onChange={(e) => handlePrimaryChange(e.target.value)}
          placeholder="Velg hovedfagområde"
          error={error}
          disabled={disabled}
          groupedOptions={specializationOptions}
        />
        <TextMuted className="text-xs">
          Velg det fagområdet som best beskriver bedriftens hovedvirksomhet
        </TextMuted>
      </div>

      {/* Secondary Specializations */}
      {primarySpecialization && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="block text-sm font-medium text-jobblogg-text-strong">
              Tilleggsfagområder
            </label>
            {!showSecondarySelector && secondarySpecializations.length < maxSecondary && (
              <SecondaryButton
                size="sm"
                onClick={() => setShowSecondarySelector(true)}
                disabled={disabled}
              >
                + Legg til fagområde
              </SecondaryButton>
            )}
          </div>

          {/* Show existing secondary specializations */}
          {secondarySpecializations.length > 0 && (
            <div className="space-y-2">
              {secondarySpecializations.map(specializationId => {
                const spec = getSpecializationById(specializationId);
                return spec ? (
                  <div 
                    key={specializationId}
                    className="flex items-center justify-between bg-jobblogg-neutral-secondary rounded-lg px-3 py-2"
                  >
                    <div>
                      <BodyText className="text-sm font-medium">
                        {spec.name}
                      </BodyText>
                      {spec.description && (
                        <TextMuted className="text-xs">
                          {spec.description}
                        </TextMuted>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveSecondary(specializationId)}
                      disabled={disabled}
                      className="text-jobblogg-error hover:text-jobblogg-error-dark transition-colors p-1"
                      aria-label={`Fjern ${spec.name}`}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ) : null;
              })}
            </div>
          )}

          {/* Secondary specialization selector */}
          {showSecondarySelector && availableSecondaryOptions.length > 0 && (
            <div className="space-y-2">
              <SelectInput
                value=""
                onChange={(e) => {
                  const value = e.target.value;
                  if (value) {
                    handleAddSecondary(value);
                    setShowSecondarySelector(false);
                  }
                }}
                placeholder="Velg tilleggsfagområde"
                disabled={disabled}
                options={availableSecondaryOptions}
              />
              <div className="flex gap-2">
                <SecondaryButton
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowSecondarySelector(false)}
                  disabled={disabled}
                >
                  Avbryt
                </SecondaryButton>
              </div>
            </div>
          )}

          <TextMuted className="text-xs">
            Legg til inntil {maxSecondary} tilleggsfagområder som bedriften også tilbyr
          </TextMuted>
        </div>
      )}
    </div>
  );
};
