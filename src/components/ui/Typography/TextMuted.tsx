import React from 'react';

interface TextMutedProps {
  /** Text content */
  children: React.ReactNode;
  /** HTML element to render */
  as?: 'p' | 'span' | 'div';
  /** Additional CSS classes */
  className?: string;
  /** HTML id attribute */
  id?: string;
}

/**
 * Muted text component for secondary information and captions
 * Uses jobblogg-text-muted color (4.5:1 contrast ratio - WCAG AA compliant)
 *
 * @example
 * ```tsx
 * <TextMuted>Sekundær informasjon</TextMuted>
 * <TextMuted as="span" className="text-xs">Liten hjelpetekst</TextMuted>
 * <TextMuted>Opprettet 26. januar 2025</TextMuted>
 * ```
 */
export const TextMuted: React.FC<TextMutedProps> = ({
  children,
  as: Component = 'p',
  className = '',
}) => {
  return (
    <Component
      className={`
        text-jobblogg-text-muted text-small
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default TextMuted;
