# JobbLogg CTA Button Design Principles & Implementation Guidelines

## 🎯 Overview

This document establishes comprehensive design principles for Call-to-Action (CTA) buttons in JobbLogg, following 2025 design trends while maintaining accessibility, usability, and brand consistency.

## 📝 Typography Standards

### Font Specifications
- **Font Family**: Inter (system fallback: system-ui, sans-serif)
- **Font Weight**: 
  - Primary CTAs: SemiBold (600) for maximum impact
  - Secondary CTAs: Medium (500) for balanced hierarchy
- **Text Style**: Sentence case only (e.g., "Prøv gratis" not "PRØV GRATIS")
- **Content Strategy**: Clear, action-oriented Norwegian text without icons
- **Text Sizing**: Responsive scaling
  - Mobile: `text-sm` (14px)
  - Desktop: `text-base` (16px)

### Typography Hierarchy
```css
Primary CTA:   font-semibold text-sm sm:text-base
Secondary CTA: font-medium text-sm sm:text-base
Tertiary CTA:  font-medium text-sm
```

## 🎨 Color System Integration

### Primary CTA Colors
- **Background**: `#2563EB` (jobblogg-primary)
- **Text**: White for maximum contrast (WCAG AAA: 8.59:1)
- **Hover**: `#3B82F6` (jobblogg-primary-light)
- **Active**: `#1E40AF` (jobblogg-primary-dark)
- **Focus Ring**: `#2563EB` with 2px offset

### Secondary CTA Colors
- **Background**: `#10B981` (jobblogg-accent)
- **Text**: White for optimal contrast (WCAG AAA: 4.89:1)
- **Hover**: `#34D399` (jobblogg-accent-light)
- **Active**: `#059669` (jobblogg-accent-dark)
- **Focus Ring**: `#10B981` with 2px offset

### State-Based Color Variations
```css
/* Hover States - 10% lighter */
hover:bg-jobblogg-primary-light (200ms transition)

/* Active States - Scale + darker color */
active:bg-jobblogg-primary-dark active:scale-[0.98]

/* Disabled States */
disabled:opacity-50 disabled:cursor-not-allowed
```

## 📐 Sizing and Spacing Standards

### Touch Target Compliance
- **Minimum Height**: 44px (WCAG AA compliance)
- **Minimum Width**: 44px for icon-only buttons
- **Touch Area**: Ensure 44x44px minimum for all interactive elements

### Responsive Padding System
```css
/* Small (Mobile) */
px-4 py-2.5 (16px horizontal, 10px vertical)

/* Medium (Tablet) */
sm:px-5 sm:py-3 (20px horizontal, 12px vertical)

/* Large (Desktop) */
lg:px-6 lg:py-3.5 (24px horizontal, 14px vertical)
```

### Border Radius
- **Standard**: `rounded-xl` (12px) - Modern but not pill-shaped
- **Consistency**: All buttons use same border radius for visual harmony

## ⚡ Micro-Interactions Specification

### Transition Timing
```css
/* Color Transitions */
transition-colors duration-200 ease-in-out

/* Transform Transitions */
transition-transform duration-150 ease-out

/* Shadow Transitions */
transition-shadow duration-200 ease-in-out
```

### Hover Effects
```css
/* Elevation Change */
shadow-soft → shadow-medium

/* Color Shift */
bg-jobblogg-primary → bg-jobblogg-primary-light

/* Subtle Scale (Optional) */
hover:scale-[1.02] (for special emphasis)
```

### Active States
```css
/* Scale Down */
active:scale-[0.98]

/* Shadow Reduction */
active:shadow-soft

/* Color Darkening */
active:bg-jobblogg-primary-dark
```

### Loading States
```css
/* Spinner Animation */
animate-spin (2s linear infinite)

/* Text Opacity */
opacity-75 during loading

/* Prevent Interaction */
pointer-events-none during loading
```

### Focus States
```css
/* Visible Focus Ring */
focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2

/* High Contrast Mode Support */
focus:ring-offset-white
```

## 🌐 Norwegian Localization Standards

### Common CTA Terminology
```
Primary Actions:
- "Registrer deg" (Sign up)
- "Logg inn" (Sign in)
- "Opprett prosjekt" (Create project)
- "Send melding" (Send message)
- "Lagre endringer" (Save changes)
- "Bekreft" (Confirm)

Secondary Actions:
- "Avbryt" (Cancel)
- "Tilbake" (Back)
- "Hopp over" (Skip)
- "Les mer" (Read more)
- "Vis detaljer" (Show details)

Destructive Actions:
- "Slett" (Delete)
- "Fjern" (Remove)
- "Arkiver" (Archive)
```

### Tone and Voice
- **Professional**: Suitable for construction industry context
- **Clear**: Avoid technical jargon
- **Action-Oriented**: Use imperative verbs
- **Concise**: Maximum 3-4 words per button

## ♿ Accessibility Requirements

### WCAG AA Compliance
- **Color Contrast**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Touch Targets**: Minimum 44x44px
- **Focus Indicators**: Visible and high contrast
- **Screen Reader Support**: Proper ARIA labels and roles

### Keyboard Navigation
```tsx
// Support Enter and Space keys
onKeyDown={(e) => {
  if (e.key === 'Enter' || e.key === ' ') {
    e.preventDefault();
    onClick();
  }
}}
```

### Screen Reader Considerations
```tsx
// Loading state announcement
aria-label={loading ? "Laster..." : children}
aria-disabled={disabled || loading}
```

## 🔧 Implementation Requirements

### TypeScript Interface
```tsx
interface CTAButtonProps {
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  className?: string;
}
```

### Required Features
- ✅ Responsive sizing across all breakpoints
- ✅ Loading states with spinner animation
- ✅ Disabled states with proper visual feedback
- ✅ Keyboard navigation support
- ✅ Focus management and visual indicators
- ✅ ARIA attributes for accessibility
- ✅ Norwegian localization support

### Design Token Usage
- ❌ No hardcoded colors or spacing values
- ✅ Use only jobblogg-prefixed Tailwind tokens
- ✅ Consistent with existing design system
- ✅ Maintainable and scalable architecture

## 📊 Button Hierarchy

### Primary CTA
- **Usage**: Main action on page/section
- **Limit**: Maximum 1 per viewport
- **Color**: JobbLogg primary blue
- **Examples**: "Opprett prosjekt", "Registrer deg"

### Secondary CTA
- **Usage**: Supporting actions
- **Limit**: 2-3 per viewport
- **Color**: JobbLogg success green
- **Examples**: "Les mer", "Kontakt oss"

### Tertiary CTA
- **Usage**: Less important actions
- **Style**: Outline or ghost variant
- **Examples**: "Avbryt", "Hopp over"

## 🚀 2025 Design Trends Integration

### Modern Flat Design
- Clean, minimal aesthetic
- Subtle shadows for depth
- No gradients or heavy effects

### Enhanced Micro-Interactions
- Smooth, purposeful animations
- Tactile feedback through transforms
- Progressive disclosure of information

### Accessibility-First Approach
- High contrast ratios
- Large touch targets
- Clear focus indicators
- Screen reader optimization

### Performance Optimization
- Hardware-accelerated animations
- Efficient CSS transitions
- Minimal DOM manipulation
