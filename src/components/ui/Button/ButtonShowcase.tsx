import React, { useState } from 'react';
import { 
  CTAButton, 
  PrimaryButton, 
  SecondaryButton, 
  TertiaryButton, 
  GhostButton, 
  DangerButton 
} from './';

/**
 * Comprehensive showcase of all CTA button variants following JobbLogg 2025 design principles
 * 
 * This component demonstrates:
 * - All button variants (primary, secondary, tertiary, ghost, danger)
 * - All size options (sm, md, lg)
 * - Interactive states (hover, focus, active, loading, disabled)
 * - Norwegian localization examples
 * - Accessibility features
 * - Responsive behavior
 */
export const ButtonShowcase: React.FC = () => {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const handleLoadingDemo = (buttonId: string) => {
    setLoadingStates(prev => ({ ...prev, [buttonId]: true }));
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, [buttonId]: false }));
    }, 2000);
  };

  return (
    <div className="p-8 space-y-12 bg-jobblogg-neutral-secondary min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-jobblogg-text-strong mb-8">
          JobbLogg CTA Button Showcase
        </h1>

        {/* Button Variants */}
        <section className="space-y-8">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Button Variants
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Primary Button */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Primary CTA
              </h3>
              <p className="text-sm text-jobblogg-text-medium mb-4">
                Main action on page. JobbLogg primary blue (#2563EB).
              </p>
              <div className="space-y-3">
                <PrimaryButton onClick={() => handleLoadingDemo('primary')}>
                  Opprett prosjekt
                </PrimaryButton>
                <PrimaryButton 
                  loading={loadingStates.primary}
                  loadingText="Oppretter prosjekt..."
                >
                  Lagre endringer
                </PrimaryButton>
              </div>
            </div>

            {/* Secondary Button */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Secondary CTA
              </h3>
              <p className="text-sm text-jobblogg-text-medium mb-4">
                Supporting actions. JobbLogg success green (#10B981).
              </p>
              <div className="space-y-3">
                <SecondaryButton onClick={() => handleLoadingDemo('secondary')}>
                  Les mer
                </SecondaryButton>
                <SecondaryButton 
                  loading={loadingStates.secondary}
                  loadingText="Laster innhold..."
                >
                  Kontakt oss
                </SecondaryButton>
              </div>
            </div>

            {/* Tertiary Button */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Tertiary CTA
              </h3>
              <p className="text-sm text-jobblogg-text-medium mb-4">
                Less important actions. Outline style with primary blue.
              </p>
              <div className="space-y-3">
                <TertiaryButton>
                  Avbryt
                </TertiaryButton>
                <TertiaryButton>
                  Hopp over
                </TertiaryButton>
              </div>
            </div>

            {/* Ghost Button */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Ghost CTA
              </h3>
              <p className="text-sm text-jobblogg-text-medium mb-4">
                Minimal style for subtle actions. No background.
              </p>
              <div className="space-y-3">
                <GhostButton>
                  Vis detaljer
                </GhostButton>
                <GhostButton>
                  Rediger
                </GhostButton>
              </div>
            </div>

            {/* Danger Button */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Danger CTA
              </h3>
              <p className="text-sm text-jobblogg-text-medium mb-4">
                Destructive actions. JobbLogg error red (#DC2626).
              </p>
              <div className="space-y-3">
                <DangerButton onClick={() => handleLoadingDemo('danger')}>
                  Slett prosjekt
                </DangerButton>
                <DangerButton 
                  loading={loadingStates.danger}
                  loadingText="Sletter..."
                >
                  Fjern bruker
                </DangerButton>
              </div>
            </div>

            {/* Core CTAButton */}
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Core CTA Component
              </h3>
              <p className="text-sm text-jobblogg-text-medium mb-4">
                Base component with variant prop for custom implementations.
              </p>
              <div className="space-y-3">
                <CTAButton variant="primary">
                  Registrer deg
                </CTAButton>
                <CTAButton variant="secondary">
                  Logg inn
                </CTAButton>
              </div>
            </div>
          </div>
        </section>

        {/* Size Variants */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Size Variants
          </h2>
          
          <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              <div className="text-center">
                <h3 className="text-sm font-medium text-jobblogg-text-medium mb-3">
                  Small (44px min height)
                </h3>
                <PrimaryButton size="sm">
                  Lagre
                </PrimaryButton>
              </div>
              <div className="text-center">
                <h3 className="text-sm font-medium text-jobblogg-text-medium mb-3">
                  Medium (44px min height)
                </h3>
                <PrimaryButton size="md">
                  Opprett prosjekt
                </PrimaryButton>
              </div>
              <div className="text-center">
                <h3 className="text-sm font-medium text-jobblogg-text-medium mb-3">
                  Large (48px min height)
                </h3>
                <PrimaryButton size="lg">
                  Kom i gang
                </PrimaryButton>
              </div>
            </div>
          </div>
        </section>

        {/* Interactive States */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Interactive States
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Loading States
              </h3>
              <div className="space-y-3">
                <PrimaryButton 
                  loading={true}
                  loadingText="Oppretter prosjekt..."
                >
                  Opprett prosjekt
                </PrimaryButton>
                <SecondaryButton 
                  loading={true}
                  loadingText="Sender melding..."
                >
                  Send melding
                </SecondaryButton>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                Disabled States
              </h3>
              <div className="space-y-3">
                <PrimaryButton disabled>
                  Opprett prosjekt
                </PrimaryButton>
                <SecondaryButton disabled>
                  Send melding
                </SecondaryButton>
              </div>
            </div>
          </div>
        </section>

        {/* Full Width Examples */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Full Width Buttons
          </h2>
          
          <div className="bg-white p-6 rounded-xl border border-jobblogg-border max-w-md">
            <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
              Mobile-Optimized Layout
            </h3>
            <div className="space-y-3">
              <PrimaryButton fullWidth>
                Registrer deg gratis
              </PrimaryButton>
              <SecondaryButton fullWidth>
                Logg inn med eksisterende konto
              </SecondaryButton>
              <TertiaryButton fullWidth>
                Fortsett uten registrering
              </TertiaryButton>
            </div>
          </div>
        </section>

        {/* Norwegian Localization Examples */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Norwegian Localization Examples
          </h2>
          
          <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <PrimaryButton>Opprett prosjekt</PrimaryButton>
              <SecondaryButton>Les mer</SecondaryButton>
              <TertiaryButton>Avbryt</TertiaryButton>
              <GhostButton>Vis detaljer</GhostButton>
              <DangerButton>Slett</DangerButton>
              <PrimaryButton>Lagre endringer</PrimaryButton>
              <SecondaryButton>Kontakt oss</SecondaryButton>
              <TertiaryButton>Hopp over</TertiaryButton>
              <GhostButton>Rediger</GhostButton>
              <DangerButton>Fjern bruker</DangerButton>
              <PrimaryButton>Send melding</PrimaryButton>
              <SecondaryButton>Last ned</SecondaryButton>
            </div>
          </div>
        </section>

        {/* Design Guidelines */}
        <section className="space-y-6">
          <h2 className="text-2xl font-semibold text-jobblogg-text-strong">
            Design Guidelines
          </h2>
          
          <div className="bg-white p-6 rounded-xl border border-jobblogg-border">
            <div className="prose prose-sm max-w-none">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-4">
                2025 CTA Button Principles
              </h3>
              
              <ul className="space-y-2 text-jobblogg-text-medium">
                <li><strong>Typography:</strong> Inter font, SemiBold (600) for primary, Medium (500) for secondary</li>
                <li><strong>Touch Targets:</strong> Minimum 44px height (WCAG AA compliance)</li>
                <li><strong>Micro-Interactions:</strong> 200ms color transitions, 150ms transform animations</li>
                <li><strong>Accessibility:</strong> High contrast ratios, keyboard navigation, screen reader support</li>
                <li><strong>Responsive:</strong> Text scales from text-sm to text-base across breakpoints</li>
                <li><strong>Norwegian:</strong> Professional terminology suitable for construction industry</li>
                <li><strong>Hierarchy:</strong> Maximum 1 primary CTA per viewport, 2-3 secondary CTAs</li>
                <li><strong>Loading States:</strong> Norwegian loading text with spinner animation</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};
