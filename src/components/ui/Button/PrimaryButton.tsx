import React from 'react';

interface PrimaryButtonProps {
  /** Button content - clear, action-oriented Norwegian text */
  children: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state with Norwegian loading text */
  loading?: boolean;
  /** Loading text override (default: "Laster...") */
  loadingText?: string;
  /** Button variant following 2025 design principles */
  variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'danger';
  /** Button size with responsive scaling */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
  /** Icon to display before the text */
  icon?: React.ReactNode;
}

/**
 * Primary CTA button component following JobbLogg 2025 design principles
 *
 * Features:
 * - WCAG AA compliant with 44px minimum touch targets
 * - Responsive typography scaling (text-sm → text-base)
 * - Smooth micro-interactions with 200ms transitions
 * - Norwegian localization with professional terminology
 * - Hardware-accelerated animations for performance
 *
 * @example
 * ```tsx
 * <PrimaryButton onClick={() => handleCreateProject()}>
 *   Opprett prosjekt
 * </PrimaryButton>
 *
 * <PrimaryButton
 *   type="submit"
 *   loading={isSubmitting}
 *   loadingText="Oppretter prosjekt..."
 *   variant="secondary"
 * >
 *   Lagre endringer
 * </PrimaryButton>
 * ```
 */
export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  loading = false,
  loadingText = 'Laster...',
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  ariaLabel,
  icon,
}) => {
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(event);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled && !loading) {
      event.preventDefault();
      if (onClick) {
        onClick(event as any); // Type assertion for keyboard event
      }
    }
  };

  // Get variant styles following 2025 design principles
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return `
          bg-jobblogg-accent text-white border border-jobblogg-accent
          hover:bg-jobblogg-accent-light active:bg-jobblogg-accent-dark
          focus:ring-jobblogg-accent
        `;
      case 'tertiary':
        return `
          bg-transparent text-jobblogg-primary border-2 border-jobblogg-primary
          hover:bg-jobblogg-primary hover:text-white active:bg-jobblogg-primary-dark
          focus:ring-jobblogg-primary
        `;
      case 'ghost':
        return `
          bg-transparent text-jobblogg-text-medium border-none
          hover:text-jobblogg-text-strong hover:bg-jobblogg-neutral
          focus:ring-jobblogg-primary shadow-none hover:shadow-soft
        `;
      case 'danger':
        return `
          bg-jobblogg-error-soft text-jobblogg-error border border-jobblogg-error-soft
          hover:bg-jobblogg-error hover:text-white active:bg-jobblogg-error-dark
          focus:ring-jobblogg-error
        `;
      default: // primary
        return `
          bg-jobblogg-primary text-white border border-jobblogg-primary
          hover:bg-jobblogg-primary-light active:bg-jobblogg-primary-dark
          focus:ring-jobblogg-primary
        `;
    }
  };

  // Get size styles with WCAG AA compliant touch targets and responsive typography
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return `
          px-4 py-2.5 text-sm sm:text-sm min-h-[44px]
          font-medium
        `;
      case 'lg':
        return `
          px-6 py-3.5 text-base sm:text-lg min-h-[48px]
          lg:px-8 lg:py-4 font-semibold
        `;
      default: // md
        return `
          px-4 py-2.5 sm:px-5 sm:py-3 lg:px-6 lg:py-3.5
          text-sm sm:text-base min-h-[44px] font-semibold
        `;
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || loading}
      className={`
        inline-flex items-center justify-center gap-2
        rounded-xl transition-all duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        active:scale-[0.98] active:transition-transform active:duration-150
        shadow-soft hover:shadow-medium transition-shadow
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${fullWidth ? 'w-full' : ''}
        ${loading ? 'cursor-wait pointer-events-none' : 'cursor-pointer'}
        ${disabled ? 'pointer-events-none' : ''}
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      aria-disabled={disabled || loading}
      aria-label={ariaLabel || (loading ? loadingText : undefined)}
    >
      {loading ? (
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>{loadingText}</span>
        </div>
      ) : (
        <div className="flex items-center gap-2">
          {icon && <span className="flex-shrink-0">{icon}</span>}
          <span>{children}</span>
        </div>
      )}
    </button>
  );
};

export default PrimaryButton;
