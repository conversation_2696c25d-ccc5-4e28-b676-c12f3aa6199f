import React from 'react';
import { CTAButton } from './CTAButton';

interface TertiaryButtonProps {
  /** Button content - clear, action-oriented Norwegian text */
  children: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state with Norwegian loading text */
  loading?: boolean;
  /** Loading text override (default: "Laster...") */
  loadingText?: string;
  /** Button size with responsive scaling */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Tertiary CTA button component following JobbLogg 2025 design principles
 * Uses outline style with JobbLogg primary blue for less important actions
 * 
 * Perfect for:
 * - Cancel actions: "Avbryt"
 * - Skip actions: "Hopp over"
 * - Alternative paths: "Tilbake"
 * 
 * @example
 * ```tsx
 * <TertiaryButton onClick={handleCancel}>
 *   Avbryt
 * </TertiaryButton>
 * 
 * <TertiaryButton onClick={handleSkip} size="sm">
 *   Hopp over
 * </TertiaryButton>
 * ```
 */
export const TertiaryButton: React.FC<TertiaryButtonProps> = (props) => {
  return <CTAButton {...props} variant="tertiary" />;
};

export default TertiaryButton;
