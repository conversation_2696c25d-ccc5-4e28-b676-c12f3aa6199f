import React from 'react';
import { CTAButton } from './CTAButton';

interface GhostButtonProps {
  /** Button content - clear, action-oriented Norwegian text */
  children: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Loading state with Norwegian loading text */
  loading?: boolean;
  /** Loading text override (default: "Laster...") */
  loadingText?: string;
  /** Button size with responsive scaling */
  size?: 'sm' | 'md' | 'lg';
  /** Full width button */
  fullWidth?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Ghost CTA button component following JobbLogg 2025 design principles
 * Minimal style with no background, perfect for subtle actions
 * 
 * Perfect for:
 * - Navigation links: "Vis detaljer"
 * - Subtle actions: "Rediger"
 * - Menu items: "Innstillinger"
 * - Footer links: "Personvern"
 * 
 * Features:
 * - Transparent background with hover state
 * - Subtle shadow on hover for depth
 * - Maintains accessibility with proper focus states
 * 
 * @example
 * ```tsx
 * <GhostButton onClick={handleViewDetails}>
 *   Vis detaljer
 * </GhostButton>
 * 
 * <GhostButton onClick={handleEdit} size="sm">
 *   Rediger
 * </GhostButton>
 * ```
 */
export const GhostButton: React.FC<GhostButtonProps> = (props) => {
  return <CTAButton {...props} variant="ghost" />;
};

export default GhostButton;
