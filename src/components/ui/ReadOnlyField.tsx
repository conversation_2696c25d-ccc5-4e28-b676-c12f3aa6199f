import React from 'react';

interface ReadOnlyFieldProps {
  label: string;
  value: string;
  placeholder?: string;
  className?: string;
  rows?: number;
}

/**
 * Read-only field component for displaying non-editable content
 * Used for subcontractors who have view-only access to job data
 */
export const ReadOnlyField: React.FC<ReadOnlyFieldProps> = ({
  label,
  value,
  placeholder = '',
  className = '',
  rows = 3
}) => {
  const displayValue = value || placeholder;
  const isEmpty = !value;

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-jobblogg-text-strong">
        {label}
      </label>
      
      <div 
        className={`
          w-full px-3 py-2 
          bg-jobblogg-neutral/30 
          border border-jobblogg-border/50 
          rounded-lg 
          text-sm 
          ${isEmpty ? 'text-jobblogg-text-muted italic' : 'text-jobblogg-text-medium'}
          ${rows > 1 ? 'min-h-[80px]' : 'min-h-[44px]'}
          whitespace-pre-wrap
          break-words
        `}
        style={{ 
          minHeight: rows > 1 ? `${Math.max(rows * 1.5, 3)}rem` : '44px'
        }}
        role="textbox"
        aria-readonly="true"
        aria-label={`${label} (kun lesing)`}
      >
        {displayValue}
      </div>
      
      {isEmpty && (
        <div className="flex items-center gap-1 text-xs text-jobblogg-text-muted">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Ingen informasjon tilgjengelig</span>
        </div>
      )}
    </div>
  );
};
