import React from 'react';
import { useUserRole } from '../../../hooks/useUserRole';
import { PageLayout } from '../Layout/PageLayout';
import { Heading2, BodyText, TextMuted } from '../Typography';

interface AdministratorOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showPageLayout?: boolean;
  pageTitle?: string;
  backUrl?: string;
}

export const AdministratorOnly: React.FC<AdministratorOnlyProps> = ({
  children,
  fallback,
  showPageLayout = false,
  pageTitle = "Ingen tilgang",
  backUrl = "/"
}) => {
  const { isLoading, isAdministrator } = useUserRole();

  // Show loading state while checking permissions
  if (isLoading) {
    const loadingContent = (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
          <TextMuted>Sjekker tilganger...</TextMuted>
        </div>
      </div>
    );

    if (showPageLayout) {
      return (
        <PageLayout 
          title={pageTitle} 
          showBackButton 
          backUrl={backUrl} 
          containerWidth="medium" 
          showFooter={false} 
          className="bg-jobblogg-surface"
        >
          <div className="bg-white rounded-xl shadow-soft p-8">
            <div className="max-w-2xl mx-auto">
              {loadingContent}
            </div>
          </div>
        </PageLayout>
      );
    }

    return loadingContent;
  }

  // Allow access for administrators
  if (isAdministrator) {
    return <>{children}</>;
  }

  // Show fallback or default unauthorized message
  if (fallback) {
    return <>{fallback}</>;
  }

  const unauthorizedContent = (
    <div className="text-center py-16">
      <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-6">
        <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <Heading2 className="mb-4">Kun for administratorer</Heading2>
      <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
        Denne siden er kun tilgjengelig for brukere med administratorrettigheter. 
        Kontakt din administrator hvis du trenger tilgang.
      </BodyText>
      <BodyText className="text-sm text-jobblogg-text-muted">
        Du er logget inn som utførende bruker
      </BodyText>
    </div>
  );

  if (showPageLayout) {
    return (
      <PageLayout 
        title={pageTitle} 
        showBackButton 
        backUrl={backUrl} 
        containerWidth="medium" 
        showFooter={false} 
        className="bg-jobblogg-surface"
      >
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            {unauthorizedContent}
          </div>
        </div>
      </PageLayout>
    );
  }

  return unauthorizedContent;
};
