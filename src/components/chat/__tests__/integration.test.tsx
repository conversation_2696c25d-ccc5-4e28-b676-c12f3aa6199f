import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ChatContainer } from '../ChatContainer';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';

// Mock Convex client
const mockConvex = new ConvexReactClient('https://test.convex.cloud');

// Mock data
const mockMessages = [
  {
    _id: 'msg1' as any,
    logId: 'log1' as any,
    senderId: 'user1',
    senderRole: 'contractor' as const,
    senderDisplayName: 'Leverandør',
    text: 'Hei! Prosjektet er startet.',
    createdAt: Date.now() - 2000,
    isOwnMessage: true,
    replies: []
  },
  {
    _id: 'msg2' as any,
    logId: 'log1' as any,
    senderId: 'user2',
    senderRole: 'customer' as const,
    senderDisplayName: 'Kunde',
    text: 'Takk for oppdateringen!',
    createdAt: Date.now() - 1000,
    isOwnMessage: false,
    replies: []
  }
];

// Mock Convex hooks
const mockSendMessage = vi.fn();
const mockAddReaction = vi.fn();
const mockEditMessage = vi.fn();
const mockDeleteMessage = vi.fn();

vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useQuery: vi.fn(() => ({ messages: mockMessages })),
    useMutation: vi.fn((mutationName) => {
      switch (mutationName) {
        case 'chat:sendMessage':
          return mockSendMessage;
        case 'chat:addReaction':
          return mockAddReaction;
        case 'chat:editMessage':
          return mockEditMessage;
        case 'chat:deleteMessage':
          return mockDeleteMessage;
        default:
          return vi.fn();
      }
    }),
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConvexProvider client={mockConvex}>
    {children}
  </ConvexProvider>
);

describe('Chat Integration Tests', () => {
  const defaultProps = {
    logId: 'log1' as any,
    userId: 'user1',
    userRole: 'contractor' as const
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Message Sending Flow', () => {
    it('sends a message successfully', async () => {
      const user = userEvent.setup();
      mockSendMessage.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      const textarea = screen.getByLabelText('Skriv melding');
      const sendButton = screen.getByText('Send');

      await user.type(textarea, 'Ny testmelding');
      await user.click(sendButton);

      expect(mockSendMessage).toHaveBeenCalledWith({
        logId: 'log1',
        text: 'Ny testmelding',
        files: [],
        parentId: undefined
      });

      await waitFor(() => {
        expect(textarea).toHaveValue('');
      });
    });

    it('handles message sending errors', async () => {
      const user = userEvent.setup();
      mockSendMessage.mockRejectedValue(new Error('Network error'));

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      const textarea = screen.getByLabelText('Skriv melding');
      const sendButton = screen.getByText('Send');

      await user.type(textarea, 'Test message');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });
    });

    it('sends message with Enter key', async () => {
      const user = userEvent.setup();
      mockSendMessage.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      const textarea = screen.getByLabelText('Skriv melding');

      await user.type(textarea, 'Test message');
      await user.keyboard('{Enter}');

      expect(mockSendMessage).toHaveBeenCalledWith({
        logId: 'log1',
        text: 'Test message',
        files: [],
        parentId: undefined
      });
    });
  });

  describe('Message Interaction Flow', () => {
    it('replies to a message', async () => {
      const user = userEvent.setup();
      mockSendMessage.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      // Find and hover over a message to show actions
      const messageElements = screen.getAllByRole('article');
      const firstMessage = messageElements[0];
      
      fireEvent.mouseEnter(firstMessage);

      // Click reply button
      const replyButton = screen.getByLabelText('Svar på melding');
      await user.click(replyButton);

      // Verify reply context is shown
      expect(screen.getByText('Svarer på Leverandør')).toBeInTheDocument();

      // Send reply
      const textarea = screen.getByLabelText('Skriv melding');
      await user.type(textarea, 'Dette er et svar');
      await user.keyboard('{Enter}');

      expect(mockSendMessage).toHaveBeenCalledWith({
        logId: 'log1',
        text: 'Dette er et svar',
        files: [],
        parentId: 'msg1'
      });
    });

    it('adds reaction to a message', async () => {
      const user = userEvent.setup();
      mockAddReaction.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      // Find and hover over a message to show actions
      const messageElements = screen.getAllByRole('article');
      const firstMessage = messageElements[0];
      
      fireEvent.mouseEnter(firstMessage);

      // Click reaction button
      const reactionButton = screen.getByLabelText('Legg til tommel opp reaksjon');
      await user.click(reactionButton);

      expect(mockAddReaction).toHaveBeenCalledWith({
        messageId: 'msg1',
        emoji: '👍'
      });
    });

    it('edits own message', async () => {
      const user = userEvent.setup();
      mockEditMessage.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      // Find and hover over own message to show actions
      const messageElements = screen.getAllByRole('article');
      const ownMessage = messageElements[0]; // First message is own message
      
      fireEvent.mouseEnter(ownMessage);

      // Click edit button
      const editButton = screen.getByLabelText('Rediger melding');
      await user.click(editButton);

      expect(mockEditMessage).toHaveBeenCalledWith({
        messageId: 'msg1'
      });
    });

    it('deletes own message', async () => {
      const user = userEvent.setup();
      mockDeleteMessage.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      // Find and hover over own message to show actions
      const messageElements = screen.getAllByRole('article');
      const ownMessage = messageElements[0]; // First message is own message
      
      fireEvent.mouseEnter(ownMessage);

      // Click delete button
      const deleteButton = screen.getByLabelText('Slett melding');
      await user.click(deleteButton);

      expect(mockDeleteMessage).toHaveBeenCalledWith({
        messageId: 'msg1'
      });
    });
  });

  describe('File Upload Flow', () => {
    it('uploads and sends file', async () => {
      const user = userEvent.setup();
      mockSendMessage.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      const fileInput = screen.getByLabelText('Last opp fil');
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });

      await user.upload(fileInput, file);

      // Verify file is shown in preview
      expect(screen.getByText('test.txt')).toBeInTheDocument();

      // Send message with file
      const sendButton = screen.getByText('Send');
      await user.click(sendButton);

      expect(mockSendMessage).toHaveBeenCalledWith({
        logId: 'log1',
        text: '',
        files: [file],
        parentId: undefined
      });
    });

    it('removes uploaded file', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      const fileInput = screen.getByLabelText('Last opp fil');
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });

      await user.upload(fileInput, file);
      expect(screen.getByText('test.txt')).toBeInTheDocument();

      // Remove file
      const removeButton = screen.getByLabelText('Fjern fil');
      await user.click(removeButton);

      expect(screen.queryByText('test.txt')).not.toBeInTheDocument();
    });
  });

  describe('Keyboard Navigation Flow', () => {
    it('navigates with keyboard shortcuts', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      const textarea = screen.getByLabelText('Skriv melding');

      // Focus textarea
      await user.click(textarea);
      expect(textarea).toHaveFocus();

      // Tab to send button
      await user.keyboard('{Tab}');
      expect(screen.getByText('Send')).toHaveFocus();

      // Tab to file upload
      await user.keyboard('{Tab}');
      expect(screen.getByLabelText('Last opp fil')).toHaveFocus();
    });

    it('cancels reply with Escape', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      // Start a reply
      const messageElements = screen.getAllByRole('article');
      const firstMessage = messageElements[0];
      
      fireEvent.mouseEnter(firstMessage);
      const replyButton = screen.getByLabelText('Svar på melding');
      await user.click(replyButton);

      expect(screen.getByText('Svarer på Leverandør')).toBeInTheDocument();

      // Cancel with Escape
      await user.keyboard('{Escape}');

      expect(screen.queryByText('Svarer på Leverandør')).not.toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('displays typing indicators', () => {
      // Mock typing users - commented out for now
      // const _typingUsers = [
      //   { userId: 'user2', displayName: 'Kunde' }
      // ];

      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      // In a real implementation, this would be tested with WebSocket mocks
      // For now, we verify the component structure exists
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('shows connection status', () => {
      render(
        <TestWrapper>
          <ChatContainer {...defaultProps} />
        </TestWrapper>
      );

      expect(screen.getByRole('status')).toBeInTheDocument();
    });
  });
});
