import { expect, afterEach, vi } from 'vitest';
import { cleanup, render } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url');
global.URL.revokeObjectURL = vi.fn();

// Mock File and FileReader
global.File = class MockFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;

  constructor(bits: any[], filename: string, options: any = {}) {
    this.name = filename;
    this.size = bits.reduce((acc, bit) => acc + (bit.length || 0), 0);
    this.type = options.type || '';
    this.lastModified = Date.now();
  }
} as any;

global.FileReader = class MockFileReader {
  result: string | ArrayBuffer | null = null;
  error: any = null;
  readyState: number = 0;
  onload: ((event: any) => void) | null = null;
  onerror: ((event: any) => void) | null = null;
  onloadend: ((event: any) => void) | null = null;

  readAsDataURL(file: File) {
    this.readyState = 2;
    this.result = `data:${file.type};base64,mock-base64-data`;
    if (this.onload) {
      this.onload({ target: this });
    }
    if (this.onloadend) {
      this.onloadend({ target: this });
    }
  }

  readAsText(_file: File) {
    this.readyState = 2;
    this.result = 'mock file content';
    if (this.onload) {
      this.onload({ target: this });
    }
    if (this.onloadend) {
      this.onloadend({ target: this });
    }
  }
} as any;

// Mock scrollIntoView
Element.prototype.scrollIntoView = vi.fn();

// Mock focus method
HTMLElement.prototype.focus = vi.fn();

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = vi.fn(() => ({
  bottom: 0,
  height: 0,
  left: 0,
  right: 0,
  top: 0,
  width: 0,
  x: 0,
  y: 0,
  toJSON: vi.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock as any;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.sessionStorage = sessionStorageMock as any;

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock performance.now
global.performance = {
  ...performance,
  now: vi.fn(() => Date.now()),
};

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id));

// Mock WebSocket for real-time features
global.WebSocket = vi.fn().mockImplementation(() => ({
  close: vi.fn(),
  send: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: 1, // OPEN
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
})) as any;

// Helper function to create mock messages
export const createMockMessage = (overrides: any = {}) => ({
  _id: `msg-${Date.now()}` as any,
  logId: 'log1' as any,
  senderId: 'user1',
  senderRole: 'contractor' as const,
  senderDisplayName: 'Test User',
  text: 'Test message',
  createdAt: Date.now(),
  isOwnMessage: true,
  replies: [],
  ...overrides,
});

// Helper function to create mock files
export const createMockFile = (name = 'test.txt', type = 'text/plain', content = 'test content') => {
  return new File([content], name, { type });
};

// Helper function to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

// Custom render function with providers
export const renderWithProviders = (ui: React.ReactElement, options: any = {}) => {
  // Use dynamic imports for test environment
  const ConvexProvider = (global as any).ConvexProvider;
  const ConvexReactClient = (global as any).ConvexReactClient;
  
  const mockConvex = new ConvexReactClient('https://test.convex.cloud');
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <ConvexProvider client={mockConvex}>
      {children}
    </ConvexProvider>
  );

  return render(ui, { wrapper: Wrapper, ...options });
};

// Accessibility testing helpers
export const checkAccessibility = async (container: HTMLElement) => {
  const { axe } = await import('jest-axe' as any);
  const results = await axe(container);
  return results;
};

// Custom matchers for chat-specific testing
expect.extend({
  toHaveProperChatStructure(received) {
    const hasMainRole = received.querySelector('[role="main"]');
    const hasLogRole = received.querySelector('[role="log"]');
    const hasStatusRole = received.querySelector('[role="status"]');
    
    const pass = hasMainRole && hasLogRole && hasStatusRole;
    
    if (pass) {
      return {
        message: () => `expected element not to have proper chat structure`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected element to have proper chat structure with main, log, and status roles`,
        pass: false,
      };
    }
  },
  
  toHaveAccessibleForm(received) {
    const hasLabels = received.querySelectorAll('label').length > 0;
    const hasAriaLabels = received.querySelectorAll('[aria-label]').length > 0;
    const hasProperInputs = received.querySelectorAll('input, textarea, button').length > 0;
    
    const pass = (hasLabels || hasAriaLabels) && hasProperInputs;
    
    if (pass) {
      return {
        message: () => `expected element not to have accessible form`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected element to have accessible form with proper labels and inputs`,
        pass: false,
      };
    }
  }
});

// Declare custom matchers for TypeScript
declare global {
  interface CustomMatchers<R = unknown> {
    toHaveProperChatStructure(): R;
    toHaveAccessibleForm(): R;
  }
}
