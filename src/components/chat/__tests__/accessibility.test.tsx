import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ChatContainer } from '../ChatContainer';
import { MessageInput } from '../MessageInput';
import { MessageItem } from '../MessageItem';
import { ScreenReaderAnnouncer } from '../ScreenReaderAnnouncer';
import { ConvexProvider } from 'convex/react';
import { ConvexReactClient } from 'convex/react';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock Convex client
const mockConvex = new ConvexReactClient('https://test.convex.cloud');

// Mock Convex hooks
vi.mock('convex/react', async () => {
  const actual = await vi.importActual('convex/react');
  return {
    ...actual,
    useQuery: vi.fn(() => ({ messages: [] })),
    useMutation: vi.fn(() => vi.fn()),
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConvexProvider client={mockConvex}>
    {children}
  </ConvexProvider>
);

describe('Chat Accessibility Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ChatContainer Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <ChatContainer
            logId={"log1" as any}
            userId="user1"
            userRole="contractor"
          />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper ARIA landmarks', () => {
      render(
        <TestWrapper>
          <ChatContainer
            logId={"log1" as any}
            userId="user1"
            userRole="contractor"
          />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('log')).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument();
    });

    it('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <ChatContainer
            logId={"log1" as any}
            userId="user1"
            userRole="contractor"
          />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Chat-samtale')).toBeInTheDocument();
      expect(screen.getByLabelText('Meldinger i samtalen')).toBeInTheDocument();
    });
  });

  describe('MessageInput Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <MessageInput
          logId={"log1" as any}
          userId="user1"
          userRole="contractor"
          onSend={vi.fn()}
          placeholder="Test placeholder"
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper form labels', () => {
      render(
        <MessageInput
          logId={"log1" as any}
          userId="user1"
          userRole="contractor"
          onSend={vi.fn()}
          placeholder="Test placeholder"
        />
      );

      expect(screen.getByLabelText('Skriv melding')).toBeInTheDocument();
      expect(screen.getByLabelText('Last opp fil')).toBeInTheDocument();
    });

    it('has proper button accessibility', () => {
      render(
        <MessageInput
          logId={"log1" as any}
          userId="user1"
          userRole="contractor"
          onSend={vi.fn()}
          placeholder="Test placeholder"
        />
      );

      const sendButton = screen.getByText('Send');
      expect(sendButton).toHaveAttribute('type', 'button');
      expect(sendButton).toHaveAttribute('aria-label', 'Send melding');
    });

    it('shows proper loading state accessibility', () => {
      render(
        <MessageInput
          logId={"log1" as any}
          userId="user1"
          userRole="contractor"
          onSend={vi.fn()}
          placeholder="Test placeholder"
          disabled={true}
        />
      );

      const textarea = screen.getByLabelText('Skriv melding');
      const sendButton = screen.getByText('Send');

      expect(textarea).toBeDisabled();
      expect(sendButton).toBeDisabled();
    });
  });

  describe('MessageItem Accessibility', () => {
    const mockMessage = {
      _id: 'msg1' as any,
      _creationTime: Date.now(),
      logId: 'log1' as any,
      senderId: 'user1',
      senderRole: 'contractor' as const,
      senderDisplayName: 'Test User',
      text: 'Test message',
      createdAt: Date.now(),
      isOwnMessage: true,
      replies: []
    };

    it('should not have accessibility violations', async () => {
      const { container } = render(
        <MessageItem
          message={mockMessage}
          userId="user1"
          onReply={vi.fn()}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onReaction={vi.fn()}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper article structure', () => {
      render(
        <MessageItem
          message={mockMessage}
          userId="user1"
          onReply={vi.fn()}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onReaction={vi.fn()}
        />
      );

      expect(screen.getByRole('article')).toBeInTheDocument();
      expect(screen.getByLabelText('Melding fra Test User')).toBeInTheDocument();
    });

    it('has proper time element', () => {
      render(
        <MessageItem
          message={mockMessage}
          userId="user1"
          onReply={vi.fn()}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onReaction={vi.fn()}
        />
      );

      const timeElement = screen.getByRole('time');
      expect(timeElement).toBeInTheDocument();
      expect(timeElement).toHaveAttribute('dateTime');
    });

    it('has accessible action buttons', () => {
      render(
        <MessageItem
          message={mockMessage}
          userId="user1"
          onReply={vi.fn()}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onReaction={vi.fn()}
        />
      );

      // Hover to show actions
      const messageElement = screen.getByRole('article');
      messageElement.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));

      expect(screen.getByLabelText('Svar på melding')).toBeInTheDocument();
      expect(screen.getByLabelText('Legg til tommel opp reaksjon')).toBeInTheDocument();
      expect(screen.getByLabelText('Rediger melding')).toBeInTheDocument();
      expect(screen.getByLabelText('Slett melding')).toBeInTheDocument();
    });

    it('has proper toolbar role for actions', () => {
      render(
        <MessageItem
          message={mockMessage}
          userId="user1"
          onReply={vi.fn()}
          onEdit={vi.fn()}
          onDelete={vi.fn()}
          onReaction={vi.fn()}
        />
      );

      // Hover to show actions
      const messageElement = screen.getByRole('article');
      messageElement.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));

      expect(screen.getByRole('toolbar')).toBeInTheDocument();
      expect(screen.getByLabelText('Meldingshandlinger')).toBeInTheDocument();
    });
  });

  describe('ScreenReaderAnnouncer Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <ScreenReaderAnnouncer
          message="Test announcement"
          priority="polite"
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper live region attributes', () => {
      render(
        <ScreenReaderAnnouncer
          message="Test announcement"
          priority="assertive"
        />
      );

      const liveRegion = screen.getByRole('status');
      expect(liveRegion).toHaveAttribute('aria-live', 'assertive');
      expect(liveRegion).toHaveAttribute('aria-atomic', 'true');
      expect(liveRegion).toHaveClass('sr-only');
    });
  });

  describe('Color Contrast', () => {
    it('meets WCAG AA contrast requirements', () => {
      // This would typically be tested with automated tools
      // For now, we verify that proper color classes are used
      render(
        <TestWrapper>
          <ChatContainer
            logId={"log1" as any}
            userId="user1"
            userRole="contractor"
          />
        </TestWrapper>
      );

      // Verify that text uses proper contrast classes
      const mainElement = screen.getByRole('main');
      expect(mainElement).toBeInTheDocument();
      
      // In a real implementation, you would use tools like:
      // - axe-core color contrast rules
      // - Custom contrast checking utilities
      // - Visual regression testing
    });
  });

  describe('Keyboard Navigation', () => {
    it('supports proper tab order', () => {
      render(
        <MessageInput
          logId={"log1" as any}
          userId="user1"
          userRole="contractor"
          onSend={vi.fn()}
          placeholder="Test placeholder"
        />
      );

      const textarea = screen.getByLabelText('Skriv melding');
      const fileButton = screen.getByLabelText('Last opp fil');
      const sendButton = screen.getByText('Send');

      // All interactive elements should be focusable
      expect(textarea).toHaveAttribute('tabIndex', '0');
      expect(fileButton).not.toHaveAttribute('tabIndex', '-1');
      expect(sendButton).not.toHaveAttribute('tabIndex', '-1');
    });

    it('has visible focus indicators', () => {
      render(
        <MessageInput
          logId={"log1" as any}
          userId="user1"
          userRole="contractor"
          onSend={vi.fn()}
          placeholder="Test placeholder"
        />
      );

      const textarea = screen.getByLabelText('Skriv melding');
      
      // Focus indicators should be present in CSS classes
      expect(textarea).toHaveClass('focus:ring-2');
      expect(textarea).toHaveClass('focus:ring-jobblogg-primary');
    });
  });
});
