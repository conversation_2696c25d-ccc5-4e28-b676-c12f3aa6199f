import React, { useState } from 'react';
// import { extractDomain } from '../../utils/linkDetection'; // TODO: Use for domain extraction

export interface LinkPreviewData {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  type?: string;
  domain: string;
  favicon?: string;
  fetchError?: string;
}

interface LinkPreviewProps {
  data: LinkPreviewData;
  className?: string;
}

export const LinkPreview: React.FC<LinkPreviewProps> = ({ data, className = '' }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleLinkClick = () => {
    window.open(data.url, '_blank', 'noopener,noreferrer');
  };

  // If there was a fetch error, show minimal preview
  if (data.fetchError) {
    return (
      <div className={`jobblogg-link-preview-minimal ${className}`}>
        <a
          href={data.url}
          target="_blank"
          rel="noopener noreferrer"
          className="group flex items-center space-x-3 p-3 bg-white hover:bg-jobblogg-neutral-secondary border border-jobblogg-border rounded-lg transition-all duration-200 max-w-md hover:shadow-sm"
        >
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-jobblogg-text-muted group-hover:text-jobblogg-primary transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-jobblogg-text-strong truncate group-hover:text-jobblogg-primary transition-colors">
              {data.domain}
            </p>
            <p className="text-xs text-jobblogg-text-muted truncate">
              {data.url}
            </p>
          </div>
        </a>
      </div>
    );
  }

  const hasImage = data.image && !imageError;
  const displayTitle = data.title || data.siteName || data.domain;
  const displayDescription = data.description;

  return (
    <div className={`jobblogg-link-preview ${className}`}>
      <div
        onClick={handleLinkClick}
        className="group cursor-pointer bg-white hover:bg-jobblogg-neutral-secondary border border-jobblogg-border rounded-lg overflow-hidden transition-all duration-200 max-w-md hover:shadow-sm"
      >
        {/* Image section */}
        {hasImage && (
          <div className="relative aspect-video bg-jobblogg-neutral-secondary">
            {imageLoading && (
              <div className="absolute inset-0 bg-jobblogg-neutral-secondary animate-pulse flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}
            <img
              src={data.image}
              alt={displayTitle}
              className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              loading="lazy"
            />
          </div>
        )}

        {/* Content section */}
        <div className="p-4">
          {/* Site info header */}
          <div className="flex items-center space-x-2 mb-2">
            {data.favicon && (
              <img
                src={data.favicon}
                alt=""
                className="w-4 h-4 rounded-sm"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                }}
              />
            )}
            <span className="text-xs text-jobblogg-text-muted font-medium">
              {data.siteName || data.domain}
            </span>
          </div>

          {/* Title */}
          {displayTitle && (
            <h3 className="text-sm font-semibold text-jobblogg-text-strong group-hover:text-jobblogg-primary transition-colors line-clamp-2 mb-1">
              {displayTitle}
            </h3>
          )}

          {/* Description */}
          {displayDescription && (
            <p className="text-xs text-jobblogg-text-medium line-clamp-2 mb-2">
              {displayDescription}
            </p>
          )}

          {/* URL */}
          <div className="flex items-center space-x-1 text-xs text-jobblogg-text-muted">
            <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            <span className="truncate">{data.domain}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Loading skeleton component for link previews
export const LinkPreviewSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`jobblogg-link-preview-skeleton ${className}`}>
      <div className="bg-white border border-jobblogg-border rounded-lg overflow-hidden max-w-md">
        {/* Image skeleton */}
        <div className="aspect-video bg-jobblogg-neutral-secondary animate-pulse" />
        
        {/* Content skeleton */}
        <div className="p-4 space-y-2">
          {/* Site info skeleton */}
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-jobblogg-neutral-secondary rounded-sm animate-pulse" />
            <div className="w-20 h-3 bg-jobblogg-neutral-secondary rounded animate-pulse" />
          </div>
          
          {/* Title skeleton */}
          <div className="space-y-1">
            <div className="w-full h-4 bg-jobblogg-neutral-secondary rounded animate-pulse" />
            <div className="w-3/4 h-4 bg-jobblogg-neutral-secondary rounded animate-pulse" />
          </div>
          
          {/* Description skeleton */}
          <div className="space-y-1">
            <div className="w-full h-3 bg-jobblogg-neutral-secondary rounded animate-pulse" />
            <div className="w-2/3 h-3 bg-jobblogg-neutral-secondary rounded animate-pulse" />
          </div>
          
          {/* URL skeleton */}
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-jobblogg-neutral-secondary rounded animate-pulse" />
            <div className="w-24 h-3 bg-jobblogg-neutral-secondary rounded animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  );
};

// Compact link preview for inline display
export const CompactLinkPreview: React.FC<LinkPreviewProps> = ({ data, className = '' }) => {
  const handleLinkClick = () => {
    window.open(data.url, '_blank', 'noopener,noreferrer');
  };

  const displayTitle = data.title || data.siteName || data.domain;

  return (
    <div className={`jobblogg-compact-link-preview ${className}`}>
      <div
        onClick={handleLinkClick}
        className="group cursor-pointer flex items-center space-x-3 p-2 bg-white hover:bg-jobblogg-neutral-secondary border border-jobblogg-border rounded-md transition-all duration-200 max-w-sm hover:shadow-sm"
      >
        {/* Favicon or default icon */}
        <div className="flex-shrink-0">
          {data.favicon ? (
            <img
              src={data.favicon}
              alt=""
              className="w-4 h-4 rounded-sm"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <svg className={`w-4 h-4 text-jobblogg-text-muted group-hover:text-jobblogg-primary transition-colors ${data.favicon ? 'hidden' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-jobblogg-text-strong truncate group-hover:text-jobblogg-primary transition-colors">
            {displayTitle}
          </p>
          <p className="text-xs text-jobblogg-text-muted truncate">
            {data.domain}
          </p>
        </div>

        {/* External link icon */}
        <div className="flex-shrink-0 opacity-60 group-hover:opacity-100 transition-opacity">
          <svg className="w-3 h-3 text-jobblogg-text-muted group-hover:text-jobblogg-primary transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
      </div>
    </div>
  );
};
