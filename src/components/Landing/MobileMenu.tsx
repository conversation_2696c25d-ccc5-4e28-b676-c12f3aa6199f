import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { PrimaryButton, SecondaryButton } from '../ui';
import { Navigation } from './Navigation';

interface MobileMenuProps {
  /** Whether the mobile menu is open */
  isOpen: boolean;
  /** Callback to close the menu */
  onClose: () => void;
}

export const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Handle backdrop click
  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 z-50 bg-black/20 backdrop-blur-sm transition-all duration-300 md:hidden ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={handleBackdropClick}
        aria-hidden="true"
      />

      {/* Mobile Menu Panel */}
      <div
        className={`fixed top-0 right-0 bottom-0 z-50 w-full max-w-sm bg-jobblogg-neutral-secondary shadow-xl transform transition-all duration-300 ease-in-out md:hidden ${
          isOpen ? 'translate-x-0 scale-100' : 'translate-x-full scale-95'
        }`}
        role="dialog"
        aria-modal="true"
        aria-label="Navigasjonsmeny"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-jobblogg-border">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-jobblogg-primary to-jobblogg-primary-dark rounded-lg flex items-center justify-center shadow-sm">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <span className="text-lg font-bold text-jobblogg-text-strong">JobbLogg</span>
            </div>
            
            <button
              onClick={onClose}
              className="w-10 h-10 rounded-lg bg-jobblogg-neutral hover:bg-jobblogg-error-soft text-jobblogg-text-medium hover:text-jobblogg-error transition-all duration-200 flex items-center justify-center focus-ring"
              aria-label="Lukk meny"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Navigation Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <Navigation isMobile onItemClick={onClose} />
          </div>

          {/* Bottom Action Buttons - Following 2025 Design Principles */}
          <div className="p-4 border-t border-jobblogg-border bg-white space-y-3">
            <Link to="/sign-in" onClick={onClose} className="block">
              <SecondaryButton
                fullWidth
                ariaLabel="Logg inn på eksisterende konto"
              >
                Logg inn
              </SecondaryButton>
            </Link>

            <Link to="/sign-up" onClick={onClose} className="block">
              <PrimaryButton
                fullWidth
                ariaLabel="Registrer deg gratis for JobbLogg"
              >
                Prøv gratis
              </PrimaryButton>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
