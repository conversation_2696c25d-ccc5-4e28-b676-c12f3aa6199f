import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { PrimaryButton, SecondaryButton, JobbLoggLogo } from '../ui';
import { Navigation } from './Navigation';
import { MobileMenu } from './MobileMenu';

export const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Auto-hide header on desktop scroll
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const isDesktop = window.innerWidth >= 768; // md breakpoint

      if (!isDesktop) {
        // Always show header on mobile
        setIsHeaderVisible(true);
        return;
      }

      // Show header when scrolling up or at top
      if (currentScrollY < lastScrollY || currentScrollY < 100) {
        setIsHeaderVisible(true);
      }
      // Hide header when scrolling down (but not if mobile menu is open)
      else if (currentScrollY > lastScrollY && !isMobileMenuOpen) {
        setIsHeaderVisible(false);
      }

      setLastScrollY(currentScrollY);
    };

    // Throttle scroll events for better performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [lastScrollY, isMobileMenuOpen]);

  return (
    <>
      <header className={`fixed top-0 left-0 right-0 z-40 bg-white border-b border-jobblogg-border shadow-sm transition-transform duration-300 ease-in-out ${
        isHeaderVisible ? 'translate-y-0' : '-translate-y-full'
      }`}>
        <div className="container-wide">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            {/* Logo */}
            <Link
              to="/"
              className="hover:opacity-80 transition-opacity focus-ring rounded-lg"
              aria-label="JobbLogg hjem"
            >
              <JobbLoggLogo
                variant="horizontal"
                size="md"
                className="hover:scale-105 transition-transform duration-200"
              />
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center justify-center flex-1 mx-8">
              <Navigation />
            </div>

            {/* Desktop Action Buttons - Following 2025 Design Principles */}
            <div className="hidden md:flex items-center gap-3">
              <Link to="/sign-in">
                <SecondaryButton
                  size="sm"
                  ariaLabel="Logg inn på eksisterende konto"
                >
                  Logg inn
                </SecondaryButton>
              </Link>
              <Link to="/sign-up">
                <PrimaryButton
                  size="sm"
                  ariaLabel="Registrer deg gratis for JobbLogg"
                >
                  Prøv gratis
                </PrimaryButton>
              </Link>
            </div>

            {/* Mobile: Primary CTA + Hamburger - Following 2025 Design Principles */}
            <div className="flex md:hidden items-center gap-3">
              <Link to="/sign-up">
                <PrimaryButton
                  size="sm"
                  ariaLabel="Registrer deg gratis for JobbLogg"
                >
                  Prøv gratis
                </PrimaryButton>
              </Link>
              
              <button
                onClick={toggleMobileMenu}
                className="w-10 h-10 rounded-lg bg-jobblogg-neutral hover:bg-jobblogg-primary-soft text-jobblogg-text-medium hover:text-jobblogg-primary transition-all duration-200 flex items-center justify-center focus-ring"
                aria-label={isMobileMenuOpen ? 'Lukk meny' : 'Åpne meny'}
                aria-expanded={isMobileMenuOpen}
              >
                <svg 
                  className="w-5 h-5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  {isMobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      <MobileMenu 
        isOpen={isMobileMenuOpen} 
        onClose={closeMobileMenu} 
      />
    </>
  );
};

export default Header;
