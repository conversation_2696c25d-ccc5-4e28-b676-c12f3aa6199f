import React from 'react';

interface NavigationProps {
  /** Whether this is mobile navigation (affects styling) */
  isMobile?: boolean;
  /** Callback when a navigation item is clicked (for mobile menu closing) */
  onItemClick?: () => void;
}

interface NavigationItem {
  label: string;
  href: string;
  description?: string;
}

const navigationItems: NavigationItem[] = [
  {
    label: 'Funksjoner',
    href: '#features',
    description: 'Se alle funksjonene i JobbLogg'
  },
  {
    label: 'Passer for',
    href: '#target-audience',
    description: 'Hvem JobbLogg er laget for'
  },
  {
    label: 'Starte prosjekt',
    href: '#getting-started',
    description: 'Hvordan komme i gang'
  },
  {
    label: 'Hjelp',
    href: '#help',
    description: 'Support og dokumentasjon'
  },
  {
    label: 'Priser',
    href: '/pricing',
    description: 'Priser og abonnement'
  }
];

export const Navigation: React.FC<NavigationProps> = ({ 
  isMobile = false, 
  onItemClick 
}) => {
  const handleItemClick = (href: string) => {
    // Handle anchor links with smooth scroll
    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    } else {
      // Handle regular navigation to other pages
      window.location.href = href;
    }

    // Call the onItemClick callback (for mobile menu closing)
    if (onItemClick) {
      onItemClick();
    }
  };

  if (isMobile) {
    return (
      <nav className="flex flex-col space-y-1" role="navigation" aria-label="Hovednavigasjon">
        {navigationItems.map((item, index) => (
          <button
            key={item.href}
            onClick={() => handleItemClick(item.href)}
            className={`flex flex-col items-start p-4 rounded-xl text-left hover:bg-jobblogg-primary-soft transition-all duration-300 focus-ring group min-h-[44px] justify-center hover:scale-105 animate-stagger-${index + 1}`}
            aria-describedby={item.description ? `${item.href}-desc` : undefined}
          >
            <span className="text-lg font-medium text-jobblogg-text-strong group-hover:text-jobblogg-primary transition-colors">
              {item.label}
            </span>
            {item.description && (
              <span 
                id={`${item.href}-desc`}
                className="text-sm text-jobblogg-text-muted mt-1"
              >
                {item.description}
              </span>
            )}
          </button>
        ))}
      </nav>
    );
  }

  return (
    <nav className="flex items-center space-x-6 lg:space-x-8" role="navigation" aria-label="Hovednavigasjon">
      {navigationItems.map((item, index) => (
        <button
          key={item.href}
          onClick={() => handleItemClick(item.href)}
          className={`text-jobblogg-text-medium hover:text-jobblogg-primary font-medium transition-all duration-300 focus-ring rounded-lg px-3 py-2 relative group hover:scale-105 animate-stagger-${index + 1}`}
          aria-label={`Gå til ${item.label}`}
        >
          {item.label}
          
          {/* Hover underline effect */}
          <span className="absolute bottom-0 left-3 right-3 h-0.5 bg-jobblogg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full" />
        </button>
      ))}
    </nav>
  );
};

export default Navigation;
