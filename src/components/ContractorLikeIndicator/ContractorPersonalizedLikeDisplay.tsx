import React from 'react';
import { TextMuted } from '../ui';

interface ContractorPersonalizedLikeDisplayProps {
  /** Total number of likes */
  totalLikes: number;
  /** Detailed like information */
  likes: Array<{
    customerSessionId: string;
    customerName?: string;
    createdAt: number;
  }>;
  /** Project customer name for personalization */
  projectCustomerName?: string;
  /** Whether to show the display */
  showCount?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Contractor-specific enhanced like display component that shows personalized text for single likes
 * and numerical counts for multiple likes, following Norwegian localization standards.
 * 
 * This version is specifically designed for contractor views and does not show "Du liker dette"
 * since contractors typically don't like their own project images.
 * 
 * @example
 * ```tsx
 * <ContractorPersonalizedLikeDisplay
 *   totalLikes={1}
 *   likes={[{ customerSessionId: 'abc123', customerName: '<PERSON> Do<PERSON>', createdAt: Date.now() }]}
 *   projectCustomerName="Acme Corp"
 *   showCount={true}
 * />
 * ```
 */
export const ContractorPersonalizedLikeDisplay: React.FC<ContractorPersonalizedLikeDisplayProps> = ({
  totalLikes,
  likes,
  projectCustomerName,
  showCount = true,
  className = ''
}) => {
  // Don't show anything if no likes or showCount is false
  if (!showCount || totalLikes === 0) {
    return null;
  }

  // For multiple likes, show numerical count
  if (totalLikes > 1) {
    return (
      <TextMuted className={`text-sm font-medium ${className}`}>
        {totalLikes} likes
      </TextMuted>
    );
  }

  // For single like, show personalized text with customer name
  if (totalLikes === 1 && likes.length > 0) {
    // Show personalized text with customer name from project data
    if (projectCustomerName) {
      return (
        <TextMuted className={`text-sm font-medium ${className}`}>
          {projectCustomerName} liker dette
        </TextMuted>
      );
    }
    
    // Fallback when customer name is not available
    return (
      <TextMuted className={`text-sm font-medium ${className}`}>
        En kunde liker dette
      </TextMuted>
    );
  }

  // Fallback for edge cases
  return null;
};

export default ContractorPersonalizedLikeDisplay;
