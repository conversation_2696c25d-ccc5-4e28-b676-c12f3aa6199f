import React from 'react';
import { getSpecializationById } from '../../utils/specializations';

interface CollaborationSummary {
  hasCollaborated: boolean;
  collaborationType: 'current_project' | 'other_projects' | 'both' | 'none';
  projectCount: number;
  primarySpecialization: string | null;
  lastCollaboration: number | null;
}

interface CollaborationBadgeProps {
  collaboration: CollaborationSummary;
  className?: string;
  showDetails?: boolean;
}

export const CollaborationBadge: React.FC<CollaborationBadgeProps> = ({
  collaboration,
  className = '',
  showDetails = false,
}) => {
  if (!collaboration.hasCollaborated) {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-neutral/20 text-jobblogg-text-medium border border-jobblogg-border ${className}`}>
        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
        Ny samarbeidspartner
      </span>
    );
  }

  const getBadgeConfig = () => {
    switch (collaboration.collaborationType) {
      case 'current_project':
        return {
          icon: (
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          ),
          text: 'Aktiv på dette prosjektet',
          bgColor: 'bg-jobblogg-success/10',
          textColor: 'text-jobblogg-success',
          borderColor: 'border-jobblogg-success/20',
        };
      case 'both':
        return {
          icon: (
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          text: `Tidligere på dette + ${collaboration.projectCount - 1} andre`,
          bgColor: 'bg-jobblogg-primary/10',
          textColor: 'text-jobblogg-primary',
          borderColor: 'border-jobblogg-primary/20',
        };
      case 'other_projects':
        return {
          icon: (
            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          text: `Tidligere samarbeid (${collaboration.projectCount} prosjekt${collaboration.projectCount !== 1 ? 'er' : ''})`,
          bgColor: 'bg-jobblogg-accent/10',
          textColor: 'text-jobblogg-accent',
          borderColor: 'border-jobblogg-accent/20',
        };
      default:
        return null;
    }
  };

  const config = getBadgeConfig();
  if (!config) return null;

  return (
    <div className={`inline-flex flex-col gap-1 ${className}`}>
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor} border ${config.borderColor}`}>
        {config.icon}
        {config.text}
      </span>
      
      {showDetails && collaboration.primarySpecialization && (
        <span className="text-xs text-jobblogg-text-medium">
          Hovedrolle: {getSpecializationById(collaboration.primarySpecialization)?.name || collaboration.primarySpecialization}
        </span>
      )}
    </div>
  );
};

export default CollaborationBadge;
