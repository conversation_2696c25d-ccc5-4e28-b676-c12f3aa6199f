import React, { useState } from 'react';
import { BodyText, TextMuted, SecondaryButton } from '../ui';
import { CollaborationBadge } from './CollaborationBadge';
import { CollaborationHistoryModal } from './CollaborationHistoryModal';
import { getSpecializationById } from '../../utils/specializations';

interface CollaborationSummary {
  hasCollaborated: boolean;
  collaborationType: 'current_project' | 'other_projects' | 'both' | 'none';
  projectCount: number;
  primarySpecialization: string | null;
  lastCollaboration: number | null;
}

interface EnhancedSubcontractorCardProps {
  company: {
    id: string;
    name: string;
    type: string;
    contactPerson?: string;
    userCount: number;
    specializations: string[];
    location?: string;
    collaboration: CollaborationSummary;
  };
  currentProjectId?: string;
  onClick: () => void;
  className?: string;
}

export const EnhancedSubcontractorCard: React.FC<EnhancedSubcontractorCardProps> = ({
  company,
  currentProjectId,
  onClick,
  className = '',
}) => {
  const [showHistoryModal, setShowHistoryModal] = useState(false);

  const handleHistoryClick = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setShowHistoryModal(true);
  };

  const formatLastCollaboration = (timestamp: number) => {
    const now = Date.now();
    const diffMs = now - timestamp;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) return `${diffDays} dager siden`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} måneder siden`;
    return `${Math.floor(diffDays / 365)} år siden`;
  };

  return (
    <>
      <div
        onClick={onClick}
        className={`p-4 border border-jobblogg-border rounded-lg hover:border-jobblogg-accent hover:bg-jobblogg-accent/5 cursor-pointer transition-all ${className}`}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Company Name and Collaboration Badge */}
            <div className="flex items-start justify-between gap-3 mb-3">
              <div className="flex-1">
                <BodyText className="font-medium text-jobblogg-text-strong mb-1">
                  {company.name}
                </BodyText>
                <CollaborationBadge 
                  collaboration={company.collaboration}
                  showDetails={false}
                />
              </div>
              
              {/* History Button */}
              {company.collaboration.hasCollaborated && (
                <SecondaryButton
                  size="sm"
                  onClick={handleHistoryClick}
                  className="flex items-center gap-1 text-xs"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Historikk
                </SecondaryButton>
              )}
            </div>

            {/* Company Details */}
            <div className="space-y-2">
              {company.contactPerson && (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <TextMuted className="text-sm">{company.contactPerson}</TextMuted>
                </div>
              )}

              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <TextMuted className="text-sm">
                  {company.userCount} teammedlem{company.userCount !== 1 ? 'mer' : ''}
                </TextMuted>
              </div>

              {company.location && (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <TextMuted className="text-sm">{company.location}</TextMuted>
                </div>
              )}
            </div>

            {/* Specializations */}
            {company.specializations.length > 0 && (
              <div className="mt-3">
                <div className="flex flex-wrap gap-1">
                  {company.specializations.slice(0, 3).map((spec) => {
                    const specialization = getSpecializationById(spec);
                    return (
                      <span
                        key={spec}
                        className="px-2 py-1 bg-jobblogg-neutral/20 text-jobblogg-text-medium text-xs rounded-full"
                      >
                        {specialization?.name || spec}
                      </span>
                    );
                  })}
                  {company.specializations.length > 3 && (
                    <span className="px-2 py-1 bg-jobblogg-neutral/20 text-jobblogg-text-medium text-xs rounded-full">
                      +{company.specializations.length - 3} flere
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Collaboration Context */}
            {company.collaboration.hasCollaborated && (
              <div className="mt-3 p-3 bg-jobblogg-neutral/10 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    {company.collaboration.collaborationType === 'current_project' && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-jobblogg-success rounded-full animate-pulse"></div>
                        <TextMuted className="text-xs">
                          Aktiv på dette prosjektet
                        </TextMuted>
                      </div>
                    )}
                    
                    {company.collaboration.collaborationType === 'both' && (
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-jobblogg-primary rounded-full"></div>
                          <TextMuted className="text-xs">
                            Tidligere på dette prosjektet
                          </TextMuted>
                        </div>
                        <TextMuted className="text-xs ml-4">
                          + {company.collaboration.projectCount - 1} andre prosjekter
                        </TextMuted>
                      </div>
                    )}
                    
                    {company.collaboration.collaborationType === 'other_projects' && (
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-jobblogg-accent rounded-full"></div>
                        <TextMuted className="text-xs">
                          {company.collaboration.projectCount} tidligere prosjekt{company.collaboration.projectCount !== 1 ? 'er' : ''}
                        </TextMuted>
                      </div>
                    )}
                  </div>
                  
                  {company.collaboration.lastCollaboration && (
                    <TextMuted className="text-xs">
                      {formatLastCollaboration(company.collaboration.lastCollaboration)}
                    </TextMuted>
                  )}
                </div>

                {/* Primary Specialization */}
                {company.collaboration.primarySpecialization && (
                  <div className="mt-2">
                    <TextMuted className="text-xs">
                      Hovedrolle: <span className="font-medium">
                        {getSpecializationById(company.collaboration.primarySpecialization)?.name || company.collaboration.primarySpecialization}
                      </span>
                    </TextMuted>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Collaboration History Modal */}
      <CollaborationHistoryModal
        isOpen={showHistoryModal}
        onClose={() => setShowHistoryModal(false)}
        subcontractorCompanyId={company.id}
        subcontractorCompanyName={company.name}
        currentProjectId={currentProjectId}
      />
    </>
  );
};

export default EnhancedSubcontractorCard;
