import React, { useState, useEffect } from 'react';
// import { useMutation } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { offlineStorage } from '../utils/offlineStorage';
import { PrimaryButton } from './ui';

interface OfflineSyncProps {
  className?: string;
}

export const OfflineSync: React.FC<OfflineSyncProps> = ({ className = '' }) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [pendingItems, setPendingItems] = useState(0);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Convex mutations
  // TODO: Re-enable when type instantiation issue is resolved
  // const createProject = useMutation(api.projects.create);
  // const createLogEntry = useMutation(api.logEntries.create);
  const createProject = async (args: any) => {
    console.log("⚠️ Create project temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation
  const createLogEntry = async (args: any) => {
    console.log("⚠️ Create log entry temporarily disabled due to type issues", args);
  }; // Temporarily mock mutation

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Update pending items count
  useEffect(() => {
    const updatePendingCount = () => {
      const queue = offlineStorage.getSyncQueue();
      setPendingItems(queue.length);
    };

    updatePendingCount();
    
    // Update count periodically
    const interval = setInterval(updatePendingCount, 5000);
    return () => clearInterval(interval);
  }, [syncStatus]);

  // Auto-sync when coming back online
  useEffect(() => {
    if (isOnline && pendingItems > 0 && syncStatus === 'idle') {
      // Auto-sync after a short delay
      const timer = setTimeout(() => {
        handleSync();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [isOnline, pendingItems, syncStatus]);

  const handleSync = async () => {
    if (!isOnline || isSyncing) return;

    setIsSyncing(true);
    setSyncStatus('syncing');
    setSyncProgress(0);

    try {
      const syncQueue = offlineStorage.getSyncQueue();
      
      if (syncQueue.length === 0) {
        setSyncStatus('success');
        setIsSyncing(false);
        return;
      }

      let completed = 0;
      const total = syncQueue.length;

      for (let i = 0; i < syncQueue.length; i++) {
        const item = syncQueue[i];
        
        try {
          if (item.type === 'create-project') {
            // Update sync status to syncing
            offlineStorage.updateSyncStatus(item.data.id, 'project', 'syncing');
            
            // Create project via Convex
            await createProject({
              name: item.data.title,
              description: item.data.description,
              userId: item.data.userId || 'offline-user'
            });
            
            // Mark as synced
            offlineStorage.updateSyncStatus(item.data.id, 'project', 'synced');
            
          } else if (item.type === 'create-project-log') {
            // Update sync status to syncing
            offlineStorage.updateSyncStatus(item.data.id, 'projectLog', 'syncing');
            
            // Create log entry via Convex
            await createLogEntry({
              projectId: item.data.projectId,
              userId: item.data.userId || 'offline-user',
              description: item.data.description,
              imageId: item.data.imageId
            });
            
            // Mark as synced
            offlineStorage.updateSyncStatus(item.data.id, 'projectLog', 'synced');
          }
          
          // Remove from sync queue
          offlineStorage.removeFromSyncQueue(0); // Always remove first item
          
          completed++;
          setSyncProgress((completed / total) * 100);
          
        } catch (error) {
          console.error('[OfflineSync] Error syncing item:', error);
          
          // Mark as error
          if (item.type === 'create-project') {
            offlineStorage.updateSyncStatus(item.data.id, 'project', 'error');
          } else if (item.type === 'create-project-log') {
            offlineStorage.updateSyncStatus(item.data.id, 'projectLog', 'error');
          }
          
          // Continue with next item instead of stopping
          completed++;
          setSyncProgress((completed / total) * 100);
        }
      }
      
      setSyncStatus('success');
      
      // Show success message briefly
      setTimeout(() => {
        setSyncStatus('idle');
      }, 3000);
      
    } catch (error) {
      console.error('[OfflineSync] Sync failed:', error);
      setSyncStatus('error');
      
      // Reset to idle after showing error
      setTimeout(() => {
        setSyncStatus('idle');
      }, 5000);
    } finally {
      setIsSyncing(false);
      setSyncProgress(0);
    }
  };

  // Don't render if no pending items and not syncing
  if (pendingItems === 0 && syncStatus === 'idle') {
    return null;
  }

  return (
    <div className={`
      fixed bottom-20 left-4 right-4 z-40
      bg-white border border-jobblogg-border rounded-xl shadow-lg p-4
      md:left-auto md:right-4 md:max-w-sm
      ${className}
    `}>
      <div className="flex items-start gap-3">
        {/* Status Icon */}
        <div className={`
          flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center
          ${syncStatus === 'syncing' ? 'bg-jobblogg-primary-soft' :
            syncStatus === 'success' ? 'bg-jobblogg-success-soft' :
            syncStatus === 'error' ? 'bg-jobblogg-error-soft' :
            'bg-jobblogg-warning-soft'
          }
        `}>
          {syncStatus === 'syncing' ? (
            <div className="w-5 h-5 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin" />
          ) : syncStatus === 'success' ? (
            <svg className="w-5 h-5 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ) : syncStatus === 'error' ? (
            <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ) : (
            <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-jobblogg-text-strong text-sm mb-1">
            {syncStatus === 'syncing' ? 'Synkroniserer data...' :
             syncStatus === 'success' ? 'Synkronisering fullført' :
             syncStatus === 'error' ? 'Synkroniseringsfeil' :
             `${pendingItems} element${pendingItems !== 1 ? 'er' : ''} venter på synkronisering`
            }
          </h3>
          
          <p className="text-jobblogg-text-medium text-xs mb-3">
            {syncStatus === 'syncing' ? `${Math.round(syncProgress)}% fullført` :
             syncStatus === 'success' ? 'Alle endringer er synkronisert med serveren' :
             syncStatus === 'error' ? 'Noen elementer kunne ikke synkroniseres' :
             isOnline ? 'Trykk for å synkronisere nå' : 'Venter på internettforbindelse'
            }
          </p>

          {/* Progress Bar */}
          {syncStatus === 'syncing' && (
            <div className="w-full bg-jobblogg-neutral rounded-full h-2 mb-3">
              <div 
                className="bg-jobblogg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${syncProgress}%` }}
              />
            </div>
          )}

          {/* Action Buttons */}
          {syncStatus === 'idle' && isOnline && pendingItems > 0 && (
            <PrimaryButton
              onClick={handleSync}
              size="sm"
              className="text-xs"
            >
              Synkroniser nå
            </PrimaryButton>
          )}
          
          {syncStatus === 'error' && (
            <div className="flex gap-2">
              <PrimaryButton
                onClick={handleSync}
                size="sm"
                className="text-xs"
              >
                Prøv igjen
              </PrimaryButton>
              <button
                onClick={() => setSyncStatus('idle')}
                className="text-jobblogg-text-muted hover:text-jobblogg-text-medium text-xs px-2 py-1 rounded transition-colors"
              >
                Lukk
              </button>
            </div>
          )}
        </div>

        {/* Close Button */}
        {syncStatus !== 'syncing' && (
          <button
            onClick={() => setSyncStatus('idle')}
            className="flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center"
            aria-label="Lukk synkroniseringspanel"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Network Status Indicator */}
      <div className="mt-3 pt-3 border-t border-jobblogg-neutral">
        <div className="flex items-center gap-2 text-xs">
          <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-jobblogg-success' : 'bg-jobblogg-error'}`} />
          <span className="text-jobblogg-text-muted">
            {isOnline ? 'Online' : 'Offline - endringer lagres lokalt'}
          </span>
        </div>
      </div>
    </div>
  );
};

// Storage Usage Component
export const StorageUsage: React.FC = () => {
  const [usage, setUsage] = useState({ used: 0, available: 0, percentage: 0 });
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const updateUsage = () => {
      const storageUsage = offlineStorage.getStorageUsage();
      setUsage(storageUsage);
    };

    updateUsage();
    const interval = setInterval(updateUsage, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  if (usage.percentage < 50) {
    return null; // Don't show unless storage is getting full
  }

  return (
    <div className="fixed top-4 left-4 z-40 bg-white border border-jobblogg-warning/20 rounded-lg p-3 max-w-xs shadow-lg">
      <div className="flex items-start gap-2">
        <svg className="w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
        </svg>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-jobblogg-text-strong">
            Lagringsplass {usage.percentage > 80 ? 'nesten full' : 'fylles opp'}
          </p>
          <p className="text-xs text-jobblogg-text-medium mb-2">
            {Math.round(usage.percentage)}% av tilgjengelig plass brukt
          </p>
          
          <div className="w-full bg-jobblogg-neutral rounded-full h-2 mb-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                usage.percentage > 90 ? 'bg-jobblogg-error' :
                usage.percentage > 80 ? 'bg-jobblogg-warning' :
                'bg-jobblogg-primary'
              }`}
              style={{ width: `${Math.min(usage.percentage, 100)}%` }}
            />
          </div>
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors"
          >
            {showDetails ? 'Skjul detaljer' : 'Vis detaljer'}
          </button>
          
          {showDetails && (
            <div className="mt-2 text-xs text-jobblogg-text-muted">
              <p>Brukt: {Math.round(usage.used / 1024)} KB</p>
              <p>Tilgjengelig: {Math.round(usage.available / 1024)} KB</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
