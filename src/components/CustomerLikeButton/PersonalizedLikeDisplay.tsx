import React from 'react';
import { TextMuted } from '../ui';

interface PersonalizedLikeDisplayProps {
  /** Total number of likes */
  totalLikes: number;
  /** Detailed like information */
  likes: Array<{
    customerSessionId: string;
    customerName?: string;
    createdAt: number;
  }>;
  /** Current customer's session ID */
  currentCustomerSessionId: string;
  /** Project customer name for personalization */
  projectCustomerName?: string;
  /** Whether to show the display */
  showCount?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Enhanced like display component that shows personalized text for single likes
 * and numerical counts for multiple likes, following Norwegian localization standards.
 * 
 * @example
 * ```tsx
 * <PersonalizedLikeDisplay
 *   totalLikes={1}
 *   likes={[{ customerSessionId: 'abc123', customerName: '<PERSON>', createdAt: Date.now() }]}
 *   currentCustomerSessionId="xyz789"
 *   projectCustomerName="Acme Corp"
 *   showCount={true}
 * />
 * ```
 */
export const PersonalizedLikeDisplay: React.FC<PersonalizedLikeDisplayProps> = ({
  totalLikes,
  likes,
  currentCustomerSessionId,
  projectCustomerName,
  showCount = true,
  className = ''
}) => {
  // Don't show anything if no likes or showCount is false
  if (!showCount || totalLikes === 0) {
    return null;
  }

  // For multiple likes, show numerical count as before
  if (totalLikes > 1) {
    return (
      <TextMuted className={`text-sm font-medium ${className}`}>
        {totalLikes} {totalLikes === 1 ? 'like' : 'likes'}
      </TextMuted>
    );
  }

  // For single like, show personalized text
  if (totalLikes === 1 && likes.length > 0) {
    const singleLike = likes[0];
    
    // Check if current customer liked it
    if (singleLike.customerSessionId === currentCustomerSessionId) {
      return (
        <TextMuted className={`text-sm font-medium ${className}`}>
          Du liker dette
        </TextMuted>
      );
    }
    
    // Another customer liked it - show personalized text with customer name
    if (projectCustomerName) {
      return (
        <TextMuted className={`text-sm font-medium ${className}`}>
          {projectCustomerName} liker dette
        </TextMuted>
      );
    }
    
    // Fallback when customer name is not available
    return (
      <TextMuted className={`text-sm font-medium ${className}`}>
        En kunde liker dette
      </TextMuted>
    );
  }

  // Fallback for edge cases
  return null;
};

export default PersonalizedLikeDisplay;
