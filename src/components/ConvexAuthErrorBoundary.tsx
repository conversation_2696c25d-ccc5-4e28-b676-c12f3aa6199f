import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  retryCount: number;
}

/**
 * Error Boundary specifically for handling Convex authentication errors
 * 
 * This component catches authentication-related errors and provides
 * a fallback UI with retry functionality.
 */
export class ConvexAuthErrorBoundary extends Component<Props, State> {
  private retryTimer?: NodeJS.Timeout;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if this is an authentication error
    const isAuthError = error.message.includes('autentisert') || 
                       error.message.includes('authentication') ||
                       error.message.includes('Ikke autentisert') ||
                       error.message.includes('Autentisering ikke fullført');

    if (isAuthError) {
      return {
        hasError: true,
        error,
        retryCount: 0
      };
    }

    // Re-throw non-auth errors
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ConvexAuthErrorBoundary caught an error:', error, errorInfo);
  }

  componentDidUpdate(_prevProps: Props, prevState: State) {
    // Auto-retry after a delay when error occurs
    if (this.state.hasError && !prevState.hasError && this.state.retryCount < 3) {
      this.retryTimer = setTimeout(() => {
        this.handleRetry();
      }, 2000 * (this.state.retryCount + 1)); // Exponential backoff: 2s, 4s, 6s
    }
  }

  componentWillUnmount() {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: undefined,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleManualRetry = () => {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
    }
    this.handleRetry();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="min-h-screen bg-white flex items-center justify-center">
          <div className="text-center space-y-6 max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            
            <div className="space-y-3">
              <h2 className="text-xl font-semibold text-jobblogg-text-strong">
                Autentiseringsproblem
              </h2>
              <p className="text-jobblogg-text-muted">
                Vi har problemer med å koble til serveren. Dette skjer vanligvis rett etter innlogging.
              </p>
              
              {this.state.retryCount < 3 ? (
                <div className="bg-jobblogg-primary-soft rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-sm text-jobblogg-text-muted">
                      Prøver automatisk igjen... (forsøk {this.state.retryCount + 1}/3)
                    </p>
                  </div>
                </div>
              ) : (
                <div className="bg-jobblogg-neutral-secondary rounded-lg p-4">
                  <p className="text-sm text-jobblogg-text-muted mb-3">
                    Automatiske forsøk fullført. Du kan prøve manuelt eller laste siden på nytt.
                  </p>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={this.handleManualRetry}
                      className="px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary-dark transition-colors duration-200"
                    >
                      Prøv igjen
                    </button>
                    <button
                      onClick={() => window.location.reload()}
                      className="px-4 py-2 bg-jobblogg-neutral-primary text-jobblogg-text-strong rounded-lg hover:bg-jobblogg-neutral-secondary transition-colors duration-200"
                    >
                      Last siden på nytt
                    </button>
                  </div>
                </div>
              )}
            </div>

            <div className="text-xs text-jobblogg-text-muted bg-jobblogg-neutral-secondary rounded-lg p-3">
              <p className="font-medium mb-1">Teknisk informasjon:</p>
              <p className="font-mono text-xs break-all">
                {this.state.error?.message || 'Ukjent autentiseringsfeil'}
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
