import React, { useState, useEffect, useMemo, useRef } from 'react';
import { usePWA } from '../hooks/usePWA';
import { offlineStorage } from '../utils/offlineStorage';

type NetworkState = 'synced' | 'offline' | 'syncing';

interface NetworkStatusIndicatorProps {
  className?: string;
  showTextOnMobile?: boolean;
}

export const NetworkStatusIndicator: React.FC<NetworkStatusIndicatorProps> = ({
  className = '',
  showTextOnMobile = false
}) => {
  const { isOnline } = usePWA();
  const [pendingSyncItems, setPendingSyncItems] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [hasTransitionedOffline, setHasTransitionedOffline] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const indicatorRef = useRef<HTMLDivElement>(null);
  const previousOnlineState = useRef(isOnline);

  // Update pending sync items count
  useEffect(() => {
    const updatePendingCount = () => {
      try {
        const queue = offlineStorage.getSyncQueue();
        setPendingSyncItems(queue.length);
      } catch (error) {
        console.warn('[NetworkStatusIndicator] Could not get sync queue:', error);
        setPendingSyncItems(0);
      }
    };

    updatePendingCount();
    
    // Update count periodically
    const interval = setInterval(updatePendingCount, 5000);
    return () => clearInterval(interval);
  }, []);

  // Track online->offline transitions for pulse animation
  useEffect(() => {
    if (previousOnlineState.current && !isOnline) {
      setHasTransitionedOffline(true);
      // Reset after animation duration
      const timer = setTimeout(() => setHasTransitionedOffline(false), 3000);
      return () => clearTimeout(timer);
    }
    previousOnlineState.current = isOnline;
  }, [isOnline]);

  // Determine current network state
  const networkState: NetworkState = useMemo(() => {
    if (!isOnline) return 'offline';
    if (pendingSyncItems > 0) return 'syncing';
    return 'synced';
  }, [isOnline, pendingSyncItems]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        indicatorRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !indicatorRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [showDropdown]);

  const getStateConfig = () => {
    switch (networkState) {
      case 'synced':
        return {
          text: 'Synkronisert',
          colors: 'text-jobblogg-accent',
          dotColors: 'bg-jobblogg-accent',
          ariaLabel: 'Nettverksstatus: Synkronisert',
          clickable: false,
          animation: ''
        };
      case 'offline':
        return {
          text: 'Frakoblet',
          colors: 'text-jobblogg-error',
          dotColors: 'bg-jobblogg-error',
          ariaLabel: `Nettverksstatus: Frakoblet. Klikk for detaljer`,
          clickable: true,
          animation: hasTransitionedOffline ? 'animate-pulse' : ''
        };
      case 'syncing':
        return {
          text: 'Synkroniserer...',
          colors: 'text-jobblogg-primary',
          dotColors: 'bg-jobblogg-primary',
          ariaLabel: 'Nettverksstatus: Synkroniserer data',
          clickable: false,
          animation: 'animate-spin'
        };
    }
  };

  const config = getStateConfig();

  const handleClick = () => {
    if (config.clickable) {
      setShowDropdown(!showDropdown);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (config.clickable && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      setShowDropdown(!showDropdown);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Status Indicator */}
      <div
        ref={indicatorRef}
        className={`flex items-center gap-1.5 text-sm font-medium transition-all duration-200 ${
          config.colors
        } ${
          config.clickable 
            ? 'cursor-pointer hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2 rounded-lg px-2 py-1 -mx-2 -my-1' 
            : ''
        }`}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        tabIndex={config.clickable ? 0 : -1}
        role={config.clickable ? 'button' : undefined}
        aria-label={config.ariaLabel}
        aria-expanded={config.clickable ? showDropdown : undefined}
      >
        {/* Status Icon/Dot */}
        {networkState === 'syncing' ? (
          <svg 
            className={`w-4 h-4 ${config.animation}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
            />
          </svg>
        ) : (
          <div 
            className={`w-2 h-2 rounded-full ${config.dotColors} ${config.animation}`}
            aria-hidden="true"
          />
        )}
        
        {/* Status Text */}
        <span className={showTextOnMobile ? 'block' : 'hidden sm:block'}>
          {config.text}
        </span>
        
        {/* Screen reader text */}
        <span className="sr-only">{config.ariaLabel}</span>
      </div>

      {/* Offline Dropdown - Desktop Popover / Mobile Bottom Sheet */}
      {showDropdown && networkState === 'offline' && (
        <>
          {/* Mobile Bottom Sheet */}
          <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={() => setShowDropdown(false)} />
          <div
            ref={dropdownRef}
            className="fixed bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-lg z-50 animate-slide-up md:absolute md:right-0 md:top-full md:mt-2 md:w-80 md:left-auto md:bottom-auto md:rounded-xl md:border md:border-jobblogg-border"
            role="dialog"
            aria-labelledby="offline-status-title"
          >
          <div className="p-4 pb-6 space-y-4 md:pb-4">
            {/* Mobile Handle */}
            <div className="w-12 h-1 bg-jobblogg-neutral rounded-full mx-auto mb-2 md:hidden" />

            {/* Header */}
            <div>
              <h3 id="offline-status-title" className="font-semibold text-jobblogg-text-strong text-base mb-1">
                Du er frakoblet
              </h3>
              <p className="text-sm text-jobblogg-text-medium">
                Ditt arbeid lagres trygt lokalt og lastes opp automatisk når du er på nett igjen.
              </p>
            </div>

            {/* Available Offline */}
            <div>
              <h4 className="font-medium text-jobblogg-text-strong text-sm mb-2">
                Tilgjengelig uten internett:
              </h4>
              <ul className="space-y-1 text-sm">
                <li className="flex items-center gap-2 text-jobblogg-accent">
                  <span className="text-jobblogg-accent">✅</span>
                  Se lagrede prosjekter
                </li>
                <li className="flex items-center gap-2 text-jobblogg-accent">
                  <span className="text-jobblogg-accent">✅</span>
                  Se prosjektdetaljer offline
                </li>
                <li className="flex items-center gap-2 text-jobblogg-accent">
                  <span className="text-jobblogg-accent">✅</span>
                  Se lagrede loggoppføringer
                </li>
                <li className="flex items-center gap-2 text-jobblogg-accent">
                  <span className="text-jobblogg-accent">✅</span>
                  Last opp bilder
                </li>
              </ul>
            </div>

            {/* Requires Internet */}
            <div>
              <h4 className="font-medium text-jobblogg-text-strong text-sm mb-2">
                Krever internettforbindelse:
              </h4>
              <ul className="space-y-1 text-sm">
                <li className="flex items-center gap-2 text-jobblogg-text-muted">
                  <span className="text-jobblogg-error">❌</span>
                  Opprett nye prosjekter
                </li>
                <li className="flex items-center gap-2 text-jobblogg-text-muted">
                  <span className="text-jobblogg-error">❌</span>
                  Legg til loggoppføringer
                </li>
                <li className="flex items-center gap-2 text-jobblogg-text-muted">
                  <span className="text-jobblogg-error">❌</span>
                  Skriv meldinger/kommentarer
                </li>
                <li className="flex items-center gap-2 text-jobblogg-text-muted">
                  <span className="text-jobblogg-error">❌</span>
                  Del prosjekter med kunder
                </li>
                <li className="flex items-center gap-2 text-jobblogg-text-muted">
                  <span className="text-jobblogg-error">❌</span>
                  Oppdater bedriftsprofil
                </li>
                <li className="flex items-center gap-2 text-jobblogg-text-muted">
                  <span className="text-jobblogg-error">❌</span>
                  Synkroniser med server
                </li>
              </ul>
            </div>
          </div>
          </div>
        </>
      )}
    </div>
  );
};
