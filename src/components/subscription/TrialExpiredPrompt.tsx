import React, { useState } from 'react';
// import { useAction } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
import { useUser } from '@clerk/clerk-react';
// import { api } from '../../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { Heading2, BodyText, TextMuted, PrimaryButton, SecondaryButton, Card } from '../ui';

interface TrialExpiredPromptProps {
  onDismiss?: () => void;
  showDismiss?: boolean;
  variant?: 'modal' | 'banner' | 'page';
}

export const TrialExpiredPrompt: React.FC<TrialExpiredPromptProps> = ({
  onDismiss,
  showDismiss = false,
  variant = 'banner'
}) => {
  const { user } = useUser();
  const { subscription, isTrialExpired, isInGracePeriod } = useSubscriptionAccess();
  // TODO: Re-enable when type instantiation issue is resolved
  // const createPortalSession = useAction(api.subscriptions.createPortalSession as any);
  const createPortalSession = async (args: any) => {
    console.log("⚠️ Create portal session temporarily disabled due to type issues", args);
    return { url: '#' };
  }; // Temporarily mock action
  const [isUpgrading, setIsUpgrading] = useState(false);

  // Don't show if not expired or no subscription
  if (!subscription || (!isTrialExpired && !isInGracePeriod)) return null;

  const handleUpgrade = async () => {
    if (!user) return;

    setIsUpgrading(true);
    try {
      const { url } = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });
      window.location.href = url;
    } catch (error) {
      console.error('Failed to create portal session:', error);
      setIsUpgrading(false);
    }
  };

  const getGracePeriodDaysLeft = () => {
    if (!subscription?.trialEnd) return 0;
    const gracePeriodEnd = subscription.trialEnd + (3 * 24 * 60 * 60 * 1000); // 3 days after trial end
    const now = Date.now();
    return Math.max(0, Math.ceil((gracePeriodEnd - now) / (24 * 60 * 60 * 1000)));
  };

  const graceDaysLeft = getGracePeriodDaysLeft();

  // Banner variant (for dashboard/pages)
  if (variant === 'banner') {
    return (
      <div className="bg-jobblogg-warning-soft border-l-4 border-jobblogg-warning rounded-lg p-4 mb-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <svg className="w-6 h-6 text-jobblogg-warning mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-jobblogg-warning mb-1">
                {isInGracePeriod ? 'Prøveperioden er utløpt' : 'Prøveperioden har utløpt'}
              </h3>
              <p className="text-sm text-jobblogg-text-medium">
                {isInGracePeriod ? (
                  <>
                    Du har {graceDaysLeft} {graceDaysLeft === 1 ? 'dag' : 'dager'} igjen med begrenset tilgang. 
                    Oppgrader nå for å fortsette å bruke alle funksjoner.
                  </>
                ) : (
                  'Du har nå begrenset tilgang til JobbLogg. Oppgrader for å fortsette å bruke alle funksjoner.'
                )}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <PrimaryButton
              onClick={handleUpgrade}
              disabled={isUpgrading}
              className="text-sm px-4 py-2"
            >
              {isUpgrading ? 'Laster...' : 'Oppgrader nå'}
            </PrimaryButton>
            {showDismiss && onDismiss && (
              <button
                onClick={onDismiss}
                className="text-jobblogg-text-muted hover:text-jobblogg-text-medium p-1"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Modal variant
  if (variant === 'modal') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <Card className="max-w-md w-full">
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            
            <div>
              <Heading2 className="mb-3">Prøveperioden er utløpt</Heading2>
              <BodyText className="text-jobblogg-text-medium">
                {isInGracePeriod ? (
                  <>
                    Du har {graceDaysLeft} {graceDaysLeft === 1 ? 'dag' : 'dager'} igjen med begrenset tilgang til JobbLogg. 
                    Oppgrader til en betalt plan for å fortsette å bruke alle funksjoner.
                  </>
                ) : (
                  'Din 7-dagers gratis prøveperiode er nå over. Oppgrader til en betalt plan for å fortsette å bruke JobbLogg.'
                )}
              </BodyText>
            </div>

            <div className="space-y-3">
              <PrimaryButton
                onClick={handleUpgrade}
                disabled={isUpgrading}
                className="w-full"
              >
                {isUpgrading ? 'Laster...' : 'Se abonnementsplaner'}
              </PrimaryButton>
              {onDismiss && (
                <SecondaryButton onClick={onDismiss} className="w-full">
                  Lukk
                </SecondaryButton>
              )}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Page variant (full page replacement)
  return (
    <div className="min-h-screen bg-jobblogg-surface flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center space-y-8">
        <div className="w-20 h-20 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto">
          <svg className="w-10 h-10 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <div>
          <Heading2 className="mb-4">Prøveperioden er utløpt</Heading2>
          <BodyText className="text-jobblogg-text-medium mb-6">
            {isInGracePeriod ? (
              <>
                Du har {graceDaysLeft} {graceDaysLeft === 1 ? 'dag' : 'dager'} igjen med begrenset tilgang til JobbLogg. 
                Oppgrader til en betalt plan for å fortsette å bruke alle funksjoner.
              </>
            ) : (
              'Din 7-dagers gratis prøveperiode er nå over. Oppgrader til en betalt plan for å fortsette å bruke JobbLogg.'
            )}
          </BodyText>
          
          <div className="bg-white rounded-lg p-4 border border-jobblogg-border mb-6">
            <TextMuted className="text-sm">
              <strong>Hva du fortsatt kan gjøre:</strong><br />
              • Se eksisterende prosjekter (kun lesing)<br />
              • Laste ned prosjektdata<br />
              • Se samtaler og meldinger
            </TextMuted>
          </div>
        </div>

        <div className="space-y-3">
          <PrimaryButton
            onClick={handleUpgrade}
            disabled={isUpgrading}
            className="w-full"
          >
            {isUpgrading ? 'Laster...' : 'Se abonnementsplaner'}
          </PrimaryButton>
          <SecondaryButton 
            onClick={() => window.location.href = '/'}
            className="w-full"
          >
            Gå til dashboard
          </SecondaryButton>
        </div>
      </div>
    </div>
  );
};
