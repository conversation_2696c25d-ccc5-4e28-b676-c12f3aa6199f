import React from 'react';
// import { useQuery } from 'convex/react'; // TODO: Re-enable when type instantiation issues are resolved
// import { api } from '../../convex/_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import {
  Heading3,
  BodyText,
  TextMuted
} from './ui';
import { Id } from '../../convex/_generated/dataModel';

interface ContractorInformationCardProps {
  contractorUserId: string;
  projectId?: Id<"projects">; // Optional project ID for project-specific contact person
  className?: string;
  defaultCollapsed?: boolean;
}

export const ContractorInformationCard: React.FC<ContractorInformationCardProps> = ({
  contractorUserId: _contractorUserId,
  projectId: _projectId,
  className = '',
  defaultCollapsed = false
}) => {
  const [isExpanded, setIsExpanded] = React.useState(!defaultCollapsed);

  // Fetch project-specific contact person if projectId is provided
  // TODO: Re-enable when type instantiation issue is resolved
  // const projectContactPerson = useQuery(
  //   api.projects.getProjectContactPerson,
  //   projectId ? { projectId } : "skip"
  // );
  const projectContactPerson = {
    phone: '',
    email: '',
    name: '',
    source: 'company',
    contactPerson: ''
  }; // Temporarily provide fallback structure due to type instantiation issues

  // Fetch contractor company information (fallback)
  // TODO: Re-enable when type instantiation issue is resolved
  // const contractorCompany = useQuery(
  //   api.customers.getContractorCompanyByUserId,
  //   { contractorUserId }
  // );
  const contractorCompany = {
    phone: '',
    email: '',
    name: '',
    orgNumber: '',
    contactPerson: ''
  }; // Temporarily provide fallback structure due to type instantiation issues

  // Format contractor address with fallback logic
  const formatContractorAddress = (company: any): string => {
    // Try structured address fields first
    if (company.streetAddress && company.postalCode && company.city) {
      let address = company.streetAddress;

      // Add entrance if available
      if (company.entrance) {
        address += `, ${company.entrance}`;
      }

      // Add postal code and city
      address += `, ${company.postalCode} ${company.city}`;

      return address;
    }

    // Fallback to legacy address field
    if (company.address) {
      return company.address;
    }

    // No address available
    return 'Ingen adresse registrert';
  };

  // Loading state
  if (contractorCompany === undefined) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="mb-4">
          <div className="h-6 bg-jobblogg-neutral rounded w-1/2"></div>
        </div>
        <div className="bg-jobblogg-neutral rounded-lg border border-jobblogg-border overflow-hidden">
          <div className="p-4 sm:p-6 border-b border-jobblogg-border">
            <div className="h-4 bg-jobblogg-neutral rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="h-12 bg-jobblogg-neutral rounded"></div>
              <div className="h-12 bg-jobblogg-neutral rounded"></div>
            </div>
          </div>
          <div className="p-4 sm:p-6">
            <div className="h-4 bg-jobblogg-neutral rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="h-12 bg-jobblogg-neutral rounded"></div>
              <div className="h-12 bg-jobblogg-neutral rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state - contractor company not found
  if (!contractorCompany) {
    return (
      <div className={className}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Heading3>Leverandørinformasjon</Heading3>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center justify-center w-8 h-8 rounded-full bg-jobblogg-neutral hover:bg-jobblogg-border transition-colors duration-200 min-h-[44px] min-w-[44px]"
              aria-label={isExpanded ? "Skjul leverandørinformasjon" : "Vis leverandørinformasjon"}
            >
              <svg
                className={`w-4 h-4 text-jobblogg-text-medium transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>
        <div className="bg-jobblogg-neutral rounded-lg border border-jobblogg-border p-4 sm:p-6">
          <div className="text-center py-4">
            <div className="w-12 h-12 mx-auto mb-3 bg-jobblogg-warning-soft rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <TextMuted className="text-sm">
              Leverandørinformasjon ikke tilgjengelig
            </TextMuted>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <Heading3>Leverandørinformasjon</Heading3>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center justify-center w-8 h-8 rounded-full bg-jobblogg-neutral hover:bg-jobblogg-border transition-colors duration-200 min-h-[44px] min-w-[44px]"
            aria-label={isExpanded ? "Skjul leverandørinformasjon" : "Vis leverandørinformasjon"}
          >
            <svg
              className={`w-4 h-4 text-jobblogg-text-medium transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>

      <div className="bg-jobblogg-neutral rounded-lg border border-jobblogg-border overflow-hidden">
        {/* Collapsed State - Basic Info */}
        {!isExpanded && (
          <div className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <BodyText className="font-medium text-jobblogg-text-strong">
                    {contractorCompany.name}
                  </BodyText>
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-jobblogg-primary-soft text-jobblogg-primary">
                    Leverandør
                  </span>
                </div>
                <TextMuted className="text-sm">
                  {formatContractorAddress(contractorCompany)}
                </TextMuted>
              </div>
            </div>
          </div>
        )}

        {/* Expanded State - Full Details */}
        {isExpanded && (
          <>
            {/* Contractor Identity Section */}
            <div className="p-4 sm:p-6 border-b border-jobblogg-border">
          <div className="flex items-center gap-2 mb-4">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h4 className="text-sm font-semibold text-jobblogg-text-strong">
              Bedriftsinformasjon
            </h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Company Name */}
            <div>
              <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                Bedriftsnavn
              </TextMuted>
              <BodyText className="font-medium">
                {contractorCompany.name}
              </BodyText>
            </div>

            {/* Organization Number */}
            {contractorCompany.orgNumber && (
              <div>
                <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                  Organisasjonsnummer
                </TextMuted>
                <BodyText className="font-mono">
                  {contractorCompany.orgNumber}
                </BodyText>
              </div>
            )}

            {/* Business Address */}
            <div className="md:col-span-2">
              <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                Bedriftsadresse
              </TextMuted>
              <BodyText>
                {formatContractorAddress(contractorCompany)}
              </BodyText>
            </div>
          </div>
        </div>

        {/* Contact Information Section */}
        <div className="p-4 sm:p-6">
          <div className="flex items-center gap-2 mb-4">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h4 className="text-sm font-semibold text-jobblogg-text-strong">
              Kontaktinformasjon
            </h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Contact Person - Use project-specific contact if available */}
            {(() => {
              const contactPerson = projectContactPerson || {
                name: contractorCompany.contactPerson,
                phone: contractorCompany.phone,
                email: contractorCompany.email,
                source: 'company'
              };

              return contactPerson.name && (
                <div>
                  <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                    Prosjektkontakt
                  </TextMuted>
                  <BodyText className="font-medium">
                    {contactPerson.name}
                  </BodyText>
                  {/* Contact source indicator */}
                  <div className="mt-1">
                    <TextMuted className="text-xs">
                      {contactPerson.source === 'assigned' && '👤 Tildelt prosjektleder'}
                      {contactPerson.source === 'creator' && '🏗️ Prosjektskaper'}
                      {contactPerson.source === 'explicit' && '📋 Valgt prosjektkontakt'}
                      {contactPerson.source === 'company' && '🏢 Bedriftskontakt'}
                    </TextMuted>
                  </div>
                </div>
              );
            })()}

            {/* Phone Number - Use project contact phone if available */}
            {(() => {
              const phone = projectContactPerson?.phone || contractorCompany.phone;
              return phone && (
                <div>
                  <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                    Telefon
                  </TextMuted>
                  <BodyText>
                    <a
                      href={`tel:${phone}`}
                      className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 hover:underline min-h-[44px] inline-flex items-center"
                      aria-label={`Ring ${phone}`}
                    >
                      {phone}
                    </a>
                  </BodyText>
                </div>
              );
            })()}

            {/* Email - Use project contact email if available */}
            {(() => {
              const email = projectContactPerson?.email || contractorCompany.email;
              return email && (
                <div className="md:col-span-2">
                  <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                    E-post
                  </TextMuted>
                  <BodyText>
                    <a
                      href={`mailto:${email}`}
                      className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 hover:underline min-h-[44px] inline-flex items-center"
                      aria-label={`Send e-post til ${email}`}
                    >
                      {email}
                    </a>
                  </BodyText>
                </div>
              );
            })()}
          </div>
        </div>
        </>
        )}
      </div>
    </div>
  );
};

export default ContractorInformationCard;
