import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { PrimaryButton, SecondaryButton } from './ui';
import { gdprCompliance } from '../utils/offlineEncryption';

interface OfflineConsentModalProps {
  isOpen: boolean;
  onConsent: () => void;
  onDecline: () => void;
  className?: string;
}

/**
 * GDPR-compliant consent modal for offline data storage
 * Provides transparent information about data processing and user rights
 */
export const OfflineConsentModal: React.FC<OfflineConsentModalProps> = ({
  isOpen,
  onConsent,
  onDecline,
  className = ''
}) => {
  const { user } = useUser();
  const [showDetails, setShowDetails] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const dataProcessingInfo = gdprCompliance.getDataProcessingInfo();

  const handleConsent = async () => {
    if (!user?.id) return;

    setIsProcessing(true);
    try {
      gdprCompliance.recordConsent(user.id);
      onConsent();
    } catch (error) {
      console.error('Error recording consent:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDecline = () => {
    onDecline();
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 ${className}`}>
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-jobblogg-border">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-jobblogg-primary rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-jobblogg-text-strong">
                Offline-funksjonalitet
              </h2>
              <p className="text-sm text-jobblogg-text-medium">
                Samtykke til lokal datalagring
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Main explanation */}
          <div className="space-y-4">
            <p className="text-jobblogg-text-strong">
              For å bruke JobbLogg offline må vi lagre noen av dine data lokalt på enheten din. 
              Dette gjør at du kan fortsette å arbeide selv uten internettforbindelse.
            </p>

            <div className="bg-jobblogg-primary-soft p-4 rounded-lg">
              <h3 className="font-medium text-jobblogg-text-strong mb-2">
                🔒 Dine data er sikre
              </h3>
              <ul className="text-sm text-jobblogg-text-medium space-y-1">
                <li>• All data krypteres med AES-256 før lagring</li>
                <li>• Kun du har tilgang til dine offline data</li>
                <li>• Data slettes automatisk når du logger ut</li>
                <li>• Ingen deling med tredjeparter</li>
              </ul>
            </div>

            {/* Purpose and data types */}
            <div className="space-y-3">
              <h3 className="font-medium text-jobblogg-text-strong">
                Formål med databehandlingen:
              </h3>
              <p className="text-sm text-jobblogg-text-medium">
                {dataProcessingInfo.purpose}
              </p>

              <h3 className="font-medium text-jobblogg-text-strong">
                Hvilke data lagres lokalt:
              </h3>
              <ul className="text-sm text-jobblogg-text-medium space-y-1">
                {dataProcessingInfo.dataTypes.map((type, index) => (
                  <li key={index}>• {type}</li>
                ))}
              </ul>
            </div>

            {/* Detailed information toggle */}
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-sm font-medium transition-colors"
            >
              {showDetails ? 'Skjul detaljer' : 'Vis detaljert informasjon'}
            </button>

            {showDetails && (
              <div className="bg-jobblogg-neutral-soft p-4 rounded-lg space-y-3 text-sm">
                <div>
                  <h4 className="font-medium text-jobblogg-text-strong mb-1">
                    Lagringsperiode:
                  </h4>
                  <p className="text-jobblogg-text-medium">
                    {dataProcessingInfo.retention}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-jobblogg-text-strong mb-1">
                    Sikkerhetstiltak:
                  </h4>
                  <ul className="text-jobblogg-text-medium space-y-1">
                    {dataProcessingInfo.security.map((measure, index) => (
                      <li key={index}>• {measure}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-jobblogg-text-strong mb-1">
                    Dine rettigheter:
                  </h4>
                  <ul className="text-jobblogg-text-medium space-y-1">
                    <li>• Du kan trekke tilbake samtykket når som helst</li>
                    <li>• Du kan slette alle offline data i innstillingene</li>
                    <li>• Du har rett til innsyn i hvilke data som lagres</li>
                    <li>• Data slettes automatisk ved utlogging</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Important notice */}
          <div className="bg-jobblogg-warning-soft border border-jobblogg-warning/20 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h4 className="font-medium text-jobblogg-text-strong mb-1">
                  Viktig informasjon
                </h4>
                <p className="text-sm text-jobblogg-text-medium">
                  Offline-funksjonalitet krever at du har logget inn minst én gang på denne enheten. 
                  Uten samtykke kan du fortsatt bruke JobbLogg, men kun når du har internettforbindelse.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-6 border-t border-jobblogg-border bg-jobblogg-neutral-soft">
          <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
            <SecondaryButton
              onClick={handleDecline}
              className="sm:order-1"
            >
              Avslå - kun online bruk
            </SecondaryButton>
            <PrimaryButton
              onClick={handleConsent}
              loading={isProcessing}
              className="sm:order-2"
            >
              {isProcessing ? 'Lagrer samtykke...' : 'Godta - aktiver offline-modus'}
            </PrimaryButton>
          </div>
          
          <p className="text-xs text-jobblogg-text-muted mt-3 text-center">
            Ved å godta samtykker du til behandling av personopplysninger som beskrevet ovenfor. 
            Du kan trekke tilbake samtykket når som helst i innstillingene.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OfflineConsentModal;
