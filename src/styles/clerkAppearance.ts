// Clerk Appearance Configuration for JobbLogg
// Light theme only configuration

// Light theme color definitions - Updated with new design system
const lightColors = {
  background: '#ffffff',
  backgroundSecondary: '#F8FAFC',
  text: '#1F2937',           // Strong text for headings (WCAG AA: 12.6:1)
  textSecondary: '#4B5563',  // Medium text for body content (WCAG AA: 7.3:1)
  textMuted: '#6B7280',      // Muted text for secondary content (WCAG AA: 4.9:1)
  border: '#E5E7EB',
  borderFocus: '#D1D5DB',
  shimmer: '#F8FAFC',
  inputBackground: '#ffffff',
  cardBackground: '#F8FAFC',
};

// Static light theme appearance configuration
export const clerkAppearance = {
    variables: {
      // JobbLogg Brand Colors - Updated design system
      colorPrimary: '#2563EB', // Updated to match jobblogg-primary
      colorPrimaryFocus: '#1E40AF',
      colorSuccess: '#10B981',
      colorWarning: '#FBBF24', // WCAG AA compliant warning color
      colorDanger: '#DC2626',

      // Typography
      fontFamily: 'Inter, system-ui, sans-serif',
      fontSize: '16px',
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },

      // Layout & Spacing
      borderRadius: '0.75rem',
      spacingUnit: '1rem',

      // Light theme colors
      colorBackground: lightColors.background,
      colorInputBackground: lightColors.inputBackground,
      colorInputText: lightColors.text,
      colorText: lightColors.text,
      colorTextSecondary: lightColors.textSecondary,
      colorTextOnPrimaryBackground: '#ffffff',

      // Shadows and borders
      colorBorder: lightColors.border,
      colorShimmer: lightColors.shimmer,
    },
  
    elements: {
      // Main card container
      card: {
        backgroundColor: lightColors.cardBackground,
        borderRadius: '1rem',
        boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
        border: `1px solid ${lightColors.border}`,
        padding: '2rem',
        maxWidth: '28rem',
        width: '100%',
      },

      // Header styling
      headerTitle: {
        fontSize: '1.875rem',
        fontWeight: '700',
        color: lightColors.text,
        textAlign: 'center',
        marginBottom: '0.5rem',
      },

      headerSubtitle: {
        fontSize: '1rem',
        color: lightColors.textSecondary,
        textAlign: 'center',
        marginBottom: '2rem',
      },
    
    // Primary action button
    formButtonPrimary: {
      backgroundColor: '#1D4ED8',
      color: '#ffffff',
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '600',
      border: 'none',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.25)',
      width: '100%',
      height: '3rem',
      
      '&:hover': {
        backgroundColor: '#1E40AF',
        transform: 'scale(1.02)',
        boxShadow: '0 8px 25px 0 rgb(29 78 216 / 0.35)',
      },
      
      '&:focus': {
        outline: '2px solid #1D4ED8',
        outlineOffset: '2px',
        transform: 'scale(1.02)',
      },
      
      '&:active': {
        transform: 'scale(0.98)',
      },
    },
    
    // Secondary/outline buttons
    formButtonSecondary: {
      backgroundColor: 'transparent',
      color: lightColors.text,
      border: `2px solid ${lightColors.border}`,
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      width: '100%',
      height: '3rem',

      '&:hover': {
        borderColor: '#1D4ED8',
        color: '#1D4ED8',
        transform: 'scale(1.02)',
        boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.15)',
      },
    },
    
    // Social buttons (Apple, Google)
    socialButtonsBlockButton: {
      backgroundColor: lightColors.background,
      color: lightColors.text,
      border: `2px solid ${lightColors.border}`,
      borderRadius: '0.75rem',
      padding: '0.75rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      width: '100%',
      height: '3rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '0.75rem',
      marginBottom: '0.75rem',

      '&:hover': {
        borderColor: '#1D4ED8',
        transform: 'scale(1.02)',
        boxShadow: '0 4px 14px 0 rgb(29 78 216 / 0.15)',
      },

      '&:focus': {
        outline: '2px solid #1D4ED8',
        outlineOffset: '2px',
      },
    },
    
      // Input fields - CRITICAL for visibility
      formFieldInput: {
        backgroundColor: lightColors.inputBackground,
        color: lightColors.text,
        border: `2px solid ${lightColors.border}`,
        borderRadius: '0.75rem',
        padding: '0.75rem 1rem',
        fontSize: '1rem',
        width: '100%',
        height: '3rem',
        transition: 'all 0.2s ease',

        '&:focus': {
          borderColor: '#1D4ED8',
          outline: 'none',
          boxShadow: '0 0 0 3px rgb(29 78 216 / 0.1)',
          backgroundColor: lightColors.inputBackground,
        },

        '&::placeholder': {
          color: lightColors.textMuted,
        },
      },

      // Form field labels - CRITICAL for visibility
      formFieldLabel: {
        fontSize: '0.875rem',
        fontWeight: '600',
        color: lightColors.text,
        marginBottom: '0.5rem',
        display: 'block',
      },
    
      // Links - Maintain brand color for visibility
      formFieldAction: {
        color: '#1D4ED8',
        fontSize: '0.875rem',
        fontWeight: '500',
        textDecoration: 'none',
        transition: 'color 0.2s ease',

        '&:hover': {
          color: '#1E40AF',
          textDecoration: 'underline',
        },
      },

      // Footer links
      footerActionLink: {
        color: '#1D4ED8',
        fontSize: '0.875rem',
        fontWeight: '500',
        textDecoration: 'none',
        transition: 'color 0.2s ease',

        '&:hover': {
          color: '#1E40AF',
          textDecoration: 'underline',
        },
      },
    
    // Divider
    dividerLine: {
      backgroundColor: lightColors.border,
      height: '1px',
      margin: '1.5rem 0',
    },

    dividerText: {
      color: lightColors.textSecondary,
      fontSize: '0.875rem',
      fontWeight: '500',
    },
    
    // Loading spinner
    spinner: {
      color: '#1D4ED8',
      width: '1.5rem',
      height: '1.5rem',
    },
    
    // Error messages - Enhanced contrast
    formFieldErrorText: {
      color: '#DC2626', // Updated to match design system error color
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '0.5rem',
    },
    
    // Success messages
    formFieldSuccessText: {
      color: '#10B981',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginTop: '0.5rem',
    },
    
    // Alert/notification styling
    alert: {
      backgroundColor: lightColors.backgroundSecondary,
      border: `1px solid ${lightColors.border}`,
      borderRadius: '0.75rem',
      padding: '1rem',
      marginBottom: '1rem',
    },

      // Alert and error text - CRITICAL for visibility
      alertText: {
        color: lightColors.text,
        fontSize: '0.875rem',
        lineHeight: '1.5',
      },
    },
};
