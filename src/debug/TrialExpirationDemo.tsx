import React, { useState } from 'react';
// import { TrialExpiredPrompt } from '../components/subscription/TrialExpiredPrompt'; // TODO: Use for trial expiration demo
import { SubscriptionGate } from '../components/subscription/SubscriptionGate';
import { PrimaryButton, SecondaryButton, Heading2, BodyText, Card } from '../components/ui';

/**
 * Demo component to showcase trial expiration behavior
 * This component demonstrates different trial expiration states and UI components
 */
export const TrialExpirationDemo: React.FC = () => {
  const [demoVariant, setDemoVariant] = useState<'banner' | 'modal' | 'page'>('banner');
  const [showModal, setShowModal] = useState(false);

  return (
    <div className="min-h-screen bg-jobblogg-surface p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <Heading2 className="mb-4">Trial Expiration Demo</Heading2>
          <BodyText className="text-jobblogg-text-medium">
            This demo showcases the different trial expiration UI components and behaviors.
          </BodyText>
        </div>

        {/* Demo Controls */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Demo Controls</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Trial Expiration Prompt Variant:</label>
              <div className="flex gap-2">
                <button
                  onClick={() => setDemoVariant('banner')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    demoVariant === 'banner'
                      ? 'bg-jobblogg-primary text-white'
                      : 'bg-jobblogg-surface text-jobblogg-text-medium hover:bg-jobblogg-primary-soft'
                  }`}
                >
                  Banner
                </button>
                <button
                  onClick={() => setDemoVariant('page')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    demoVariant === 'page'
                      ? 'bg-jobblogg-primary text-white'
                      : 'bg-jobblogg-surface text-jobblogg-text-medium hover:bg-jobblogg-primary-soft'
                  }`}
                >
                  Full Page
                </button>
                <button
                  onClick={() => setShowModal(true)}
                  className="px-4 py-2 rounded-lg text-sm font-medium bg-jobblogg-surface text-jobblogg-text-medium hover:bg-jobblogg-primary-soft transition-colors"
                >
                  Show Modal
                </button>
              </div>
            </div>
          </div>
        </Card>

        {/* Banner Demo */}
        {demoVariant === 'banner' && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Banner Variant</h3>
            <BodyText className="mb-4 text-jobblogg-text-medium">
              This banner appears at the top of pages when the trial has expired but the user is still in the grace period.
            </BodyText>
            
            {/* Simulated banner */}
            <div className="bg-jobblogg-warning-soft border-l-4 border-jobblogg-warning rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <svg className="w-6 h-6 text-jobblogg-warning mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-jobblogg-warning mb-1">
                      Prøveperioden er utløpt
                    </h3>
                    <p className="text-sm text-jobblogg-text-medium">
                      Du har 2 dager igjen med begrenset tilgang. Oppgrader nå for å fortsette å bruke alle funksjoner.
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  <PrimaryButton className="text-sm px-4 py-2">
                    Oppgrader nå
                  </PrimaryButton>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Page Demo */}
        {demoVariant === 'page' && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Full Page Variant</h3>
            <BodyText className="mb-4 text-jobblogg-text-medium">
              This full-page replacement appears when users try to access features that require an active subscription.
            </BodyText>
            
            <div className="bg-jobblogg-surface rounded-lg p-8 text-center">
              <div className="w-20 h-20 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              
              <Heading2 className="mb-4">Prøveperioden er utløpt</Heading2>
              <BodyText className="text-jobblogg-text-medium mb-6">
                Du har 2 dager igjen med begrenset tilgang til JobbLogg. 
                Oppgrader til en betalt plan for å fortsette å bruke alle funksjoner.
              </BodyText>
              
              <div className="bg-white rounded-lg p-4 border border-jobblogg-border mb-6 text-left">
                <p className="text-sm text-jobblogg-text-muted">
                  <strong>Hva du fortsatt kan gjøre:</strong><br />
                  • Se eksisterende prosjekter (kun lesing)<br />
                  • Laste ned prosjektdata<br />
                  • Se samtaler og meldinger
                </p>
              </div>

              <div className="flex gap-3 justify-center">
                <PrimaryButton>Se abonnementsplaner</PrimaryButton>
                <SecondaryButton>Gå til dashboard</SecondaryButton>
              </div>
            </div>
          </Card>
        )}

        {/* Subscription Gate Demo */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Subscription Gate Examples</h3>
          <BodyText className="mb-4 text-jobblogg-text-medium">
            These examples show how different features are gated based on subscription status.
          </BodyText>
          
          <div className="space-y-4">
            <div className="p-4 border border-jobblogg-border rounded-lg">
              <h4 className="font-medium mb-2">Create Project (Blocked for expired trials)</h4>
              <SubscriptionGate feature="create_project">
                <PrimaryButton>Opprett nytt prosjekt</PrimaryButton>
              </SubscriptionGate>
            </div>
            
            <div className="p-4 border border-jobblogg-border rounded-lg">
              <h4 className="font-medium mb-2">View Projects (Read-only for expired trials)</h4>
              <SubscriptionGate feature="view_projects">
                <div className="text-sm text-jobblogg-text-medium">
                  Project list would appear here with read-only access during grace period.
                </div>
              </SubscriptionGate>
            </div>
            
            <div className="p-4 border border-jobblogg-border rounded-lg">
              <h4 className="font-medium mb-2">Full Access Features (Blocked for expired trials)</h4>
              <SubscriptionGate feature="full_access">
                <div className="text-sm text-jobblogg-text-medium">
                  Advanced features like team management, customer sharing, etc.
                </div>
              </SubscriptionGate>
            </div>
          </div>
        </Card>

        {/* Implementation Notes */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Implementation Notes</h3>
          <div className="space-y-3 text-sm text-jobblogg-text-medium">
            <p>
              <strong>Trial Lifecycle:</strong> Active Trial (7 days) → Grace Period (3 days) → Canceled
            </p>
            <p>
              <strong>Access Control:</strong> Full access → Read-only → Minimal access
            </p>
            <p>
              <strong>User Experience:</strong> Progress indicators → Warning banners → Upgrade prompts
            </p>
            <p>
              <strong>Data Retention:</strong> All data preserved throughout trial lifecycle
            </p>
          </div>
        </Card>
      </div>

      {/* Modal Demo */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="max-w-md w-full">
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              
              <div>
                <Heading2 className="mb-3">Prøveperioden er utløpt</Heading2>
                <BodyText className="text-jobblogg-text-medium">
                  Du har 2 dager igjen med begrenset tilgang til JobbLogg. 
                  Oppgrader til en betalt plan for å fortsette å bruke alle funksjoner.
                </BodyText>
              </div>

              <div className="space-y-3">
                <PrimaryButton className="w-full">
                  Se abonnementsplaner
                </PrimaryButton>
                <SecondaryButton onClick={() => setShowModal(false)} className="w-full">
                  Lukk
                </SecondaryButton>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};
