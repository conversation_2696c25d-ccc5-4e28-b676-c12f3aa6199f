# JobbLogg Offline Functionality Testing Guide

## 🧪 **Testing Environment Setup**

### **Prerequisites**
1. **Development Server Running**: `npm run dev` or `yarn dev`
2. **Browser DevTools Open**: F12 or right-click → Inspect
3. **Network Tab Available**: For simulating offline conditions
4. **Application Tab Available**: For inspecting storage and service workers

### **Recommended Testing Browsers**
- **Chrome/Edge**: Best PWA support and debugging tools
- **Firefox**: Good PWA support with different behavior patterns
- **Safari**: iOS PWA testing (if on Mac)
- **Mobile browsers**: Chrome Mobile, Safari Mobile

### **Testing Tools Setup**
1. **Chrome DevTools**:
   - Open DevTools (F12)
   - Go to **Application** tab
   - Check **Service Workers** section
   - Go to **Network** tab for offline simulation

2. **Storage Inspection**:
   - **Application** → **Local Storage** → `localhost:5173`
   - **Application** → **IndexedDB** → Look for `jobblogg-offline-*` databases
   - **Application** → **Storage** → Check usage

## 🔧 **How to Simulate Offline Mode**

### **Method 1: Chrome DevTools Network Tab**
1. Open DevTools → **Network** tab
2. Click **Network conditions** (or **Throttling** dropdown)
3. Select **Offline** or check **Offline** checkbox
4. Page will now behave as if offline

### **Method 2: Chrome DevTools Application Tab**
1. Open DevTools → **Application** tab
2. Go to **Service Workers** section
3. Check **Offline** checkbox
4. This simulates offline for the current tab only

### **Method 3: System Network Disconnection**
1. Disconnect WiFi/Ethernet
2. Or use airplane mode on mobile devices
3. Most realistic testing method

### **Method 4: Browser Extensions**
- Install "Offline" or "Network Throttling" extensions
- Provides quick toggle for offline testing

## 📋 **Comprehensive Testing Scenarios**

### **Phase 1: Initial Setup and Authentication Testing**

#### **Test 1.1: First-Time User (Online)**
```
✅ EXPECTED BEHAVIOR:
- User can register/login normally
- Offline consent modal should NOT appear yet
- PWA install banner shows honest messaging
- No offline data should exist

🧪 STEPS:
1. Clear all browser data (localStorage, IndexedDB, cookies)
2. Navigate to JobbLogg
3. Complete authentication flow
4. Verify no offline consent modal appears
5. Check PWA status indicator shows "Online"
```

#### **Test 1.2: Offline Consent Flow**
```
✅ EXPECTED BEHAVIOR:
- Consent modal appears when going offline after authentication
- User can accept or decline offline functionality
- Accepting enables offline features
- Declining keeps app online-only

🧪 STEPS:
1. Complete Test 1.1 (authenticated user)
2. Go offline using DevTools
3. Verify offline consent modal appears
4. Test both "Accept" and "Decline" flows
5. Check localStorage for 'jobblogg-gdpr-consent'
6. Verify PWA status indicator reflects choice
```

### **Phase 2: Offline Project Management Testing**

#### **Test 2.1: Create Project Offline**
```
✅ EXPECTED BEHAVIOR:
- Can create projects when offline
- Projects stored with encryption
- Sync status shows "pending"
- Projects appear in Dashboard with offline indicators

🧪 STEPS:
1. Ensure offline consent is granted
2. Go offline
3. Navigate to "Create Project"
4. Verify OfflineFeatureGuard allows access
5. Fill out project form completely
6. Submit project
7. Verify success message
8. Check Dashboard shows new project with "Venter" status
9. Inspect localStorage for encrypted project data
```

#### **Test 2.2: View Offline Projects in Dashboard**
```
✅ EXPECTED BEHAVIOR:
- Dashboard shows both online and offline projects
- Offline projects have visual indicators
- Sync status is clearly displayed
- No duplicate projects appear

🧪 STEPS:
1. Create 2-3 projects offline (from Test 2.1)
2. Go back online
3. Create 1-2 projects online
4. Go offline again
5. Verify Dashboard shows all projects
6. Check offline projects have status indicators
7. Verify no duplicates exist
8. Test project card interactions
```

#### **Test 2.3: Project Sync When Coming Online**
```
✅ EXPECTED BEHAVIOR:
- Offline projects sync automatically when online
- Sync status updates to "syncing" then "synced"
- Projects become available online
- No data loss occurs

🧪 STEPS:
1. Create projects offline
2. Verify they show "pending" status
3. Go back online
4. Watch sync status change to "syncing"
5. Wait for sync completion ("synced")
6. Refresh page and verify projects persist
7. Check Convex dashboard for synced projects
```

### **Phase 3: Offline Image Handling Testing**

#### **Test 3.1: Upload Images Offline**
```
✅ EXPECTED BEHAVIOR:
- Can select and upload images when offline
- Images stored encrypted in IndexedDB
- Upload queue shows pending uploads
- Thumbnails generate correctly

🧪 STEPS:
1. Go offline with consent granted
2. Create a project or project log
3. Try to upload images (various formats: JPG, PNG, HEIC)
4. Verify images appear immediately
5. Check IndexedDB for encrypted image data
6. Verify thumbnails generate
7. Check upload queue in offline settings
```

#### **Test 3.2: Image Sync and Upload Queue**
```
✅ EXPECTED BEHAVIOR:
- Images upload automatically when online
- Upload queue processes in order
- Failed uploads retry with backoff
- Sync status updates correctly

🧪 STEPS:
1. Upload 3-5 images offline
2. Verify they appear in upload queue
3. Go back online
4. Monitor upload progress
5. Verify images sync to server
6. Check upload queue empties
7. Test with large images (>2MB)
```

### **Phase 4: Storage and Security Testing**

#### **Test 4.1: Encryption Verification**
```
✅ EXPECTED BEHAVIOR:
- All offline data is encrypted
- Cannot read data without proper keys
- Different users have isolated data
- Encryption keys derive from user auth

🧪 STEPS:
1. Create offline data as User A
2. Check localStorage - data should be encrypted
3. Try to decrypt manually (should fail)
4. Logout and login as User B
5. Verify User A's data is not accessible
6. Check encryption keys are different
```

#### **Test 4.2: Storage Usage and Limits**
```
✅ EXPECTED BEHAVIOR:
- Storage usage tracked accurately
- Warnings appear near limits
- Cleanup tools work correctly
- No storage quota exceeded errors

🧪 STEPS:
1. Create many projects offline (10-20)
2. Upload many images (test storage limits)
3. Open Offline Settings Panel
4. Verify storage usage display is accurate
5. Test "Clear Offline Data" functionality
6. Verify storage usage resets
```

### **Phase 5: GDPR and Privacy Testing**

#### **Test 5.1: Consent Management**
```
✅ EXPECTED BEHAVIOR:
- Consent can be revoked at any time
- Revoking consent deletes all data
- Data processing info is transparent
- Consent expires after 1 year

🧪 STEPS:
1. Grant offline consent
2. Create offline data
3. Open Offline Settings Panel
4. Click "Revoke Consent"
5. Verify all offline data is deleted
6. Verify localStorage is cleared
7. Check IndexedDB is cleared
8. Test consent expiration (modify timestamp)
```

#### **Test 5.2: Data Deletion and Cleanup**
```
✅ EXPECTED BEHAVIOR:
- Logout clears all offline data
- Manual deletion works correctly
- No data remnants remain
- Fresh login starts clean

🧪 STEPS:
1. Create extensive offline data
2. Logout completely
3. Check all storage is cleared
4. Login again
5. Verify no old data appears
6. Test manual "Delete Offline Data"
7. Verify selective deletion works
```

### **Phase 6: Edge Cases and Error Handling**

#### **Test 6.1: Network Interruption During Sync**
```
✅ EXPECTED BEHAVIOR:
- Graceful handling of network interruptions
- Sync resumes when connection restored
- No data corruption occurs
- Clear error messaging

🧪 STEPS:
1. Create offline data
2. Go online to start sync
3. Quickly go offline during sync
4. Go back online
5. Verify sync resumes correctly
6. Check for any corrupted data
7. Test multiple interruption cycles
```

#### **Test 6.2: Storage Quota Exceeded**
```
✅ EXPECTED BEHAVIOR:
- Graceful handling of storage limits
- Clear error messages
- Cleanup suggestions provided
- App remains functional

🧪 STEPS:
1. Fill storage with large images
2. Try to create more offline data
3. Verify error handling
4. Check error messages are helpful
5. Test cleanup functionality
6. Verify app recovers correctly
```

#### **Test 6.3: Corrupted Data Recovery**
```
✅ EXPECTED BEHAVIOR:
- Corrupted data detected and handled
- Graceful fallback to online mode
- Clear error messaging
- Data recovery options provided

🧪 STEPS:
1. Create offline data
2. Manually corrupt localStorage data
3. Refresh application
4. Verify error handling
5. Check fallback behavior
6. Test data recovery options
```

### **Phase 7: User Experience Testing**

#### **Test 7.1: Status Indicators and Feedback**
```
✅ EXPECTED BEHAVIOR:
- Clear online/offline status always visible
- Sync progress clearly communicated
- Error states have helpful messages
- Loading states are appropriate

🧪 STEPS:
1. Test PWA status indicator in all states
2. Verify offline mode banner appears
3. Check project sync status indicators
4. Test loading states during sync
5. Verify error messages are helpful
6. Check accessibility of indicators
```

#### **Test 7.2: Feature Availability Management**
```
✅ EXPECTED BEHAVIOR:
- Features disabled gracefully when unavailable
- Clear messaging about feature availability
- No broken functionality when offline
- Smooth transitions between modes

🧪 STEPS:
1. Test all major features offline
2. Verify OfflineFeatureGuard works
3. Check disabled features show helpful messages
4. Test transitions between online/offline
5. Verify no JavaScript errors occur
6. Check mobile responsiveness
```

## 🔍 **Debugging and Inspection Tools**

### **Chrome DevTools Inspection**
```javascript
// Console commands for debugging

// Check offline storage status
localStorage.getItem('jobblogg-gdpr-consent')
localStorage.getItem('jobblogg-offline-enabled')

// Inspect encrypted data
Object.keys(localStorage).filter(key => key.startsWith('jobblogg-'))

// Check IndexedDB
// Go to Application → IndexedDB → jobblogg-offline-images-[userId]

// Service Worker status
navigator.serviceWorker.getRegistrations()

// Check encryption status
// Look for 'OfflineEncryption initialized' in console
```

### **Network Monitoring**
```
Monitor these network requests:
- Convex API calls (should fail gracefully offline)
- Image uploads (should queue offline)
- Authentication requests (should cache sessions)
- Service worker updates
```

### **Storage Inspection**
```
Check these storage locations:
- localStorage: jobblogg-* keys
- IndexedDB: jobblogg-offline-images-* databases
- Service Worker cache: jobblogg-* caches
- Session storage: temporary data
```

## ✅ **Testing Checklist**

### **Basic Functionality**
- [ ] PWA installs correctly
- [ ] Service worker caches assets
- [ ] Offline consent flow works
- [ ] Projects can be created offline
- [ ] Images can be uploaded offline
- [ ] Dashboard shows offline projects
- [ ] Sync works when coming online

### **Security & Privacy**
- [ ] Data is encrypted in storage
- [ ] GDPR consent is properly managed
- [ ] Data deletion works completely
- [ ] User isolation is maintained
- [ ] Logout clears all data

### **Error Handling**
- [ ] Network interruptions handled gracefully
- [ ] Storage limits handled properly
- [ ] Corrupted data recovery works
- [ ] Error messages are helpful
- [ ] App remains stable in all scenarios

### **User Experience**
- [ ] Status indicators are clear
- [ ] Feature availability is obvious
- [ ] Transitions are smooth
- [ ] Mobile experience is good
- [ ] Accessibility is maintained

## 🚨 **Common Issues and Solutions**

### **Issue: Offline consent modal doesn't appear**
```
Solution: 
1. Check user is authenticated
2. Verify going offline after authentication
3. Check localStorage for existing consent
4. Clear browser data and retry
```

### **Issue: Projects don't sync when online**
```
Solution:
1. Check network connectivity
2. Verify Convex API is accessible
3. Check browser console for errors
4. Inspect sync queue in localStorage
```

### **Issue: Images don't upload offline**
```
Solution:
1. Verify IndexedDB is supported
2. Check storage quota availability
3. Verify encryption is initialized
4. Check browser console for errors
```

### **Issue: Data appears corrupted**
```
Solution:
1. Clear all offline data
2. Revoke and re-grant consent
3. Check for browser compatibility issues
4. Verify encryption keys are valid
```

This comprehensive testing guide will help you thoroughly validate JobbLogg's offline functionality across all scenarios and edge cases.
