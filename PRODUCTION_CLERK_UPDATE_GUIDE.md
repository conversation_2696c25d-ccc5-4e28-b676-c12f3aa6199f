# 🔐 Oppdatering av Clerk Live API-nøkler - Komplett Guide

## 📋 Forberedelser (g<PERSON><PERSON><PERSON> <PERSON><PERSON> først)

### 1. Få Live Clerk-nøkkel
1. Gå til https://dashboard.clerk.com/
2. Velg ditt JobbLogg-prosjekt
3. G<PERSON> til **API Keys**
4. Kopier **Publishable key** som starter med `pk_live_`
5. Lagre denne nøkkelen trygt

### 2. <PERSON><PERSON><PERSON> script til server
```bash
# Fra din lokale maskin (JobbLogg-mappen)
scp update-production-clerk-keys.sh root@jobblogg-prod-server:/root/
```

## 🚀 Utførelse på produksjonsserver

### Steg 1: SSH til server
```bash
ssh root@jobblogg-prod-server
```

### Steg 2: Kj<PERSON>r oppdateringsscript
```bash
chmod +x /root/update-production-clerk-keys.sh
/root/update-production-clerk-keys.sh
```

**Scriptet vil:**
- ✅ Hente siste kode fra Git
- ✅ Stoppe eksisterende container
- ✅ Spørre deg om live Clerk-nøkkel
- ✅ Bygge ny Docker image
- ✅ Starte ny container
- ✅ Kjøre health check

### Steg 3: Test at alt fungerer
1. Gå til https://jobblogg.no
2. Test innlogging
3. Verifiser at Clerk fungerer

## 🔄 Hvis noe går galt (Rollback)

```bash
# Stopp ny container
docker stop jobblogg-frontend-production
docker rm jobblogg-frontend-production

# Start forrige versjon (hvis den finnes)
docker run -d \
  --name jobblogg-frontend-production \
  --restart unless-stopped \
  -p 3000:3000 \
  jobblogg-frontend-production:previous
```

## 📊 Overvåking

```bash
# Sjekk container status
docker ps | grep jobblogg

# Sjekk logger
docker logs jobblogg-frontend-production -f

# Test health endpoint
curl http://localhost:3000/health
```

## ⚠️ Viktige punkter

1. **Backup**: Den gamle containeren stoppes, men imaget beholdes
2. **Testing**: Test grundig at innlogging fungerer
3. **Monitoring**: Overvåk logger i noen minutter etter deployment
4. **Support**: Hvis problemer oppstår, kontakt meg med feilmeldinger

## 📞 Hvis du trenger hjelp

Send meg:
- Feilmeldinger fra terminalen
- Docker logs output
- Beskrivelse av hva som ikke fungerer

Jeg kan da hjelpe deg med feilsøking og løsninger!
