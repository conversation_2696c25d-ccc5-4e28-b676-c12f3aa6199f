# 🚀 Rask Clerk Live API-n<PERSON><PERSON>kel Oppdatering

## 📋 Steg 1: <PERSON><PERSON> din Live Clerk-nøkkel
1. Gå til https://dashboard.clerk.com/
2. Velg ditt JobbLogg-prosjekt  
3. Gå til **API Keys**
4. Kopier **Publishable key** som starter med `pk_live_`

## 🔐 Steg 2: SSH til server og kjør kommandoer

### Koble til server:
```bash
ssh <EMAIL>
```

### Kjør disse kommandoene (en og en):

```bash
# 1. Gå til riktig mappe
cd /root/JobbLogg

# 2. Hent siste kode
git pull origin main

# 3. Stopp gammel container
docker stop jobblogg-frontend-production 2>/dev/null || true
docker rm jobblogg-frontend-production 2>/dev/null || true

# 4. Sett din live Clerk-nøkkel (erstatt med din ekte nøkkel)
export LIVE_CLERK_KEY="pk_live_DIN_EKTE_CLERK_NØKKEL_HER"

# 5. Bygg ny image med live nøkler
docker build --no-cache \
  --target production \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY="$LIVE_CLERK_KEY" \
  --build-arg VITE_CONVEX_URL="https://api.jobblogg.no" \
  --build-arg VITE_GOOGLE_MAPS_API_KEY="AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs" \
  --build-arg VITE_STRIPE_PUBLISHABLE_KEY="pk_test_51QuHVWRqXwHRnsDwFyefP4DJfEDG9Ti42UkWO7Y5nWmSDZbZtVLWfgDmmAP3YnYYb8905qIhtDDB8UUPLDjaUk9F00snevRBNh" \
  -f docker/frontend/Dockerfile.fixed \
  -t jobblogg-frontend-production:latest .

# 6. Start ny container
docker run -d \
  --name jobblogg-frontend-production \
  --restart unless-stopped \
  -p 3000:3000 \
  jobblogg-frontend-production:latest

# 7. Sjekk status
docker ps | grep jobblogg-frontend-production
```

## ✅ Steg 3: Test at det fungerer
1. Gå til https://jobblogg.no
2. Test innlogging med Clerk
3. Verifiser at alt fungerer

## 📊 Overvåking og feilsøking

```bash
# Sjekk container logger
docker logs jobblogg-frontend-production -f

# Sjekk container status
docker ps

# Test health endpoint
curl http://localhost:3000/health
```

## 🔄 Hvis noe går galt (Rollback)

```bash
# Stopp ny container
docker stop jobblogg-frontend-production
docker rm jobblogg-frontend-production

# List tilgjengelige images
docker images | grep jobblogg

# Start forrige versjon (hvis den finnes)
docker run -d \
  --name jobblogg-frontend-production \
  --restart unless-stopped \
  -p 3000:3000 \
  [FORRIGE_IMAGE_ID]
```

## ⚠️ Viktige punkter

1. **Erstatt `pk_live_DIN_EKTE_CLERK_NØKKEL_HER`** med din ekte live Clerk-nøkkel
2. **Test grundig** at innlogging fungerer etter oppdateringen
3. **Overvåk logger** i noen minutter etter deployment
4. **Ta backup** av nåværende image før du starter (gjøres automatisk)

## 📞 Hvis du trenger hjelp

Send meg:
- Feilmeldinger fra terminalen
- Output fra `docker logs jobblogg-frontend-production`
- Beskrivelse av hva som ikke fungerer
