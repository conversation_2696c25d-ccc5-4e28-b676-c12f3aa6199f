/**
 * JobbLogg PWA & Push Notification Testing Script
 *
 * Run this in the browser console to test PWA and push notification functionality
 * Usage: Copy and paste sections into browser console while testing
 */

// =============================================================================
// TESTING UTILITIES
// =============================================================================

const PWATestUtils = {
  // Check PWA and notification status
  checkPWAStatus() {
    console.log('🔍 Checking PWA Status...');
    console.log('Navigator online:', navigator.onLine);
    console.log('Service Worker supported:', 'serviceWorker' in navigator);
    console.log('Push Manager supported:', 'PushManager' in window);
    console.log('Notification supported:', 'Notification' in window);
    console.log('Notification permission:', Notification.permission);

    // Check PWA installation
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    console.log('PWA installed (standalone):', isStandalone);

    // Check service worker registration
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        console.log('Service Worker registrations:', registrations.length);
        registrations.forEach((reg, i) => {
          console.log(`  ${i + 1}. ${reg.scope}`);
        });
      });
    }

    return {
      isOnline: navigator.onLine,
      hasServiceWorker: 'serviceWorker' in navigator,
      hasPushManager: 'PushManager' in window,
      hasNotifications: 'Notification' in window,
      notificationPermission: Notification.permission,
      isInstalled: isStandalone
    };
  },

  // Test push notifications
  async testPushNotifications() {
    console.log('🔔 Testing Push Notifications...');

    if (!('Notification' in window)) {
      console.log('❌ Notifications not supported');
      return false;
    }

    console.log('Current permission:', Notification.permission);

    if (Notification.permission === 'default') {
      console.log('� Requesting notification permission...');
      const permission = await Notification.requestPermission();
      console.log('Permission result:', permission);
    }

    if (Notification.permission === 'granted') {
      console.log('✅ Testing local notification...');

      // Test basic notification
      new Notification('🧪 JobbLogg Test', {
        body: 'Push notifications are working!',
        icon: '/icons/icon-192x192.svg',
        tag: 'test-notification'
      });

      return true;
    } else {
      console.log('❌ Notification permission denied');
      return false;
    }
  },

  // Test service worker push
  async testServiceWorkerPush() {
    console.log('📱 Testing Service Worker Push...');

    if (!('serviceWorker' in navigator)) {
      console.log('❌ Service Worker not supported');
      return false;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      console.log('✅ Service Worker ready');

      // Test service worker notification
      await registration.showNotification('🧪 JobbLogg SW Test', {
        body: 'Service Worker notifications working!',
        icon: '/icons/icon-192x192.svg',
        badge: '/icons/icon-192x192.svg',
        actions: [
          { action: 'view', title: 'Vis' },
          { action: 'dismiss', title: 'Lukk' }
        ],
        data: { url: '/', type: 'test' },
        tag: 'sw-test'
      });

      console.log('✅ Service Worker notification sent');
      return true;
    } catch (error) {
      console.error('❌ Service Worker push failed:', error);
      return false;
    }
  },

  // Simulate going offline
  simulateOffline() {
    console.log('📱 Simulating offline mode...');
    console.log('Use Chrome DevTools → Network → Offline checkbox');
    console.log('Or Application → Service Workers → Offline checkbox');
  },

  // Clear all offline data
  clearAllOfflineData() {
    console.log('🗑️ Clearing all offline data...');
    
    const offlineKeys = Object.keys(localStorage).filter(key => key.startsWith('jobblogg-'));
    offlineKeys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`Removed: ${key}`);
    });
    
    // Clear IndexedDB
    if ('indexedDB' in window) {
      indexedDB.databases().then(databases => {
        databases.forEach(db => {
          if (db.name?.includes('jobblogg')) {
            indexedDB.deleteDatabase(db.name);
            console.log(`Deleted IndexedDB: ${db.name}`);
          }
        });
      });
    }
    
    console.log('✅ All offline data cleared');
  },

  // Test encryption
  testEncryption() {
    console.log('🔐 Testing encryption...');
    
    const encryptedKeys = Object.keys(localStorage).filter(key => 
      key.includes('encrypted') || key.includes('offline-data')
    );
    
    encryptedKeys.forEach(key => {
      const data = localStorage.getItem(key);
      try {
        const parsed = JSON.parse(data);
        if (parsed.encrypted && parsed.userId) {
          console.log(`✅ ${key}: Properly encrypted`);
          console.log(`   User ID: ${parsed.userId.substring(0, 8)}...`);
          console.log(`   Timestamp: ${new Date(parsed.consentTimestamp).toLocaleString()}`);
        } else {
          console.log(`❌ ${key}: Not properly encrypted`);
        }
      } catch (e) {
        console.log(`❌ ${key}: Invalid JSON`);
      }
    });
  }
};

// =============================================================================
// AUTOMATED TEST SCENARIOS
// =============================================================================

const PWATests = {
  // Test 1: PWA Installation and Setup
  async testPWASetup() {
    console.log('\n🧪 TEST 1: PWA Installation and Setup');
    console.log('=====================================');

    const status = PWATestUtils.checkPWAStatus();

    if (!status.hasServiceWorker) {
      console.log('❌ Service Worker not supported');
      return false;
    }

    if (!status.hasNotifications) {
      console.log('❌ Notifications not supported');
      return false;
    }

    console.log('✅ PWA features supported');
    console.log('💡 Try installing the PWA from the browser menu or install banner');
    return true;
  },

  // Test 2: Push Notification Setup
  async testPushNotificationSetup() {
    console.log('\n🧪 TEST 2: Push Notification Setup');
    console.log('=====================================');

    const success = await PWATestUtils.testPushNotifications();

    if (success) {
      console.log('✅ Push notifications working');
      console.log('💡 Check if you received the test notification');

      // Test service worker notifications too
      setTimeout(async () => {
        await PWATestUtils.testServiceWorkerPush();
      }, 2000);
    } else {
      console.log('❌ Push notifications not working');
      console.log('💡 Check notification permissions in browser settings');
    }

    return success;
  },

  // Test 3: Offline Feature Restrictions
  async testOfflineFeatureRestrictions() {
    console.log('\n🧪 TEST 3: Offline Feature Restrictions');
    console.log('=====================================');

    if (navigator.onLine) {
      console.log('❌ Test requires offline mode');
      console.log('💡 Go offline first using DevTools');
      return false;
    }

    console.log('✅ Offline mode detected');
    console.log('💡 Navigate to /create-project');
    console.log('💡 Should see: "Krever internettforbindelse" message');
    console.log('💡 Project creation should be blocked offline');
    console.log('💡 Reason: Requires Brønnøysundregisteret API integration');
    console.log('');
    console.log('💡 Also try clicking on existing projects in Dashboard');
    console.log('💡 Should see: "Krever internettforbindelse" message');
    console.log('💡 Reason: Requires Convex queries with valid auth tokens');

    // Check current URL
    console.log('Current URL:', window.location.href);

    // Look for offline restriction messages
    setTimeout(() => {
      const restrictionElements = document.querySelectorAll('*');
      const foundRestrictions = Array.from(restrictionElements).filter(el =>
        el.textContent?.includes('Krever internettforbindelse') ||
        el.textContent?.includes('Ikke tilgjengelig offline') ||
        el.textContent?.includes('Brønnøysundregisteret')
      );

      if (foundRestrictions.length > 0) {
        console.log('✅ Found offline restriction messages:', foundRestrictions.length);
      } else {
        console.log('❌ No offline restriction messages found');
      }
    }, 2000);
  },

  // Test 4: Sync when coming online
  async testSyncWhenOnline() {
    console.log('\n🧪 TEST 4: Sync When Coming Online');
    console.log('=====================================');
    
    if (!navigator.onLine) {
      console.log('❌ Test requires online mode');
      console.log('💡 Go online first');
      return false;
    }
    
    // Check for pending sync data
    const syncKeys = Object.keys(localStorage).filter(key => 
      key.includes('sync-queue') || key.includes('offline-data')
    );
    
    if (syncKeys.length === 0) {
      console.log('❌ No offline data to sync');
      console.log('💡 Create some data offline first');
      return false;
    }
    
    console.log('✅ Online mode detected');
    console.log('✅ Found offline data to sync');
    console.log('💡 Watch for sync status changes in the UI');
    
    // Monitor sync progress
    let syncAttempts = 0;
    const syncMonitor = setInterval(() => {
      syncAttempts++;
      console.log(`🔄 Sync attempt ${syncAttempts} - Check UI for status updates`);
      
      if (syncAttempts >= 10) {
        clearInterval(syncMonitor);
        console.log('⏰ Sync monitoring stopped after 10 attempts');
      }
    }, 2000);
  },

  // Test 5: Storage cleanup
  async testStorageCleanup() {
    console.log('\n🧪 TEST 5: Storage Cleanup');
    console.log('=====================================');
    
    const beforeSize = OfflineTestUtils.checkStorageUsage();
    
    console.log('💡 Now test the "Clear Offline Data" button in settings');
    console.log('💡 Or run: OfflineTestUtils.clearAllOfflineData()');
    
    setTimeout(() => {
      const afterSize = OfflineTestUtils.checkStorageUsage();
      
      if (afterSize < beforeSize) {
        console.log('✅ Storage cleanup successful');
        console.log(`📉 Reduced from ${(beforeSize/1024).toFixed(2)} KB to ${(afterSize/1024).toFixed(2)} KB`);
      } else {
        console.log('❌ Storage cleanup may have failed');
      }
    }, 3000);
  },

  // Run all tests
  async runAllTests() {
    console.log('🚀 Running All PWA Tests');
    console.log('=====================================');

    await this.testPWASetup();
    await this.testPushNotificationSetup();
    await this.testOfflineFeatureRestrictions();

    console.log('\n📊 Test Summary');
    console.log('=====================================');
    PWATestUtils.checkPWAStatus();
  }
};

// =============================================================================
// MANUAL TESTING HELPERS
// =============================================================================

const ManualTestHelpers = {
  // Create test project data
  createTestProject() {
    const testProject = {
      title: `Test Project ${Date.now()}`,
      description: 'This is a test project created for offline testing',
      status: 'active',
      createdAt: new Date().toISOString()
    };
    
    console.log('📝 Test project data:', testProject);
    console.log('💡 Use this data when creating projects manually');
    return testProject;
  },

  // Monitor network status changes
  monitorNetworkStatus() {
    console.log('📡 Monitoring network status changes...');
    
    window.addEventListener('online', () => {
      console.log('🌐 Network: ONLINE');
      console.log('💡 Good time to test sync functionality');
    });
    
    window.addEventListener('offline', () => {
      console.log('📱 Network: OFFLINE');
      console.log('💡 Good time to test offline functionality');
    });
    
    console.log('✅ Network status monitoring active');
  },

  // Check PWA installation
  checkPWAInstallation() {
    console.log('📱 Checking PWA installation status...');
    
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        console.log('Service Workers:', registrations.length);
        registrations.forEach((reg, index) => {
          console.log(`  ${index + 1}. ${reg.scope}`);
        });
      });
    }
    
    if (window.matchMedia('(display-mode: standalone)').matches) {
      console.log('✅ PWA is installed and running standalone');
    } else {
      console.log('ℹ️ PWA is running in browser mode');
    }
  }
};

// =============================================================================
// QUICK START GUIDE
// =============================================================================

console.log(`
🧪 JobbLogg PWA & Push Notification Testing Console
====================================================

Quick Start Commands:
• PWATestUtils.checkPWAStatus()              - Check PWA and notification status
• PWATestUtils.testPushNotifications()       - Test push notifications
• PWATestUtils.testServiceWorkerPush()       - Test service worker notifications
• PWATests.runAllTests()                     - Run all automated tests
• ManualTestHelpers.monitorNetworkStatus()   - Monitor network changes

Manual Testing Steps:
1. Install PWA from browser menu or install banner
2. Grant notification permissions when prompted
3. Test push notifications with test functions
4. Verify PWA works in standalone mode
5. Test notification click actions

Focus: Mobile-first PWA with push notifications for staying connected to projects
`);

// Auto-start network monitoring
ManualTestHelpers.monitorNetworkStatus();

// Export for global access
window.PWATestUtils = PWATestUtils;
window.PWATests = PWATests;
window.ManualTestHelpers = ManualTestHelpers;
