#!/bin/bash

# Script for å oppdatere staging-milj<PERSON><PERSON> på <PERSON>ner-serveren
# Kj<PERSON><PERSON> dette scriptet fra JobbLogg-mappen

echo "🚀 Oppdaterer staging-miljø på Hetzner-serveren..."

# Kopier oppdatert Docker Compose-fil til serveren
echo "📁 Kopierer docker-compose.staging-fixed.yml til serveren..."
scp docker-compose.staging-fixed.yml root@************:/opt/jobblogg-staging/

# Kopier oppdatert .env.staging til serveren
echo "📁 Kopierer .env.staging til serveren..."
scp .env.staging root@************:/opt/jobblogg-staging/

echo "🔧 Kobler til serveren for å starte staging-miljøet på nytt..."
ssh root@************ << 'EOF'
cd /opt/jobblogg-staging

echo "⏹️  Stopper eksisterende staging-containere..."
docker-compose -f docker-compose.staging-simple.yml down 2>/dev/null || true
docker-compose -f docker-compose.staging.yml down 2>/dev/null || true

echo "🏗️  Starter staging med oppdatert konfigurasjon..."
docker-compose -f docker-compose.staging-fixed.yml up -d --build

echo "⏳ Venter på at containeren skal starte..."
sleep 10

echo "🔍 Sjekker status..."
docker ps | grep staging

echo "🌐 Tester staging-miljøet..."
curl -I http://localhost:5175

echo "📋 Sjekker miljøvariabler i containeren..."
docker exec jobblogg-frontend-staging env | grep -E "(VITE_CONVEX_URL|CONVEX_DEPLOYMENT|VITE_CLERK|NODE_ENV)" | sort

echo "✅ Staging-miljø oppdatert!"
echo "🌍 Tilgjengelig på: http://************:5175"
EOF

echo "🎉 Ferdig! Staging-miljøet skal nå bruke enchanted-quail-174 Convex-deployment."
