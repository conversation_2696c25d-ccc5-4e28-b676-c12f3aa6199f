#!/bin/bash

# JobbLogg Port Conflict Fix Script
# This script resolves port conflicts during production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🔧 JobbLogg Port Conflict Resolution"
print_status "======================================"

# Function to stop container by name pattern
stop_container_by_pattern() {
    local pattern=$1
    local port=$2
    
    print_status "Checking for containers matching pattern: $pattern"
    
    # Find containers by name pattern
    containers=$(docker ps -a --format "{{.Names}}" | grep -E "$pattern" || true)
    
    if [ -n "$containers" ]; then
        print_warning "Found containers matching pattern '$pattern':"
        echo "$containers"
        
        for container in $containers; do
            print_status "Stopping container: $container"
            docker stop "$container" 2>/dev/null || true
            
            print_status "Removing container: $container"
            docker rm "$container" 2>/dev/null || true
        done
    else
        print_status "No containers found matching pattern: $pattern"
    fi
}

# Function to check what's using a port
check_port_usage() {
    local port=$1
    print_status "Checking what's using port $port..."
    
    # Check Docker containers using the port
    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep ":$port->" || true
    
    # Check system processes using the port
    lsof -i :$port 2>/dev/null || netstat -tlnp 2>/dev/null | grep ":$port " || true
}

# Function to force kill processes on port
force_kill_port() {
    local port=$1
    print_warning "Force killing processes on port $port..."
    
    # Find and kill processes using the port
    pids=$(lsof -ti :$port 2>/dev/null || true)
    if [ -n "$pids" ]; then
        print_status "Killing PIDs: $pids"
        kill -9 $pids 2>/dev/null || true
        sleep 2
    fi
}

print_status "Step 1: Stopping all JobbLogg containers"
print_status "=========================================="

# Stop containers with various naming patterns
stop_container_by_pattern "jobblogg-frontend.*" "5174"
stop_container_by_pattern "jobblogg-convex.*" "3210"
stop_container_by_pattern "mock-convex.*" "3211"

print_status "Step 2: Checking port usage"
print_status "============================"

# Check critical ports
for port in 5174 5175 3210 3211; do
    check_port_usage $port
done

print_status "Step 3: Force cleanup if needed"
print_status "==============================="

# Force kill processes on critical ports if they're still in use
for port in 5174 5175; do
    if lsof -i :$port >/dev/null 2>&1; then
        print_warning "Port $port is still in use, force killing..."
        force_kill_port $port
    fi
done

print_status "Step 4: Docker cleanup"
print_status "======================"

# Remove any dangling containers and networks
print_status "Removing dangling containers..."
docker container prune -f 2>/dev/null || true

print_status "Removing unused networks..."
docker network prune -f 2>/dev/null || true

# Remove specific networks that might conflict
for network in jobblogg-prod-network jobblogg-production-network jobblogg_jobblogg-network; do
    print_status "Removing network: $network"
    docker network rm "$network" 2>/dev/null || true
done

print_status "Step 5: Final verification"
print_status "=========================="

# Final check - ensure ports are free
for port in 5174 5175 3210 3211; do
    if lsof -i :$port >/dev/null 2>&1; then
        print_error "Port $port is still in use!"
        check_port_usage $port
    else
        print_success "Port $port is free"
    fi
done

print_status "Step 6: Container status"
print_status "======================="

print_status "Current running containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(jobblogg|mock-convex)" || echo "No JobbLogg containers running"

print_success "🎉 Port conflict resolution completed!"
print_status ""
print_status "You can now run the deployment:"
print_status "docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build"
