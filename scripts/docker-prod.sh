#!/bin/bash

# JobbLogg Docker Production Script
# This script helps manage the Docker production environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if environment file exists
check_env_file() {
    if [ ! -f ".env.prod" ]; then
        print_error ".env.prod not found. Please create production environment file."
        exit 1
    fi
}

# Function to check SSL certificates
check_ssl() {
    if [ ! -f "docker/nginx/ssl/cert.pem" ] || [ ! -f "docker/nginx/ssl/key.pem" ]; then
        print_warning "SSL certificates not found. HTTPS will not work."
        print_status "To generate self-signed certificates:"
        print_status "mkdir -p docker/nginx/ssl"
        print_status "openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout docker/nginx/ssl/key.pem -out docker/nginx/ssl/cert.pem"
    fi
}

# Function to deploy production environment
deploy() {
    print_status "Deploying JobbLogg production environment..."
    
    check_docker
    check_env_file
    check_ssl
    
    # Load environment variables
    export $(cat .env.prod | grep -v '^#' | xargs)
    
    # Build and start services
    docker compose -f docker-compose.prod.yml up --build -d
    
    print_success "Production environment deployed!"
    print_status "Application: https://your-domain.com"
    print_status ""
    print_status "To view logs: ./scripts/docker-prod.sh logs"
    print_status "To stop: ./scripts/docker-prod.sh stop"
}

# Function to stop production environment
stop() {
    print_status "Stopping JobbLogg production environment..."
    docker compose -f docker-compose.prod.yml down
    print_success "Production environment stopped!"
}

# Function to update production environment
update() {
    print_status "Updating JobbLogg production environment..."
    
    # Pull latest changes
    git pull origin main
    
    # Rebuild and restart
    docker compose -f docker-compose.prod.yml down
    docker compose -f docker-compose.prod.yml build --no-cache
    docker compose -f docker-compose.prod.yml up -d
    
    print_success "Production environment updated!"
}

# Function to view logs
view_logs() {
    if [ -z "$2" ]; then
        docker compose -f docker-compose.prod.yml logs -f
    else
        docker compose -f docker-compose.prod.yml logs -f "$2"
    fi
}

# Function to backup data
backup() {
    print_status "Creating backup of production data..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup Convex data
    docker run --rm -v jobblogg_convex_data_prod:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/convex_data.tar.gz -C /data .
    
    print_success "Backup created in $BACKUP_DIR"
}

# Function to show status
show_status() {
    print_status "JobbLogg Production Environment Status:"
    docker compose -f docker-compose.prod.yml ps
}

# Function to show help
show_help() {
    echo "JobbLogg Docker Production Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy    Deploy the production environment"
    echo "  stop      Stop the production environment"
    echo "  update    Update and restart the production environment"
    echo "  logs      View logs (optionally specify service: logs nginx)"
    echo "  backup    Create a backup of production data"
    echo "  status    Show service status"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 logs nginx"
    echo "  $0 backup"
}

# Main script logic
case "${1:-help}" in
    deploy)
        deploy
        ;;
    stop)
        stop
        ;;
    update)
        update
        ;;
    logs)
        view_logs "$@"
        ;;
    backup)
        backup
        ;;
    status)
        show_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
