#!/bin/bash

# 🔐 JobbLogg SSH Key Generator
# Generates SSH keys for GitHub Actions deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if SSH directory exists
check_ssh_dir() {
    if [[ ! -d ~/.ssh ]]; then
        print_status "Creating ~/.ssh directory..."
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
    fi
}

# Generate SSH key pair
generate_key() {
    local key_name=$1
    local comment=$2
    local key_path="$HOME/.ssh/$key_name"
    
    if [[ -f "$key_path" ]]; then
        print_warning "Key $key_name already exists. Skipping..."
        return 0
    fi
    
    print_status "Generating SSH key: $key_name"
    ssh-keygen -t ed25519 -C "$comment" -f "$key_path" -N ""
    
    if [[ $? -eq 0 ]]; then
        print_success "Generated: $key_path"
        chmod 600 "$key_path"
        chmod 644 "$key_path.pub"
    else
        print_error "Failed to generate key: $key_name"
        return 1
    fi
}

# Display public key
show_public_key() {
    local key_name=$1
    local key_path="$HOME/.ssh/$key_name.pub"
    
    if [[ -f "$key_path" ]]; then
        echo ""
        echo "=== $key_name PUBLIC KEY ==="
        cat "$key_path"
        echo ""
    fi
}

# Display private key (for GitHub Secrets)
show_private_key() {
    local key_name=$1
    local key_path="$HOME/.ssh/$key_name"
    
    if [[ -f "$key_path" ]]; then
        echo ""
        echo "=== $key_name PRIVATE KEY (for GitHub Secrets) ==="
        echo "Copy this entire content to GitHub Secrets:"
        echo "----------------------------------------"
        cat "$key_path"
        echo "----------------------------------------"
        echo ""
    fi
}

# Main function
main() {
    print_status "🔐 Generating SSH keys for JobbLogg deployment..."
    echo ""
    
    check_ssh_dir
    
    # Generate staging key
    generate_key "jobblogg-staging" "<EMAIL>"
    
    # Generate production key
    generate_key "jobblogg-production" "<EMAIL>"
    
    print_success "🎉 SSH keys generated successfully!"
    echo ""
    
    # Show public keys (for server setup)
    print_status "📋 PUBLIC KEYS (copy to servers):"
    show_public_key "jobblogg-staging"
    show_public_key "jobblogg-production"
    
    # Show private keys (for GitHub Secrets)
    print_status "🔐 PRIVATE KEYS (copy to GitHub Secrets):"
    show_private_key "jobblogg-staging"
    show_private_key "jobblogg-production"
    
    # Instructions
    echo ""
    print_status "📝 NEXT STEPS:"
    echo ""
    echo "1. 🖥️  STAGING SERVER SETUP:"
    echo "   ssh root@your-hetzner-server-ip"
    echo "   mkdir -p /opt/jobblogg-staging/.ssh"
    echo "   echo '$(cat ~/.ssh/jobblogg-staging.pub)' >> /opt/jobblogg-staging/.ssh/authorized_keys"
    echo ""
    echo "2. 🖥️  PRODUCTION SERVER SETUP:"
    echo "   ssh root@your-hetzner-server-ip"
    echo "   echo '$(cat ~/.ssh/jobblogg-production.pub)' >> /root/.ssh/authorized_keys"
    echo ""
    echo "3. 🔐 GITHUB SECRETS SETUP:"
    echo "   Go to: https://github.com/djrobbieh/JobbLogg/settings/secrets/actions"
    echo "   Add these secrets:"
    echo "   - STAGING_SSH_KEY (content of ~/.ssh/jobblogg-staging)"
    echo "   - HETZNER_SSH_KEY (content of ~/.ssh/jobblogg-production)"
    echo ""
    echo "4. 🧪 TEST SSH CONNECTION:"
    echo "   ssh -i ~/.ssh/jobblogg-staging staging-deploy@your-server-ip"
    echo "   ssh -i ~/.ssh/jobblogg-production root@your-server-ip"
    echo ""
    
    print_success "✅ SSH key generation completed!"
    print_status "📖 Full setup guide: docs/STAGING_SSH_SETUP.md"
}

# Handle script arguments
case "$1" in
    --help|-h)
        echo "Usage: $0"
        echo "Generates SSH keys for JobbLogg GitHub Actions deployment"
        echo ""
        echo "Generated keys:"
        echo "  ~/.ssh/jobblogg-staging     - For staging server deployment"
        echo "  ~/.ssh/jobblogg-production  - For production server deployment"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
