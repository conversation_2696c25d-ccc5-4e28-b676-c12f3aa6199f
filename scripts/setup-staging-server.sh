#!/bin/bash

# JobbLogg Staging Server Setup Script
# Run this on your staging server to prepare it for deployment

set -e

echo "🚀 Setting up JobbLogg staging server..."

# Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install Docker
echo "🐳 Installing Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Install Docker Compose
echo "🔧 Installing Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create application directory
echo "📁 Creating application directory..."
mkdir -p /opt/jobblogg-staging
cd /opt/jobblogg-staging

# Create logs directory
mkdir -p logs

# Set up basic auth password file
echo "🔐 Setting up basic authentication..."
apt install -y apache2-utils
echo "Enter password for staging basic auth:"
htpasswd -c .htpasswd staging

# Create systemd service for auto-start
echo "⚙️ Creating systemd service..."
cat > /etc/systemd/system/jobblogg-staging.service << EOF
[Unit]
Description=JobbLogg Staging Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/jobblogg-staging
ExecStart=/usr/local/bin/docker-compose -f docker-compose.staging.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.staging.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl enable jobblogg-staging

# Set up firewall
echo "🔥 Configuring firewall..."
ufw allow ssh
ufw allow 80
ufw allow 443
ufw --force enable

# Install Nginx for SSL termination (optional)
echo "🌐 Installing Nginx..."
apt install -y nginx certbot python3-certbot-nginx

# Create basic Nginx config
cat > /etc/nginx/sites-available/staging.jobblogg.no << EOF
server {
    listen 80;
    server_name staging.jobblogg.no;
    
    location / {
        proxy_pass http://localhost:5174;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

ln -sf /etc/nginx/sites-available/staging.jobblogg.no /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

echo "✅ Staging server setup complete!"
echo ""
echo "🔑 Next steps:"
echo "1. Configure DNS: staging.jobblogg.no → $(curl -s ifconfig.me)"
echo "2. Run: certbot --nginx -d staging.jobblogg.no"
echo "3. Add all GitHub secrets as listed in the documentation"
echo "4. Push to main branch to trigger deployment"
echo ""
echo "📋 Server info:"
echo "- IP: $(curl -s ifconfig.me)"
echo "- Docker: $(docker --version)"
echo "- Docker Compose: $(docker-compose --version)"
echo "- Basic auth user: staging"
echo "- App directory: /opt/jobblogg-staging"
