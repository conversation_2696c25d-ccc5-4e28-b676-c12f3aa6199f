#!/bin/bash

# 🔒 JobbLogg Staging Deployment with Basic Auth
# This script deploys the staging environment with password protection

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on staging server
check_environment() {
    print_status "Checking environment..."
    
    if [[ ! -f ".env.staging" ]]; then
        print_error "Missing .env.staging file!"
        exit 1
    fi
    
    if [[ ! -f "docker/nginx/.htpasswd" ]]; then
        print_error "Missing basic auth file! Run setup first."
        exit 1
    fi
    
    print_success "Environment check passed!"
}

# Load staging environment variables
load_env() {
    print_status "Loading staging environment variables..."
    export $(cat .env.staging | grep -v '^#' | xargs)
    print_success "Environment variables loaded!"
}

# Deploy with Traefik (default)
deploy_traefik() {
    print_status "Deploying with Traefik and Basic Auth..."
    
    # Pull latest images
    docker compose -f docker-compose.staging.yml pull
    
    # Build and start services
    docker compose -f docker-compose.staging.yml up --build -d
    
    print_success "Staging deployed with Traefik!"
    print_status "URL: https://staging.jobblogg.no"
    print_status "Username: staging"
    print_status "Password: [configured]"
}

# Deploy with Nginx (alternative)
deploy_nginx() {
    print_status "Deploying with Nginx and Basic Auth..."
    
    # Check if SSL certificates exist
    if [[ ! -d "docker/nginx/ssl" ]]; then
        print_warning "SSL certificates not found. Creating self-signed for staging..."
        mkdir -p docker/nginx/ssl
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout docker/nginx/ssl/staging.jobblogg.no.key \
            -out docker/nginx/ssl/staging.jobblogg.no.crt \
            -subj "/C=NO/ST=Oslo/L=Oslo/O=JobbLogg/CN=staging.jobblogg.no"
    fi
    
    # Pull latest images
    docker compose -f docker-compose.staging.yml pull
    
    # Build and start services with nginx profile
    docker compose -f docker-compose.staging.yml --profile nginx up --build -d
    
    print_success "Staging deployed with Nginx!"
    print_status "URL: https://staging.jobblogg.no"
    print_status "Username: staging"
    print_status "Password: [configured]"
}

# Health check
health_check() {
    print_status "Running health checks..."
    
    sleep 10
    
    # Test with basic auth
    if curl -f -u "staging:c(mS5jEO7!PT5B>!4'" https://staging.jobblogg.no/health > /dev/null 2>&1; then
        print_success "Health check passed!"
    else
        print_warning "Health check failed, but service might still be starting..."
    fi
}

# Main deployment function
main() {
    print_status "🔒 Starting secure staging deployment..."
    
    check_environment
    load_env
    
    # Choose deployment method
    if [[ "$1" == "--nginx" ]]; then
        deploy_nginx
    else
        deploy_traefik
    fi
    
    health_check
    
    print_success "🎉 Staging deployment completed!"
    echo ""
    print_status "Access Information:"
    print_status "URL: https://staging.jobblogg.no"
    print_status "Username: staging"
    print_status "Password: c(mS5jEO7!PT5B>!4'"
    echo ""
    print_status "Commands:"
    print_status "View logs: docker compose -f docker-compose.staging.yml logs -f"
    print_status "Stop: docker compose -f docker-compose.staging.yml down"
    print_status "Restart: $0"
}

# Handle script arguments
case "$1" in
    --nginx)
        main --nginx
        ;;
    --traefik|"")
        main --traefik
        ;;
    --help|-h)
        echo "Usage: $0 [--nginx|--traefik]"
        echo "  --nginx    Deploy with Nginx reverse proxy"
        echo "  --traefik  Deploy with Traefik (default)"
        exit 0
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
