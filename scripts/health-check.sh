#!/bin/bash

# JobbLogg Health Check Script
# This script checks the health of all services in the deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-dev}
COMPOSE_FILE="docker-compose.yml"

if [ "$ENVIRONMENT" = "prod" ]; then
    COMPOSE_FILE="docker-compose.yml -f docker-compose.prod.yml"
elif [ "$ENVIRONMENT" = "staging" ]; then
    COMPOSE_FILE="docker-compose.yml -f docker-compose.staging.yml"
fi

echo -e "${BLUE}🏥 JobbLogg Health Check - Environment: $ENVIRONMENT${NC}"
echo "=================================================="

# Function to check HTTP endpoint
check_endpoint() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    
    echo -n "Checking $name ($url)... "
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/health_response "$url" 2>/dev/null); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            echo -e "${GREEN}✅ Healthy (HTTP $status_code)${NC}"
            if [ -s /tmp/health_response ]; then
                echo "  Response: $(cat /tmp/health_response | head -c 100)..."
            fi
        else
            echo -e "${RED}❌ Unhealthy (HTTP $status_code)${NC}"
            if [ -s /tmp/health_response ]; then
                echo "  Response: $(cat /tmp/health_response)"
            fi
        fi
    else
        echo -e "${RED}❌ Connection failed${NC}"
    fi
}

# Function to check Docker service health
check_docker_service() {
    local service_name=$1
    echo -n "Checking Docker service $service_name... "
    
    if docker compose -f $COMPOSE_FILE ps --format json | jq -r ".[] | select(.Service == \"$service_name\") | .Health" | grep -q "healthy"; then
        echo -e "${GREEN}✅ Healthy${NC}"
    elif docker compose -f $COMPOSE_FILE ps --format json | jq -r ".[] | select(.Service == \"$service_name\") | .Health" | grep -q "unhealthy"; then
        echo -e "${RED}❌ Unhealthy${NC}"
        echo "  Checking logs for errors..."
        docker compose -f $COMPOSE_FILE logs --tail=10 "$service_name" | tail -5
    else
        echo -e "${YELLOW}⚠️  No health check or starting${NC}"
    fi
}

# Check Docker services
echo -e "\n${BLUE}🐳 Docker Services${NC}"
echo "-------------------"

if [ "$ENVIRONMENT" = "dev" ]; then
    check_docker_service "convex"
fi
check_docker_service "frontend"

# Check HTTP endpoints
echo -e "\n${BLUE}🌐 HTTP Endpoints${NC}"
echo "-------------------"

if [ "$ENVIRONMENT" = "prod" ]; then
    check_endpoint "Production Frontend" "https://jobblogg.no/"
    check_endpoint "Production Health" "https://jobblogg.no/api/health"
    # Production uses cloud Convex - no local service to check
elif [ "$ENVIRONMENT" = "staging" ]; then
    check_endpoint "Staging Frontend" "https://staging.jobblogg.no/"
    check_endpoint "Staging Health" "https://staging.jobblogg.no/api/health"
    # Staging uses cloud Convex - no local service to check
else
    check_endpoint "Dev Frontend" "http://localhost:5173/"
    check_endpoint "Dev Convex" "http://localhost:3210/health"
fi

# Check container resource usage
echo -e "\n${BLUE}📊 Resource Usage${NC}"
echo "-------------------"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" | grep jobblogg || echo "No JobbLogg containers running"

# Summary
echo -e "\n${BLUE}📋 Summary${NC}"
echo "==========="
echo "Health check completed for environment: $ENVIRONMENT"
echo "Check the output above for any issues that need attention."

# Cleanup
rm -f /tmp/health_response

echo -e "\n${GREEN}Health check script completed!${NC}"
