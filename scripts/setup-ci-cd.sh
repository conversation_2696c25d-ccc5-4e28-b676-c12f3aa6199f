#!/bin/bash

# 🚀 JobbLogg CI/CD Setup Script
# This script sets up the server for automated deployments

set -e

echo "🚀 Setting up JobbLogg CI/CD infrastructure..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root"
    exit 1
fi

print_status "Starting CI/CD setup for JobbLogg..."

# 1. Create deployment user
print_status "Creating GitHub Actions deployment user..."
if id "github-actions" &>/dev/null; then
    print_warning "User 'github-actions' already exists"
else
    adduser --disabled-password --gecos "GitHub Actions Deployment User" github-actions
    print_success "Created user 'github-actions'"
fi

# 2. Add user to necessary groups
print_status "Adding user to docker and sudo groups..."
usermod -aG docker github-actions
usermod -aG sudo github-actions
print_success "User added to groups"

# 3. Setup SSH directory
print_status "Setting up SSH directory..."
mkdir -p /home/<USER>/.ssh
chown github-actions:github-actions /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
print_success "SSH directory created"

# 4. Create project directory
print_status "Creating project directory..."
mkdir -p /opt/jobblogg
chown github-actions:github-actions /opt/jobblogg
print_success "Project directory created"

# 5. Clone repository (if not exists)
if [ ! -d "/opt/jobblogg/.git" ]; then
    print_status "Cloning JobbLogg repository..."
    cd /opt/jobblogg
    # Note: This will need to be updated with the actual repository URL
    git clone https://github.com/djrobbieh/JobbLogg.git .
    chown -R github-actions:github-actions /opt/jobblogg
    print_success "Repository cloned"
else
    print_warning "Repository already exists"
fi

# 6. Create environment files
print_status "Creating environment file templates..."

# Staging environment
cat > /opt/jobblogg/.env.staging << 'EOF'
# Staging Environment Variables
NODE_ENV=staging
CONVEX_DEPLOYMENT=staging:jobblogg
CONVEX_URL_STAGING=
VITE_CONVEX_URL_STAGING=
VITE_CLERK_PUBLISHABLE_KEY_STAGING=
VITE_GOOGLE_MAPS_API_KEY=
VITE_STRIPE_PUBLISHABLE_KEY_STAGING=
STRIPE_SECRET_KEY_STAGING=
STRIPE_WEBHOOK_SECRET_STAGING=
RESEND_API_KEY_STAGING=
EOF

# Production environment
cat > /opt/jobblogg/.env.production << 'EOF'
# Production Environment Variables
NODE_ENV=production
CONVEX_DEPLOYMENT=prod:jobblogg
CONVEX_URL=
VITE_CONVEX_URL=
VITE_CLERK_PUBLISHABLE_KEY=
VITE_GOOGLE_MAPS_API_KEY=
VITE_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
RESEND_API_KEY=
EOF

chown github-actions:github-actions /opt/jobblogg/.env.*
chmod 600 /opt/jobblogg/.env.*
print_success "Environment files created"

# 7. Create deployment scripts directory
print_status "Creating deployment scripts..."
mkdir -p /opt/jobblogg/scripts/deployment
chown -R github-actions:github-actions /opt/jobblogg/scripts

# Health check script
cat > /opt/jobblogg/scripts/deployment/health-check.sh << 'EOF'
#!/bin/bash
# Health check script for deployments

set -e

ENVIRONMENT=${1:-staging}
MAX_RETRIES=30
RETRY_INTERVAL=10

if [ "$ENVIRONMENT" = "production" ]; then
    URL="https://jobblogg.no/api/health"
else
    URL="https://staging.jobblogg.no/api/health"
fi

echo "🔍 Running health check for $ENVIRONMENT environment..."
echo "🌐 URL: $URL"

for i in $(seq 1 $MAX_RETRIES); do
    echo "Attempt $i/$MAX_RETRIES..."
    
    if curl -f -s "$URL" > /dev/null; then
        echo "✅ Health check passed!"
        exit 0
    fi
    
    if [ $i -lt $MAX_RETRIES ]; then
        echo "⏳ Waiting ${RETRY_INTERVAL}s before retry..."
        sleep $RETRY_INTERVAL
    fi
done

echo "❌ Health check failed after $MAX_RETRIES attempts"
exit 1
EOF

# Backup script
cat > /opt/jobblogg/scripts/deployment/backup.sh << 'EOF'
#!/bin/bash
# Backup script for deployments

set -e

ENVIRONMENT=${1:-production}
BACKUP_DIR="/opt/jobblogg/backups"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

echo "💾 Creating backup for $ENVIRONMENT deployment..."

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Backup current deployment
if [ -d "/opt/jobblogg" ]; then
    echo "📦 Backing up current deployment..."
    tar -czf "$BACKUP_DIR/deployment-$ENVIRONMENT-$TIMESTAMP.tar.gz" \
        -C /opt/jobblogg \
        --exclude=node_modules \
        --exclude=.git \
        --exclude=dist \
        --exclude=backups \
        .
    echo "✅ Backup created: deployment-$ENVIRONMENT-$TIMESTAMP.tar.gz"
fi

# Keep only last 5 backups
echo "🧹 Cleaning old backups..."
cd "$BACKUP_DIR"
ls -t deployment-$ENVIRONMENT-*.tar.gz | tail -n +6 | xargs -r rm
echo "✅ Backup cleanup completed"
EOF

# Rollback script
cat > /opt/jobblogg/scripts/deployment/rollback.sh << 'EOF'
#!/bin/bash
# Rollback script for deployments

set -e

ENVIRONMENT=${1:-staging}
BACKUP_DIR="/opt/jobblogg/backups"

echo "🚨 Rolling back $ENVIRONMENT deployment..."

# Find latest backup
LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/deployment-$ENVIRONMENT-*.tar.gz 2>/dev/null | head -1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "❌ No backup found for rollback"
    exit 1
fi

echo "📦 Using backup: $(basename "$LATEST_BACKUP")"

# Stop services
echo "🛑 Stopping services..."
docker-compose -f docker-compose.$ENVIRONMENT.yml down || true

# Restore backup
echo "📥 Restoring backup..."
cd /opt/jobblogg
tar -xzf "$LATEST_BACKUP"

# Restart services
echo "🚀 Starting services..."
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --build

echo "✅ Rollback completed"
EOF

# Make scripts executable
chmod +x /opt/jobblogg/scripts/deployment/*.sh
chown -R github-actions:github-actions /opt/jobblogg/scripts
print_success "Deployment scripts created"

# 8. Setup log rotation
print_status "Setting up log rotation..."
cat > /etc/logrotate.d/jobblogg << 'EOF'
/opt/jobblogg/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 github-actions github-actions
    postrotate
        docker-compose -f /opt/jobblogg/docker-compose.staging.yml restart || true
        docker-compose -f /opt/jobblogg/docker-compose.prod.yml restart || true
    endscript
}
EOF
print_success "Log rotation configured"

# 9. Create systemd service for monitoring
print_status "Creating monitoring service..."
cat > /etc/systemd/system/jobblogg-monitor.service << 'EOF'
[Unit]
Description=JobbLogg Health Monitor
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
User=github-actions
WorkingDirectory=/opt/jobblogg
ExecStart=/opt/jobblogg/scripts/deployment/health-check.sh production
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# Create timer for regular health checks
cat > /etc/systemd/system/jobblogg-monitor.timer << 'EOF'
[Unit]
Description=Run JobbLogg Health Monitor every 5 minutes
Requires=jobblogg-monitor.service

[Timer]
OnCalendar=*:0/5
Persistent=true

[Install]
WantedBy=timers.target
EOF

systemctl daemon-reload
systemctl enable jobblogg-monitor.timer
systemctl start jobblogg-monitor.timer
print_success "Monitoring service created and started"

# 10. Final setup
print_status "Final setup steps..."

# Create logs directory
mkdir -p /opt/jobblogg/logs
chown github-actions:github-actions /opt/jobblogg/logs

# Set proper permissions
chown -R github-actions:github-actions /opt/jobblogg
chmod -R 755 /opt/jobblogg

print_success "CI/CD setup completed successfully!"

echo ""
echo "🎉 JobbLogg CI/CD Setup Complete!"
echo ""
echo "📋 Next steps:"
echo "1. Add SSH public key to /home/<USER>/.ssh/authorized_keys"
echo "2. Configure environment variables in .env.staging and .env.production"
echo "3. Set up GitHub Secrets with SSH private key"
echo "4. Test deployment with GitHub Actions"
echo ""
echo "📁 Key directories:"
echo "   - Project: /opt/jobblogg"
echo "   - Backups: /opt/jobblogg/backups"
echo "   - Logs: /opt/jobblogg/logs"
echo "   - Scripts: /opt/jobblogg/scripts/deployment"
echo ""
echo "👤 Deployment user: github-actions"
echo "🔐 SSH key location: /home/<USER>/.ssh/authorized_keys"
echo ""
print_success "Setup script completed!"
