#!/bin/bash

# JobbLogg Comprehensive Database Reset Script
# This script safely removes all user-generated data while preserving database schema structure

set -e  # Exit on any error

echo "🚨 JobbLogg Comprehensive Database Reset Utility"
echo "================================================"
echo ""
echo "⚠️  WARNING: This will permanently delete ALL user-generated data!"
echo "⚠️  Including: projects, customers, users, chat messages, contractor data"
echo "⚠️  COLLABORATION: Project assignments, user notifications, email tracking"
echo "⚠️  USER DATA: Personal project notes, notification history, link previews"
echo "⚠️  Only use in development/testing environments!"
echo ""
echo "✅ PRESERVES: Database schema, indexes, system configuration"
echo ""

# Check if we're in the right directory
if [ ! -f "convex/schema.ts" ]; then
    echo "❌ Error: Please run this script from the JobbLogg project root directory"
    exit 1
fi

# Check if Convex CLI is available
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx is not available. Please install Node.js and npm."
    exit 1
fi

# Function to get comprehensive data count
get_data_count() {
    echo "📊 Getting comprehensive database state..."
    npx convex run clearAllProjectData:getProjectDataCount '{}'
    echo ""
}

# Function to run dry-run analysis
dry_run_analysis() {
    echo "🔍 Running dry-run analysis (no data will be deleted)..."
    echo "Include file storage analysis? (y/n):"
    read -r include_storage

    if [ "$include_storage" = "y" ] || [ "$include_storage" = "Y" ]; then
        npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": true}'
    else
        npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'
    fi
    echo ""
}

# Function to create test data
create_test_data() {
    echo "🧪 Creating test data..."
    echo "Please enter your Clerk user ID (you can find this in the Convex dashboard):"
    read -r USER_ID

    if [ -z "$USER_ID" ]; then
        echo "❌ Error: User ID is required"
        exit 1
    fi

    npx convex run testDataUtilities:createTestData "{\"confirmationCode\": \"CREATE_TEST_DATA\", \"testUserId\": \"$USER_ID\"}"
    echo ""
}

# Function to clear all data (legacy)
clear_all_data() {
    echo "🗑️  Clearing all data (legacy function)..."
    npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA"}'
    echo ""
}

# Function to selective team reset
selective_team_reset() {
    echo ""
    echo "🎯 SELECTIVE TEAM DATA RESET"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "🗑️  WILL DELETE:"
    echo "   • Team members with role 'utfoerende' (executor role)"
    echo "   • All team invitations (pending and accepted)"
    echo "   • All project assignments"
    echo ""
    echo "✅ WILL PRESERVE:"
    echo "   • Administrator users"
    echo "   • Company records"
    echo "   • All projects"
    echo "   • All customers"
    echo "   • All chat messages and reactions"
    echo "   • All other non-team-related data"
    echo ""
    echo "This is perfect for testing team invitation system without losing development data!"
    echo ""

    # Dry run first
    echo "🔍 Running dry-run analysis first..."
    npx convex run teamManagement:selectiveTeamDataReset '{"confirmationCode": "DELETE_TEAM_DATA", "dryRun": true}'
    echo ""

    echo "⚠️  This action cannot be undone!"
    read -p "Type 'DELETE_TEAM_DATA' to confirm selective team reset: " confirm

    if [ "$confirm" = "DELETE_TEAM_DATA" ]; then
        echo "🗑️  Executing selective team data reset..."
        npx convex run teamManagement:selectiveTeamDataReset '{"confirmationCode": "DELETE_TEAM_DATA", "dryRun": false}'
        echo "✅ Selective team data reset completed!"
    else
        echo "❌ Operation cancelled"
    fi
    echo ""
}

# Function to comprehensive reset
comprehensive_reset() {
    echo ""
    echo "🔧 COMPREHENSIVE DATABASE RESET OPTIONS:"
    echo "1) Database records only (preserve file storage)"
    echo "2) Database records + file storage (complete wipe)"
    echo ""
    read -p "Choose option (1-2): " storage_choice

    local include_storage="false"
    if [ "$storage_choice" = "2" ]; then
        include_storage="true"
        echo ""
        echo "⚠️  WARNING: This will also delete all images and files from Convex storage!"
        echo "⚠️  This action cannot be undone!"
        echo ""
    fi

    echo "🗑️  Executing comprehensive database reset..."
    npx convex run clearAllProjectData:comprehensiveReset "{\"confirmationCode\": \"DELETE_ALL_PROJECT_DATA\", \"dryRun\": false, \"includeFileStorage\": $include_storage}"
    echo ""
}

# Main menu
while true; do
    echo "What would you like to do?"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📊 ANALYSIS & INSPECTION:"
    echo "1) Check comprehensive database state"
    echo "2) Dry-run analysis (preview what would be deleted)"
    echo ""
    echo "🗑️  DATA MANAGEMENT:"
    echo "3) Selective team data reset (preserve projects/customers)"
    echo "4) Comprehensive database reset (full wipe)"
    echo "5) Legacy project data clear (backward compatibility)"
    echo ""
    echo "🧪 TESTING:"
    echo "6) Create test data"
    echo "7) Full reset + create test data"
    echo ""
    echo "8) Exit"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    read -p "Enter your choice (1-8): " choice

    case $choice in
        1)
            get_data_count
            ;;
        2)
            dry_run_analysis
            ;;
        3)
            selective_team_reset
            ;;
        4)
            echo ""
            echo "⚠️  COMPREHENSIVE DATABASE RESET"
            echo "⚠️  This will delete ALL user-generated data:"
            echo "   • All projects (including archived)"
            echo "   • All customers (including contractor companies)"
            echo "   • All user records (contractor onboarding data)"
            echo "   • All chat messages and reactions"
            echo "   • All log entries and image likes"
            echo "   • All typing indicators"
            echo "   • All project assignments and team collaboration"
            echo "   • All user notifications and notification history"
            echo "   • All user project notes (private notes)"
            echo "   • All email tracking and delivery history"
            echo "   • All link preview cache data"
            echo "   • Optionally: All file storage (images/attachments)"
            echo ""
            echo "✅ PRESERVES: Database schema, indexes, system configuration"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'RESET' to confirm: " confirm

            if [ "$confirm" = "RESET" ]; then
                echo "📊 Current state before reset:"
                get_data_count

                comprehensive_reset
                echo "✅ Comprehensive database reset completed!"

                echo "📊 Final state after reset:"
                get_data_count

                echo "🎉 Database is now completely clean for fresh testing!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        5)
            echo ""
            echo "⚠️  LEGACY PROJECT DATA CLEAR"
            echo "⚠️  This uses the legacy function (may not include contractor data)"
            echo "This action cannot be undone!"
            read -p "Type 'yes' to confirm: " confirm

            if [ "$confirm" = "yes" ]; then
                clear_all_data
                echo "✅ Legacy project data cleared successfully!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        6)
            create_test_data
            echo "✅ Test data created successfully!"
            echo ""
            ;;
        7)
            echo ""
            echo "⚠️  FULL RESET + TEST DATA CREATION"
            echo "⚠️  This will:"
            echo "   1. Delete all existing user data"
            echo "   2. Create fresh test data"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'RESET' to confirm: " confirm

            if [ "$confirm" = "RESET" ]; then
                echo "📊 Current state before reset:"
                get_data_count

                comprehensive_reset
                echo "✅ All data cleared!"

                create_test_data
                echo "✅ Test data created!"

                echo "📊 Final state after reset:"
                get_data_count

                echo "🎉 Full database reset with test data completed successfully!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        8)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-8."
            echo ""
            ;;
    esac
done
