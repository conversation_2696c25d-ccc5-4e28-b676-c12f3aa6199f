#!/bin/bash

# JobbLogg Docker Development Script
# This script helps manage the Docker development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if environment file exists
check_env_file() {
    if [ ! -f ".env.docker.local" ]; then
        print_warning ".env.docker.local not found. Creating from template..."
        cp .env.docker .env.docker.local
        print_warning "Please edit .env.docker.local with your configuration before continuing."
        exit 1
    fi
}

# Function to start development environment
start_dev() {
    print_status "Starting JobbLogg development environment..."
    
    check_docker
    check_env_file
    
    # Load environment variables
    export $(cat .env.docker.local | grep -v '^#' | xargs)
    
    # Build and start services
    docker compose -f docker-compose.dev.yml up --build -d
    
    print_success "Development environment started!"
    print_status "Frontend: http://localhost:5173"
    print_status "Convex: http://localhost:3210"
    print_status ""
    print_status "To view logs: ./scripts/docker-dev.sh logs"
    print_status "To stop: ./scripts/docker-dev.sh stop"
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping JobbLogg development environment..."
    docker compose -f docker-compose.dev.yml down
    print_success "Development environment stopped!"
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting JobbLogg development environment..."
    stop_dev
    start_dev
}

# Function to view logs
view_logs() {
    if [ -z "$2" ]; then
        docker compose -f docker-compose.dev.yml logs -f
    else
        docker compose -f docker-compose.dev.yml logs -f "$2"
    fi
}

# Function to rebuild services
rebuild() {
    print_status "Rebuilding JobbLogg development environment..."
    docker compose -f docker-compose.dev.yml down
    docker compose -f docker-compose.dev.yml build --no-cache
    docker compose -f docker-compose.dev.yml up -d
    print_success "Development environment rebuilt!"
}

# Function to show status
show_status() {
    print_status "JobbLogg Development Environment Status:"
    docker compose -f docker-compose.dev.yml ps
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker compose -f docker-compose.dev.yml down -v
    docker system prune -f
    print_success "Cleanup completed!"
}

# Function to show help
show_help() {
    echo "JobbLogg Docker Development Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start the development environment"
    echo "  stop      Stop the development environment"
    echo "  restart   Restart the development environment"
    echo "  logs      View logs (optionally specify service: logs convex)"
    echo "  rebuild   Rebuild and restart services"
    echo "  status    Show service status"
    echo "  cleanup   Clean up Docker resources"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs convex"
    echo "  $0 rebuild"
}

# Main script logic
case "${1:-help}" in
    start)
        start_dev
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart_dev
        ;;
    logs)
        view_logs "$@"
        ;;
    rebuild)
        rebuild
        ;;
    status)
        show_status
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
