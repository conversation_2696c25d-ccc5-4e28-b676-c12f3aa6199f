#!/bin/bash

# JobbLogg SSH Deploy Key Setup Script
# This script sets up SSH Deploy Key authentication for private repository access

set -e

echo "🔑 JobbLogg SSH Deploy Key Setup"
echo "================================="

# Step 1: Generate SSH Deploy Key
echo "📝 Step 1: Generating SSH Deploy Key..."
SSH_KEY_PATH="$HOME/.ssh/jobblogg_deploy_key"

if [ -f "$SSH_KEY_PATH" ]; then
    echo "⚠️ SSH key already exists at $SSH_KEY_PATH"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Aborted. Using existing key."
    else
        rm -f "$SSH_KEY_PATH" "$SSH_KEY_PATH.pub"
    fi
fi

if [ ! -f "$SSH_KEY_PATH" ]; then
    ssh-keygen -t ed25519 -C "jobblogg-staging-deploy-key" -f "$SSH_KEY_PATH" -N ""
    echo "✅ SSH key pair generated:"
    echo "   Private key: $SSH_KEY_PATH"
    echo "   Public key: $SSH_KEY_PATH.pub"
fi

# Step 2: Display public key for GitHub
echo ""
echo "📋 Step 2: Add this public key to GitHub Repository"
echo "=================================================="
echo "1. Go to: https://github.com/djrobbieh/JobbLogg/settings/keys"
echo "2. Click 'Add deploy key'"
echo "3. Title: 'Staging Server Deploy Key'"
echo "4. Paste this public key:"
echo ""
echo "--- PUBLIC KEY (copy this) ---"
cat "$SSH_KEY_PATH.pub"
echo "--- END PUBLIC KEY ---"
echo ""
echo "5. Check 'Allow write access' if needed"
echo "6. Click 'Add key'"
echo ""

# Step 3: Display private key for GitHub Secrets
echo "🔐 Step 3: Add private key to GitHub Secrets"
echo "============================================="
echo "1. Go to: https://github.com/djrobbieh/JobbLogg/settings/secrets/actions"
echo "2. Click 'New repository secret'"
echo "3. Name: JOBBLOGG_DEPLOY_KEY"
echo "4. Paste this private key (including BEGIN/END lines):"
echo ""
echo "--- PRIVATE KEY (copy this) ---"
cat "$SSH_KEY_PATH"
echo "--- END PRIVATE KEY ---"
echo ""

# Step 4: Setup on staging server
echo "🖥️ Step 4: Setup on Staging Server"
echo "==================================="
echo "Run these commands on your staging server:"
echo ""
echo "# Create SSH directory"
echo "mkdir -p ~/.ssh"
echo "chmod 700 ~/.ssh"
echo ""
echo "# Add GitHub host key"
echo "ssh-keyscan -H github.com >> ~/.ssh/known_hosts"
echo ""
echo "# Create deploy key file (paste private key content)"
echo "nano ~/.ssh/jobblogg_deploy_key"
echo "chmod 600 ~/.ssh/jobblogg_deploy_key"
echo ""
echo "# Test SSH connection"
echo "ssh -T -i ~/.ssh/jobblogg_deploy_key **************"
echo ""

# Step 5: Verification
echo "✅ Step 5: Verification"
echo "======================"
echo "After completing all steps above:"
echo "1. Test SSH connection from staging server"
echo "2. Push to main branch to trigger deployment"
echo "3. Check GitHub Actions for successful deployment"
echo ""

echo "🎉 SSH Deploy Key setup guide complete!"
echo "Follow the steps above to enable SSH authentication for staging deployment."
