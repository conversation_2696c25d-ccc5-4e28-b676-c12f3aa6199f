#!/usr/bin/env node

/**
 * Authentication Flow Test Script
 * 
 * This script helps verify that the authentication configuration is working correctly
 * by checking the necessary environment variables and configuration files.
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

console.log('🔐 JobbLogg Authentication Flow Test\n');

// Check if auth.config.ts exists
const authConfigPath = join(process.cwd(), 'convex', 'auth.config.ts');
if (existsSync(authConfigPath)) {
  console.log('✅ convex/auth.config.ts exists');
  
  const authConfig = readFileSync(authConfigPath, 'utf8');
  if (authConfig.includes('applicationID: "convex"')) {
    console.log('✅ auth.config.ts has correct applicationID');
  } else {
    console.log('❌ auth.config.ts missing correct applicationID');
  }
  
  if (authConfig.includes('domain:')) {
    console.log('✅ auth.config.ts has domain configuration');
  } else {
    console.log('❌ auth.config.ts missing domain configuration');
  }
} else {
  console.log('❌ convex/auth.config.ts is missing');
}

// Check environment variables
console.log('\n📋 Environment Variables Check:');

const requiredEnvVars = [
  'VITE_CONVEX_URL',
  'VITE_CLERK_PUBLISHABLE_KEY',
  'VITE_CLERK_FRONTEND_API_URL'
];

let envFile = '';
try {
  envFile = readFileSync('.env.local', 'utf8');
} catch (error) {
  try {
    envFile = readFileSync('.env', 'utf8');
  } catch (error) {
    console.log('❌ No .env.local or .env file found');
  }
}

requiredEnvVars.forEach(envVar => {
  if (envFile.includes(envVar)) {
    console.log(`✅ ${envVar} is configured`);
  } else {
    console.log(`❌ ${envVar} is missing`);
  }
});

// Check main.tsx for ConvexProviderWithClerk
const mainTsxPath = join(process.cwd(), 'src', 'main.tsx');
if (existsSync(mainTsxPath)) {
  const mainTsx = readFileSync(mainTsxPath, 'utf8');
  
  console.log('\n🔧 Provider Configuration Check:');
  
  if (mainTsx.includes('ConvexProviderWithClerk')) {
    console.log('✅ Using ConvexProviderWithClerk');
  } else {
    console.log('❌ Not using ConvexProviderWithClerk');
  }
  
  if (mainTsx.includes('useAuth={useAuth}')) {
    console.log('✅ useAuth hook is passed to ConvexProviderWithClerk');
  } else {
    console.log('❌ useAuth hook not passed to ConvexProviderWithClerk');
  }
  
  if (mainTsx.includes('convex/react-clerk')) {
    console.log('✅ Importing from convex/react-clerk');
  } else {
    console.log('❌ Not importing from convex/react-clerk');
  }
}

// Check App.tsx for Convex auth components
const appTsxPath = join(process.cwd(), 'src', 'App.tsx');
if (existsSync(appTsxPath)) {
  const appTsx = readFileSync(appTsxPath, 'utf8');
  
  console.log('\n🛡️ Authentication Components Check:');
  
  if (appTsx.includes('Authenticated') && appTsx.includes('Unauthenticated')) {
    console.log('✅ Using Convex authentication components');
  } else {
    console.log('❌ Not using Convex authentication components');
  }
  
  if (appTsx.includes('AuthLoading')) {
    console.log('✅ Using AuthLoading component');
  } else {
    console.log('❌ Not using AuthLoading component');
  }
  
  if (appTsx.includes('SignedIn') || appTsx.includes('SignedOut')) {
    console.log('⚠️  Still using Clerk authentication components (should be replaced)');
  } else {
    console.log('✅ Not using deprecated Clerk authentication components');
  }
}

console.log('\n🎯 Next Steps:');
console.log('1. Ensure all environment variables are set in .env.local');
console.log('2. Update convex/auth.config.ts with your actual Clerk Frontend API URL');
console.log('3. Create a JWT template in Clerk dashboard named "convex"');
console.log('4. Run "npx convex dev" to deploy the auth configuration');
console.log('5. Test the contractor onboarding flow');

console.log('\n📚 For detailed instructions, see AUTHENTICATION_FIX_GUIDE.md');
