#!/bin/bash

# 🔐 Script for å oppdatere Clerk Live API-nøkler på produksjonsserver
# Kj<PERSON><PERSON> dette scriptet på produksjonsserveren (jobblogg.no)

set -e  # Exit on any error

echo "🔐 Oppdaterer Clerk Live API-nøkler på produksjonsserver..."

# Farge-koder for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Sjekk at vi er på riktig server
if [ ! -d "/root/JobbLogg" ]; then
    print_error "Ikke på produksjonsserver eller JobbLogg-mappen finnes ikke!"
    exit 1
fi

# Naviger til prosjektmappen
cd /root/JobbLogg

print_status "Navigert til /root/JobbLogg"

# Pull latest changes
print_status "Henter siste endringer fra Git..."
git pull origin main

# Stopp eksisterende container
print_status "Stopper eksisterende produksjonscontainer..."
docker stop jobblogg-frontend-production 2>/dev/null || true
docker rm jobblogg-frontend-production 2>/dev/null || true

# Prompt for live Clerk key
echo ""
print_warning "Du må oppgi din LIVE Clerk Publishable Key"
echo "Gå til https://dashboard.clerk.com/ → API Keys"
echo "Kopier 'Publishable key' som starter med 'pk_live_'"
echo ""
read -p "Skriv inn din LIVE Clerk Publishable Key: " LIVE_CLERK_KEY

# Validate key format
if [[ ! $LIVE_CLERK_KEY =~ ^pk_live_ ]]; then
    print_error "Ugyldig Clerk key format! Må starte med 'pk_live_'"
    exit 1
fi

print_success "Gyldig Clerk live key mottatt"

# Build new production image with live keys
print_status "Bygger ny produksjonsimage med live Clerk-nøkler..."

docker build --no-cache \
  --target production \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY="$LIVE_CLERK_KEY" \
  --build-arg VITE_CONVEX_URL="https://api.jobblogg.no" \
  --build-arg VITE_GOOGLE_MAPS_API_KEY="AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs" \
  --build-arg VITE_STRIPE_PUBLISHABLE_KEY="pk_live_[DIN_LIVE_STRIPE_KEY]" \
  -f docker/frontend/Dockerfile.fixed \
  -t jobblogg-frontend-production:latest .

if [ $? -ne 0 ]; then
    print_error "Docker build feilet!"
    exit 1
fi

print_success "Docker image bygget med live Clerk-nøkler"

# Start new container
print_status "Starter ny produksjonscontainer..."

docker run -d \
  --name jobblogg-frontend-production \
  --restart unless-stopped \
  -p 3000:3000 \
  jobblogg-frontend-production:latest

if [ $? -ne 0 ]; then
    print_error "Kunne ikke starte ny container!"
    exit 1
fi

# Wait for container to start
print_status "Venter på at container skal starte..."
sleep 10

# Health check
print_status "Kjører health check..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    print_success "Health check OK!"
else
    print_warning "Health check feilet, men container kjører"
fi

# Show container status
print_status "Container status:"
docker ps | grep jobblogg-frontend-production

print_success "🎉 Produksjonsserver oppdatert med live Clerk-nøkler!"
print_status "Nettside: https://jobblogg.no"
print_status "For å sjekke logger: docker logs jobblogg-frontend-production"

echo ""
print_warning "VIKTIG: Test at innlogging fungerer på https://jobblogg.no"
print_warning "Hvis noe går galt, kan du rulle tilbake med forrige image"
