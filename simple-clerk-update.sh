#!/bin/bash

# 🔐 Enkel Clerk Live API-nøkkel oppdatering
# Kjør på produksjonsserveren: bash simple-clerk-update.sh

echo "🔐 Oppdaterer til Clerk Live API-nøkler..."
echo "📍 Nåværende mappe: $(pwd)"

# Sjekk om vi er på riktig server
if [ ! -d "/root/JobbLogg" ]; then
    echo "❌ Ikke på produksjonsserver eller JobbLogg-mappen finnes ikke!"
    echo "📍 Prøver å finne JobbLogg-mappen..."
    find / -name "JobbLogg" -type d 2>/dev/null | head -5
    exit 1
fi

# Gå til riktig mappe
cd /root/JobbLogg
echo "✅ Navigert til /root/JobbLogg"

# Hent siste kode
echo "📥 Henter siste kode..."
git pull origin main

# Stopp gammel container
echo "🛑 Stopper gammel container..."
docker stop jobblogg-frontend-production 2>/dev/null || true
docker rm jobblogg-frontend-production 2>/dev/null || true

# Spør om live nøkkel
echo ""
echo "🔑 Trenger din LIVE Clerk Publishable Key"
echo "Gå til https://dashboard.clerk.com/ → API Keys"
echo "Kopier nøkkelen som starter med 'pk_live_'"
echo ""
read -p "Lim inn din live Clerk key: " CLERK_KEY

# Sjekk at nøkkelen er riktig format
if [[ ! $CLERK_KEY =~ ^pk_live_ ]]; then
    echo "❌ Feil format! Må starte med 'pk_live_'"
    exit 1
fi

echo "✅ Gyldig nøkkel mottatt"

# Bygg ny image
echo "🏗️ Bygger ny produksjonsimage..."
docker build --no-cache \
  --target production \
  --build-arg VITE_CLERK_PUBLISHABLE_KEY="$CLERK_KEY" \
  --build-arg VITE_CONVEX_URL="https://api.jobblogg.no" \
  --build-arg VITE_GOOGLE_MAPS_API_KEY="AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs" \
  --build-arg VITE_STRIPE_PUBLISHABLE_KEY="pk_test_51QuHVWRqXwHRnsDwFyefP4DJfEDG9Ti42UkWO7Y5nWmSDZbZtVLWfgDmmAP3YnYYb8905qIhtDDB8UUPLDjaUk9F00snevRBNh" \
  -f docker/frontend/Dockerfile.fixed \
  -t jobblogg-frontend-production:latest .

if [ $? -ne 0 ]; then
    echo "❌ Build feilet!"
    exit 1
fi

# Start ny container
echo "🚀 Starter ny container..."
docker run -d \
  --name jobblogg-frontend-production \
  --restart unless-stopped \
  -p 3000:3000 \
  jobblogg-frontend-production:latest

# Vent litt
echo "⏳ Venter på oppstart..."
sleep 15

# Sjekk status
echo "📊 Container status:"
docker ps | grep jobblogg-frontend-production

echo ""
echo "✅ Ferdig! Test nå https://jobblogg.no"
echo "📝 For å sjekke logger: docker logs jobblogg-frontend-production"
