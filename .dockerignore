# Ignore ALL environment files to prevent them from overriding build args
.env*
*.env
.env
.env.local
.env.prod
.env.production
.env.production.backup
.env.development
.env.docker
.env.docker.local
.env.staging

# Node modules
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build
.next

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
*.md
docs/

# Test files
tests/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Coverage
coverage/
.nyc_output/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Temporary folders
tmp/
temp/
