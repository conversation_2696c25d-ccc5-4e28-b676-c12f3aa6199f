# JobbLogg Docker Setup Guide

This guide explains how to run <PERSON>b<PERSON>og<PERSON> using Docker on your VPS server.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed on your VPS
- Git installed
- SSH access to your VPS
- Domain name (for production with SSL)

## Quick Start

### 1. <PERSON>lone the Repository

```bash
<NAME_EMAIL>:djrobbieh/JobbLogg.git
cd JobbLogg
```

### 2. Setup Environment Variables

```bash
# Copy the Docker environment template
cp .env.docker .env.docker.local

# Edit the environment file with your configuration
nano .env.docker.local
```

### 3. Start Development Environment

```bash
# Make scripts executable
chmod +x scripts/docker-dev.sh scripts/docker-prod.sh

# Start development environment
./scripts/docker-dev.sh start
```

The application will be available at:
- Frontend: http://localhost:5173
- Convex Backend: http://localhost:3210

## Environment Configuration

### Development (.env.docker.local)

```env
NODE_ENV=development
CONVEX_DEPLOYMENT=dev:jobblogg-docker
CONVEX_URL=http://localhost:3210
VITE_CONVEX_URL=http://localhost:3210

# Add your API keys
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key
RESEND_API_KEY=your_resend_key
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
```

### Production (.env.prod)

```env
NODE_ENV=production
CONVEX_DEPLOYMENT=prod:jobblogg
CONVEX_URL=https://your-domain.com/api
VITE_CONVEX_URL=https://your-domain.com/api

# Production API keys
VITE_CLERK_PUBLISHABLE_KEY=your_production_clerk_key
# ... other production keys
```

## Development Commands

```bash
# Start development environment
./scripts/docker-dev.sh start

# View logs
./scripts/docker-dev.sh logs

# View logs for specific service
./scripts/docker-dev.sh logs convex

# Restart services
./scripts/docker-dev.sh restart

# Rebuild services (after code changes)
./scripts/docker-dev.sh rebuild

# Stop environment
./scripts/docker-dev.sh stop

# Show service status
./scripts/docker-dev.sh status

# Clean up Docker resources
./scripts/docker-dev.sh cleanup
```

## Production Deployment

### 1. Setup SSL Certificates

```bash
# Create SSL directory
mkdir -p docker/nginx/ssl

# Generate self-signed certificate (for testing)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/nginx/ssl/key.pem \
  -out docker/nginx/ssl/cert.pem

# Or use Let's Encrypt (recommended for production)
# Install certbot and generate certificates
```

### 2. Create Production Environment File

```bash
cp .env.docker .env.prod
# Edit .env.prod with production values
```

### 3. Deploy to Production

```bash
# Deploy production environment
./scripts/docker-prod.sh deploy

# View production logs
./scripts/docker-prod.sh logs

# Update production (pull latest code and rebuild)
./scripts/docker-prod.sh update

# Create backup
./scripts/docker-prod.sh backup

# Stop production
./scripts/docker-prod.sh stop
```

## Architecture

The Docker setup includes:

### Services

1. **Convex Backend** (`convex:3210`)
   - Handles all backend logic
   - Database operations
   - API endpoints
   - Real-time subscriptions

2. **Frontend** (`frontend:5173`)
   - React/Vite application
   - Serves static assets
   - Client-side routing

3. **Nginx** (`nginx:80/443`) - Production only
   - Reverse proxy
   - SSL termination
   - Static file serving
   - Load balancing

### Volumes

- `convex_data_dev/prod`: Persistent storage for Convex data
- Source code mounts for development hot-reload

### Networks

- `jobblogg-dev-network`: Development network
- `jobblogg-prod-network`: Production network

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   sudo netstat -tulpn | grep :5173
   sudo netstat -tulpn | grep :3210
   
   # Stop conflicting services
   sudo systemctl stop apache2  # if Apache is running
   ```

2. **Permission issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   chmod +x scripts/*.sh
   ```

3. **Docker daemon not running**
   ```bash
   # Start Docker
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

4. **Environment variables not loaded**
   ```bash
   # Check if .env.docker.local exists
   ls -la .env.docker.local
   
   # Verify environment variables
   ./scripts/docker-dev.sh status
   ```

### Logs and Debugging

```bash
# View all logs
./scripts/docker-dev.sh logs

# View specific service logs
./scripts/docker-dev.sh logs convex
./scripts/docker-dev.sh logs frontend

# Follow logs in real-time
docker compose -f docker-compose.dev.yml logs -f

# Check container status
docker ps

# Execute commands in container
docker exec -it jobblogg-convex-dev bash
docker exec -it jobblogg-frontend-dev sh
```

## Monitoring

### Health Checks

The setup includes health checks for all services:

```bash
# Check service health
docker compose -f docker-compose.dev.yml ps

# Manual health check
curl http://localhost:5173/health
curl http://localhost:3210/health
```

### Resource Usage

```bash
# Monitor resource usage
docker stats

# View disk usage
docker system df

# Clean up unused resources
docker system prune -f
```

## Security Considerations

1. **Environment Variables**: Never commit `.env.docker.local` or `.env.prod`
2. **SSL Certificates**: Use proper SSL certificates in production
3. **Firewall**: Configure firewall to only allow necessary ports
4. **Updates**: Regularly update Docker images and dependencies
5. **Backups**: Regular backups of Convex data

## Performance Optimization

1. **Resource Limits**: Set appropriate CPU/memory limits in Docker Compose
2. **Caching**: Nginx caching for static assets
3. **Compression**: Gzip compression enabled
4. **Health Checks**: Proper health checks for service monitoring

## Next Steps

1. Setup monitoring (Prometheus/Grafana)
2. Configure log aggregation (ELK stack)
3. Setup CI/CD pipeline
4. Configure automated backups
5. Setup domain and SSL certificates
