name: 🔒 Security Scan

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security scan daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '20'

jobs:
  # 🔍 Dependency Security Scan
  dependency-scan:
    name: 🔍 Dependency Security
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 📥 Install dependencies
      run: npm ci
      
    - name: 🔍 Run npm audit
      run: |
        echo "🔍 Running npm security audit..."
        npm audit --audit-level=high --json > audit-results.json || true
        
        # Parse and display results
        if [ -s audit-results.json ]; then
          echo "📊 Security Audit Results:"
          cat audit-results.json | jq -r '.vulnerabilities | to_entries[] | "\(.key): \(.value.severity) - \(.value.title)"' || true
        fi
        
        # Fail if high or critical vulnerabilities found
        npm audit --audit-level=high
        
    - name: 📊 Upload audit results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: security-audit-results
        path: audit-results.json
        retention-days: 30

  # 🔒 Secret Detection
  secret-scan:
    name: 🔒 Secret Detection
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔍 Scan for secrets
      run: |
        echo "🔍 Scanning for potential secrets..."
        
        # Define patterns to search for
        PATTERNS=(
          "api[_-]?key\s*=\s*['\"][^'\"]{20,}['\"]"
          "secret[_-]?key\s*=\s*['\"][^'\"]{10,}['\"]"
          "private[_-]?key\s*=\s*['\"][^'\"]{20,}['\"]"
          "access[_-]?key\s*=\s*['\"][^'\"]{10,}['\"]"
          "auth[_-]?token\s*=\s*['\"][^'\"]{20,}['\"]"
          "bearer[_-]?token\s*=\s*['\"][^'\"]{20,}['\"]"
          "jwt[_-]?token\s*=\s*['\"][^'\"]{20,}['\"]"
          "refresh[_-]?token\s*=\s*['\"][^'\"]{20,}['\"]"
        )
        
        FOUND_SECRETS=false
        
        for pattern in "${PATTERNS[@]}"; do
          echo "🔍 Checking for pattern: $pattern"
          
          # Search in source files, excluding test files, examples, and localization
          if grep -r -E "$pattern" src/ \
            --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" \
            --exclude-dir=node_modules \
            | grep -v -E "(placeholder|example|test|mock|demo|sample|localization)" \
            | grep -v -E "//.*" \
            | grep -v -E "/\*.*\*/" \
            | grep -v -E "import\.meta\.env\." \
            | grep -v -E "process\.env\." ; then
            
            echo "⚠️ Potential secret found with pattern: $pattern"
            FOUND_SECRETS=true
          fi
        done
        
        if [ "$FOUND_SECRETS" = true ]; then
          echo "❌ Potential secrets detected in source code!"
          echo "Please review the findings above and ensure no real secrets are committed."
          exit 1
        else
          echo "✅ No secrets detected in source code"
        fi
        
    - name: 🔍 Check environment files
      run: |
        echo "🔍 Checking for environment files..."
        
        ENV_FILES=(
          ".env"
          ".env.local"
          ".env.production"
          ".env.staging"
          ".env.development"
        )
        
        for file in "${ENV_FILES[@]}"; do
          if [ -f "$file" ]; then
            echo "⚠️ Environment file found: $file"
            echo "Ensure this file is in .gitignore and contains no real secrets"
          fi
        done
        
        echo "✅ Environment file check completed"

  # 🛡️ Code Security Analysis
  code-security:
    name: 🛡️ Code Security
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📦 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 📥 Install dependencies
      run: npm ci
      
    - name: 🔍 Check for dangerous patterns
      run: |
        echo "🔍 Scanning for dangerous code patterns..."
        
        DANGEROUS_PATTERNS=(
          "eval\("
          "innerHTML\s*="
          "dangerouslySetInnerHTML"
          "document\.write"
          "window\.location\s*="
          "localStorage\.setItem.*['\"]password['\"]"
          "sessionStorage\.setItem.*['\"]password['\"]"
          "console\.log.*['\"][^'\"]*password[^'\"]*['\"]"
          "console\.log.*['\"][^'\"]*secret[^'\"]*['\"]"
        )
        
        FOUND_ISSUES=false
        
        for pattern in "${DANGEROUS_PATTERNS[@]}"; do
          echo "🔍 Checking for dangerous pattern: $pattern"
          
          if grep -r -E "$pattern" src/ \
            --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" \
            --exclude-dir=node_modules; then
            
            echo "⚠️ Potentially dangerous pattern found: $pattern"
            FOUND_ISSUES=true
          fi
        done
        
        if [ "$FOUND_ISSUES" = true ]; then
          echo "⚠️ Potentially dangerous patterns detected!"
          echo "Please review the findings above for security implications."
          # Don't fail the build for warnings, just alert
        else
          echo "✅ No dangerous patterns detected"
        fi
        
    - name: 🔍 Check HTTPS usage
      run: |
        echo "🔍 Checking for HTTP usage (should use HTTPS)..."
        
        if grep -r "http://" src/ \
          --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" \
          --exclude-dir=node_modules \
          | grep -v "localhost" \
          | grep -v "127.0.0.1"; then
          
          echo "⚠️ HTTP URLs found - consider using HTTPS for security"
        else
          echo "✅ No insecure HTTP URLs detected"
        fi

  # 📊 Security Summary
  security-summary:
    name: 📊 Security Summary
    runs-on: ubuntu-latest
    needs: [dependency-scan, secret-scan, code-security]
    if: always()
    
    steps:
    - name: 📊 Generate security summary
      run: |
        echo "## 🔒 Security Scan Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ needs.dependency-scan.result }}" == "success" ]; then
          echo "✅ **Dependency Scan**: No high-risk vulnerabilities" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Dependency Scan**: Vulnerabilities detected" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.secret-scan.result }}" == "success" ]; then
          echo "✅ **Secret Detection**: No secrets detected" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Secret Detection**: Potential secrets found" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.code-security.result }}" == "success" ]; then
          echo "✅ **Code Security**: No dangerous patterns" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ **Code Security**: Review recommended" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "🕐 **Scan Time**: $(date)" >> $GITHUB_STEP_SUMMARY
        echo "🔗 **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        
        # Set overall status
        if [ "${{ needs.dependency-scan.result }}" == "success" ] && [ "${{ needs.secret-scan.result }}" == "success" ]; then
          echo "🎉 **Overall Status**: SECURE ✅" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ **Overall Status**: REVIEW REQUIRED" >> $GITHUB_STEP_SUMMARY
        fi
