# Convex Backend Dockerfile
FROM node:20-alpine

# Install necessary packages
RUN apk add --no-cache \
    git \
    curl \
    bash

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Install Convex CLI globally
RUN npm install -g convex@latest

# Copy Convex functions and schema
COPY convex/ ./convex/
COPY tsconfig.json ./
COPY tsconfig.node.json ./

# Create a non-root user
RUN addgroup -g 1001 -S convex && \
    adduser -S convex -u 1001

# Change ownership of the app directory
RUN chown -R convex:convex /app

# Switch to non-root user
USER convex

# Expose port (Convex typically uses 3210 for local dev)
EXPOSE 3210

# Health check with better error handling and logging
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3210/health || (echo "Health check failed for Convex service" && exit 1)

# Default command
CMD ["npx", "convex", "dev", "--url", "0.0.0.0:3210"]
