# 🚀 JobbLogg CI/CD Quick Start Guide

**Status**: ✅ Pipeline implementert - Klar for server setup og testing

---

## 📋 Hva er implementert

### ✅ **GitHub Actions Workflows**
- **deploy.yml**: Hovedworkflow med staging → production pipeline
- **test.yml**: PR testing og code quality checks
- **security.yml**: Security scanning og dependency audit

### ✅ **Docker Konfigurasjoner**
- **docker-compose.staging.yml**: Staging environment setup
- **docker-compose.prod.yml**: Production environment (eksisterende)

### ✅ **Server Scripts**
- **setup-ci-cd.sh**: Automatisk server setup
- **health-check.sh**: Deployment verification
- **backup.sh**: Backup creation
- **rollback.sh**: Emergency rollback

### ✅ **Dokumentasjon**
- **GITHUB_ACTIONS_SETUP.md**: Komplett setup guide
- **CI_CD_QUICK_START.md**: Denne guiden

---

## 🎯 Neste Steg - Server Setup

### 1. **K<PERSON><PERSON>r Server Setup Script**
```bash
# SSH til serveren som root
ssh root@************

# Naviger til prosjekt directory
cd /opt

# Klon repository (hvis ikke allerede gjort)
git clone https://github.com/djrobbieh/JobbLogg.git jobblogg
cd jobblogg

# Kjør setup script
chmod +x scripts/setup-ci-cd.sh
./scripts/setup-ci-cd.sh
```

**Dette scriptet vil:**
- ✅ Opprette `github-actions` bruker
- ✅ Sette opp SSH directory
- ✅ Konfigurere Docker permissions
- ✅ Opprette deployment scripts
- ✅ Sette opp monitoring service
- ✅ Konfigurere log rotation

### 2. **Generer SSH Nøkler**
```bash
# På din lokale maskin
ssh-keygen -t ed25519 -C "github-actions-staging" -f ~/.ssh/github-actions-staging
ssh-keygen -t ed25519 -C "github-actions-production" -f ~/.ssh/github-actions-production

# Kopier public keys til server
ssh-copy-id -i ~/.ssh/github-actions-staging.pub github-actions@************
ssh-copy-id -i ~/.ssh/github-actions-production.pub github-actions@************

# Test SSH tilgang
ssh -i ~/.ssh/github-actions-staging github-actions@************ 'whoami'
```

### 3. **Konfigurer GitHub Secrets**

Gå til GitHub Repository → Settings → Secrets and variables → Actions

#### Repository Secrets:
```
STAGING_SSH_KEY     = [innhold av ~/.ssh/github-actions-staging]
PRODUCTION_SSH_KEY  = [innhold av ~/.ssh/github-actions-production]
STAGING_HOST        = ************
PRODUCTION_HOST     = ************
```

#### Environment Secrets (staging):
```
CONVEX_URL_STAGING
VITE_CONVEX_URL_STAGING
VITE_CLERK_PUBLISHABLE_KEY_STAGING
VITE_STRIPE_PUBLISHABLE_KEY_STAGING
STRIPE_SECRET_KEY_STAGING
STRIPE_WEBHOOK_SECRET_STAGING
RESEND_API_KEY_STAGING
```

#### Environment Secrets (production):
```
CONVEX_URL
VITE_CONVEX_URL
VITE_CLERK_PUBLISHABLE_KEY
VITE_STRIPE_PUBLISHABLE_KEY
STRIPE_SECRET_KEY
STRIPE_WEBHOOK_SECRET
RESEND_API_KEY
```

---

## 🧪 Testing Pipeline

### 1. **Test Lokal Build**
```bash
# Sjekk at alt bygger lokalt
npm run build
npm run lint
npm run type-check
npm run validate:imports
```

### 2. **Test PR Workflow**
```bash
# Opprett feature branch
git checkout -b test-ci-cd-pipeline

# Gjør en liten endring
echo "// CI/CD test" >> src/App.tsx

# Push og opprett PR
git add .
git commit -m "test: CI/CD pipeline testing"
git push origin test-ci-cd-pipeline
```

**Forventet resultat:**
- ✅ Test workflow kjører automatisk
- ✅ Code quality checks passerer
- ✅ Security scan kjører
- ✅ PR kommentar med build info

### 3. **Test Staging Deployment**
```bash
# Merge PR til main branch
# Dette vil automatisk trigge staging deployment

# Verifiser staging deployment
curl -f https://staging.jobblogg.no/api/health
```

### 4. **Test Production Deployment**
```bash
# Gå til GitHub Actions
# Kjør "Deploy JobbLogg" workflow manuelt
# Velg "production" environment
# Godkjenn deployment

# Verifiser production deployment
curl -f https://jobblogg.no/api/health
```

---

## 🔍 Monitoring og Debugging

### **Sjekk Deployment Status**
```bash
# SSH til server
ssh github-actions@************

# Sjekk container status
cd /opt/jobblogg
docker-compose -f docker-compose.staging.yml ps
docker-compose -f docker-compose.prod.yml ps

# Sjekk logs
docker-compose -f docker-compose.staging.yml logs -f --tail=50
```

### **Health Checks**
```bash
# Staging health
curl -f https://staging.jobblogg.no/api/health

# Production health
curl -f https://jobblogg.no/api/health

# Detailed health (hvis implementert)
curl -f https://jobblogg.no/api/health/detailed
```

### **Manual Rollback (hvis nødvendig)**
```bash
# SSH til server
ssh github-actions@************

# Kjør rollback script
/opt/jobblogg/scripts/deployment/rollback.sh production
```

---

## 🚨 Troubleshooting

### **SSH Problemer**
```bash
# Test SSH tilgang
ssh -v github-actions@************

# Sjekk SSH nøkler på server
ssh github-actions@************ 'cat ~/.ssh/authorized_keys'

# Sjekk SSH agent lokalt
ssh-add -l
```

### **Docker Problemer**
```bash
# Sjekk Docker status
ssh github-actions@************ 'docker ps'
ssh github-actions@************ 'docker system df'

# Cleanup hvis nødvendig
ssh github-actions@************ 'docker system prune -f'
```

### **Environment Variable Problemer**
```bash
# Sjekk environment filer
ssh github-actions@************ 'ls -la /opt/jobblogg/.env*'

# Test Docker Compose config
ssh github-actions@************ 'cd /opt/jobblogg && docker-compose -f docker-compose.staging.yml config'
```

---

## ✅ Success Criteria

### **Pipeline Setup Complete When:**
- [ ] Server setup script kjørt uten feil
- [ ] SSH nøkler konfigurert og testet
- [ ] GitHub Secrets lagt til
- [ ] PR workflow kjører og passerer
- [ ] Staging deployment fungerer automatisk
- [ ] Production deployment fungerer med manual approval
- [ ] Health checks passerer for begge environments
- [ ] Rollback mekanisme testet

### **Ready for Production When:**
- [ ] Alle tests passerer konsistent
- [ ] Staging environment fungerer stabilt
- [ ] Production deployment testet grundig
- [ ] Monitoring og alerting fungerer
- [ ] Team er trent på pipeline bruk
- [ ] Rollback prosedyrer er dokumentert og testet

---

## 🎉 Neste Fase

Når CI/CD pipeline er fullt operativ:

1. **Monitoring Setup** - Grafana/Prometheus
2. **Advanced Features** - Blue-green deployment
3. **Performance Testing** - Load testing i pipeline
4. **Security Enhancements** - SAST/DAST scanning

---

*CI/CD Quick Start Guide - Klar for implementering!*
