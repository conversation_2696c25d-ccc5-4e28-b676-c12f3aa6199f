#!/bin/bash

# JobbLogg Production Deployment Script - Fixed Version
# This script handles the port conflict issue and ensures clean deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🎯 Starting fixed production deployment..."
print_status "=========================================="

# Step 1: Comprehensive cleanup
print_status "Step 1: Comprehensive container cleanup"
print_status "======================================="

# Stop all JobbLogg related containers (multiple naming patterns)
containers_to_stop=(
    "jobblogg-frontend-production"
    "jobblogg-frontend-prod" 
    "jobblogg-convex-production"
    "jobblogg-convex-prod"
    "jobblogg-convex-prod-placeholder"
    "mock-convex-production-new"
    "mock-convex-staging-new"
)

for container in "${containers_to_stop[@]}"; do
    if docker ps -a --format "{{.Names}}" | grep -q "^${container}$"; then
        print_status "Stopping and removing container: $container"
        docker stop "$container" 2>/dev/null || true
        docker rm "$container" 2>/dev/null || true
    fi
done

# Remove networks
networks_to_remove=(
    "jobblogg-prod-network"
    "jobblogg-production-network" 
    "jobblogg_jobblogg-network"
)

for network in "${networks_to_remove[@]}"; do
    if docker network ls --format "{{.Name}}" | grep -q "^${network}$"; then
        print_status "Removing network: $network"
        docker network rm "$network" 2>/dev/null || true
    fi
done

# Step 2: Check port availability
print_status "Step 2: Checking port availability"
print_status "=================================="

critical_ports=(5174 3210 3211)
for port in "${critical_ports[@]}"; do
    if lsof -i :$port >/dev/null 2>&1; then
        print_warning "Port $port is in use, attempting to free it..."
        
        # Find and display what's using the port
        print_status "Port $port usage:"
        lsof -i :$port 2>/dev/null || netstat -tlnp 2>/dev/null | grep ":$port " || true
        
        # Kill processes using the port (be careful!)
        pids=$(lsof -ti :$port 2>/dev/null || true)
        if [ -n "$pids" ]; then
            print_warning "Killing processes on port $port: $pids"
            kill -9 $pids 2>/dev/null || true
            sleep 2
        fi
    else
        print_success "Port $port is available"
    fi
done

# Step 3: Docker system cleanup
print_status "Step 3: Docker system cleanup"
print_status "============================="

print_status "Removing dangling containers..."
docker container prune -f 2>/dev/null || true

print_status "Removing unused networks..."
docker network prune -f 2>/dev/null || true

# Step 4: Verify environment
print_status "Step 4: Environment verification"
print_status "==============================="

if [[ ! -f ".env.production" ]]; then
    print_error "Missing .env.production file!"
    print_status "Creating from template..."
    cp .env.production.example .env.production 2>/dev/null || true
fi

# Step 5: Deploy with Docker Compose
print_status "Step 5: Starting deployment"
print_status "==========================="

print_status "Using compose files: -f docker-compose.yml -f docker-compose.prod.yml"

# Export environment variables
if [[ -f ".env.production" ]]; then
    print_status "Loading production environment variables..."
    export $(grep -v '^#' .env.production | xargs) 2>/dev/null || true
fi

# Build and start services
print_status "Building and starting services..."
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Step 6: Wait and verify
print_status "Step 6: Deployment verification"
print_status "==============================="

print_status "Waiting for services to start..."
sleep 15

print_status "Checking service status..."
docker compose -f docker-compose.yml -f docker-compose.prod.yml ps

# Check if frontend is responding
print_status "Testing frontend connectivity..."
if curl -f http://localhost:5174/ >/dev/null 2>&1; then
    print_success "Frontend is responding on port 5174"
else
    print_warning "Frontend not responding yet, checking logs..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=20 frontend
fi

# Final status
print_status "Step 7: Final status"
print_status "==================="

print_status "Current containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(jobblogg|convex)" || echo "No JobbLogg containers found"

print_status "Port usage check:"
for port in 5174 3210 3211; do
    if lsof -i :$port >/dev/null 2>&1; then
        echo "Port $port: IN USE"
        docker ps --format "table {{.Names}}\t{{.Ports}}" | grep ":$port->" || true
    else
        echo "Port $port: FREE"
    fi
done

print_success "🎉 Production deployment completed!"
print_status ""
print_status "Next steps:"
print_status "- Check https://jobblogg.no to verify the site is working"
print_status "- Monitor logs: docker compose -f docker-compose.yml -f docker-compose.prod.yml logs -f"
print_status "- Run health check: ./scripts/health-check.sh prod"
