# JobbLogg Architecture Review - Revision 2

## 📋 Detaljert Arkitekturplan og Anbefalinger

**Dato**: August 2025  
**Basert på**: REV_1 funn og anbefalinger

---

## 🏗️ Repo og Utviklermiljø

**Q: Monorepo eller flere repo, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Node versjon, byggverktøy, testverktøy?**

**A: Monorepo med moderne toolchain**
- **Repo**: Monorepo (frontend + backend i samme repo)
- **Pakkehåndterer**: npm (beholde eksisterende)
- **Node versjon**: Node 20 LTS (som nåværende)
- **Byggverktøy**: Vite (frontend), Convex CLI (backend)
- **Testverktøy**: Vitest + React Testing Library + Playwright (E2E)

---

## 🌿 Branchestrategi og Release

**Q: Hovedgren, bruk av feature branches, tagging av releaser, ønsket releasefrekvens?**

**A: GitHub Flow med semantic versioning**
- **<PERSON><PERSON><PERSON>**: `main` (beskyttet)
- **Feature branches**: `feature/beskrivende-navn`
- **Tagging**: Semantic versioning (v1.2.3)
- **Releasefrekvens**: Ukentlig til produksjon, daglig til staging
- **Hotfixes**: Direkte til main med umiddelbar deploy

---

## 🔄 CI Leverandør

**Q: GitHub Actions eller annet, om CI skal være eneste vei til staging og prod?**

**A: GitHub Actions med streng kontroll**
- **CI**: GitHub Actions
- **Deployment**: Kun via CI til staging og prod
- **Lokal deploy**: Kun til dev miljø
- **Manual approval**: Påkrevd for prod deployment

---

## 🌐 Miljøer og Domener

**Q: Eksakte URLer for dev, staging, prod, domener, TLS, reverse proxy?**

**A: Strukturerte domener med Cloudflare**
- **Dev**: `http://localhost:5173` (lokal)
- **Staging**: `https://staging.jobblogg.no`
- **Prod**: `https://jobblogg.no`
- **DNS**: Cloudflare (med proxy)
- **TLS**: Cloudflare SSL + Hetzner Caddy
- **Reverse proxy**: Caddy på Hetzner

---

## 📊 Convex

**Q: Navn/ID for prosjekter, deploy keys, schema migreringer?**

**A: Separerte Convex deployments**
- **Dev**: `dev:enchanted-quail-174` (eksisterende)
- **Staging**: `staging:jobblogg-staging` (ny)
- **Prod**: `prod:jobblogg-production` (eksisterende)
- **Deploy keys**: GitHub Secrets
- **Migreringer**: Obligatorisk i CI pipeline før deployment
- **Schema**: Automatisk validering mot breaking changes

---

## 🖥️ Frontend Hosting

**Q: Hvor frontend skal serve, CDN, cache policy, Hetzner eller edge?**

**A: Hetzner med Cloudflare CDN**
- **Hosting**: Hetzner server (Docker containers)
- **CDN**: Cloudflare (gratis tier)
- **Cache**: 1 år for assets, 1 time for HTML
- **Build**: På Hetzner (ikke edge platform)
- **Static assets**: Served via Cloudflare

---

## 🔐 Autentisering

**Q: Clerk tenants, webhook endepunkter, innloggingstyper?**

**A: Separerte Clerk tenants**
- **Dev**: `loved-dory-86.clerk.accounts.dev` (eksisterende)
- **Staging**: `staging-jobblogg.clerk.accounts.dev` (ny)
- **Prod**: `jobblogg.clerk.accounts.dev` (eksisterende)
- **Webhooks**: 
  - Staging: `https://staging.jobblogg.no/api/clerk/webhook`
  - Prod: `https://jobblogg.no/api/clerk/webhook`
- **Innlogging**: Email/passord + Google OAuth

---

## 💳 Betaling

**Q: Stripe kontoer, produkter, webhooks, Vipps/Apple Pay?**

**A: Stripe test/live separation**
- **Dev/Staging**: Stripe test mode
- **Prod**: Stripe live mode
- **Produkter**: Basic (299 NOK), Professional (999 NOK), Enterprise (2999 NOK)
- **Webhooks**:
  - Staging: `https://staging.jobblogg.no/api/stripe/webhook`
  - Prod: `https://jobblogg.no/api/stripe/webhook`
- **Betalingsmetoder**: Kort først, Vipps i fase 2

---

## 📧 Epost og Varsling

**Q: Epostleverandør, avsenderdomene, push varsler?**

**A: Resend med eget domene**
- **Leverandør**: Resend (beholde eksisterende)
- **Avsenderdomene**: `<EMAIL>`
- **Push varsler**: Implementer i fase 2
- **Templates**: HTML templates i Convex

---

## 💾 Lagring

**Q: R2 eller S3, bucket navn, private/offentlige filer?**

**A: Cloudflare R2 (kostnadseffektivt)**
- **Tjeneste**: Cloudflare R2
- **Buckets**:
  - `jobblogg-dev-files`
  - `jobblogg-staging-files`
  - `jobblogg-prod-files`
- **Private**: Prosjektbilder, dokumenter
- **Offentlige**: Profilbilder, logoer (via CDN)

---

## 🔑 Secrets og Miljøvariabler

**Q: Full liste over variabler, offentlige/hemmelige, hvor lagre?**

**A: Strukturert secrets management**

**Offentlige (klient)**:
- `VITE_CONVEX_URL`
- `VITE_CLERK_PUBLISHABLE_KEY`
- `VITE_STRIPE_PUBLISHABLE_KEY`
- `VITE_GOOGLE_MAPS_API_KEY`

**Hemmelige (server)**:
- `CONVEX_DEPLOY_KEY`
- `CLERK_SECRET_KEY`
- `STRIPE_SECRET_KEY`
- `STRIPE_WEBHOOK_SECRET`
- `RESEND_API_KEY`
- `R2_ACCESS_KEY_ID`
- `R2_SECRET_ACCESS_KEY`

**Lagring**:
- **CI**: GitHub Secrets
- **Hetzner**: Docker secrets eller encrypted files

---

## 🧪 Test og Kvalitet

**Q: Minimumskrav for tester, røyktest, PR previews?**

**A: Lagdelt testing strategi**
- **Unit tests**: 80% coverage påkrevd
- **Integration tests**: Kritiske user flows
- **E2E tests**: Registrering, innlogging, prosjektopprettelse
- **Røyktest**: Automatisk etter deploy (health checks)
- **PR previews**: Implementer med isolerte backends

---

## 🚩 Feature Toggles

**Q: Config fil eller tjeneste, første toggles?**

**A: Enkel config-fil løsning**
- **Implementering**: JSON config fil i repo
- **Første toggles**:
  - `ENABLE_STRIPE_PAYMENTS`
  - `ENABLE_TEAM_INVITATIONS`
  - `ENABLE_PROJECT_SHARING`
  - `ENABLE_PUSH_NOTIFICATIONS`

---

## 📊 Observabilitet

**Q: Logging, metrikker, verktøy, alarm terskler?**

**A: Strukturert observabilitet**
- **Logging**: Structured JSON logs via Winston
- **Metrikker**: Custom metrics i Convex
- **Verktøy**: Grafana + Prometheus (self-hosted)
- **Alarm terskler**:
  - Error rate > 1%
  - Response time > 2s
  - Deployment failures

---

## 💾 Backup og Gjenoppretting

**Q: Backup frekvens, lagring, restore testing?**

**A: Automatisk backup strategi**
- **Database**: Daglig Convex backup (automatisk)
- **Filer**: Daglig R2 backup til annen region
- **Oppbevaring**: 30 dager daglig, 12 måneder ukentlig
- **Restore test**: Månedlig til staging miljø

---

## 🔒 Tilgang og Sikkerhet

**Q: MFA, tilgang i dag, prod deploy godkjenning?**

**A: Streng tilgangskontroll**
- **MFA**: Påkrevd på alle tjenester
- **Nåværende tilgang**: djrobbieh (owner)
- **Prod deploy**: Krever manual approval fra owner
- **Audit log**: All prod tilgang logges

---

## ⏪ Rollback Preferanse

**Q: Forrige bygg eller fremoverrettet fiks, nedetid?**

**A: Hybrid tilnærming**
- **Mindre bugs**: Fremoverrettet fiks
- **Kritiske feil**: Rollback til forrige versjon
- **Nedetid**: Maksimalt 5 minutter
- **Database**: Kun fremoverrettede endringer

---

## 📊 Data i Staging

**Q: Pseudonymiserte data, seed script, typiske datasett?**

**A: Realistiske testdata**
- **Data**: Pseudonymiserte produksjonsdata
- **Seed script**: Automatisk generering av testprosjekter
- **Datasett**:
  - 10 test-bedrifter
  - 50 test-prosjekter
  - 200 test-meldinger

---

## 🖥️ Hetzner Infrastruktur

**Q: Maskintype, OS, container, reverse proxy, brannmur?**

**A: Moderne container setup**
- **Maskin**: CPX31 (4 vCPU, 8GB RAM)
- **OS**: Ubuntu 22.04 LTS
- **Container**: Docker + Docker Compose
- **Reverse proxy**: Caddy (automatisk HTTPS)
- **Brannmur**: UFW (22, 80, 443 åpen)
- **Logging**: Logrotate (30 dager)

---

## ⚖️ Juridiske Krav

**Q: GDPR krav, datalagringssted, tilgangslogging?**

**A: GDPR compliance**
- **Datalagringssted**: EU (Convex Frankfurt, R2 EU)
- **Persondata**: Minimering og pseudonymisering
- **Tilgangslogging**: All admin tilgang logges
- **Slettingsrett**: Automatisk anonymisering
- **Dataportabilitet**: Export funksjonalitet

---

## 🎯 Implementeringsplan

### Fase 1 (Uke 1-2): Sikkerhet
1. Fjern eksponerte secrets
2. Implementer GitHub Secrets
3. Sett opp MFA på alle tjenester

### Fase 2 (Uke 3-4): CI/CD
1. GitHub Actions workflow
2. Staging miljø på Hetzner
3. Automatisk testing

### Fase 3 (Uke 5-6): Observabilitet
1. Strukturert logging
2. Metrikker og alerting
3. Backup strategi

### Fase 4 (Uke 7-8): Optimalisering
1. PR previews
2. Feature toggles
3. Performance monitoring

---

*Neste steg: Godkjenning av plan og start på Fase 1*
