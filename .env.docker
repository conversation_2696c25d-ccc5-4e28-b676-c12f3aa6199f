# Docker Environment Configuration for JobbLogg
# Copy this file to .env.docker.local and customize for your VPS

# Environment
NODE_ENV=development

# Convex Configuration
CONVEX_DEPLOYMENT=dev:jobblogg-docker
CONVEX_URL=http://localhost:3210
VITE_CONVEX_URL=http://localhost:3210

# Clerk Authentication
# Replace with your actual Clerk publishable key
VITE_CLERK_PUBLISHABLE_KEY=pk_test_bG92ZWQtZG9yeS04Ni5jbGVyay5hY2NvdW50cy5kZXYk

# Google Maps API (Optional)
# Get your API key from: https://console.cloud.google.com/apis/credentials
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs

# Resend Email Service
# Get your API key from: https://resend.com/api-keys
RESEND_API_KEY=re_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL

# Stripe Payment Integration
# Get your keys from: https://dashboard.stripe.com/apikeys
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51QuHVWRqXwHRnsDwFyefP4DJfEDG9Ti42UkWO7Y5nWmSDZbZtVLWfgDmmAP3YnYYb8905qIhtDDB8UUPLDjaUk9F00snevRBNh
STRIPE_SECRET_KEY=sk_test_51QuHVWRqXwHRnsDwtLPJ2Qd310QWPUvfvYKmxE4WPmC6ERPHCGfkdKgZp9xNZs3uPhUzGKQsmqytsgBdnXEClv3u00sKnCLi9T
STRIPE_WEBHOOK_SECRET=whsec_05c94535d706fbf2ac6105f0bd4967e5acabdf1ef2ba6135e1c6ac6f346b93e4

# Docker Network Configuration
DOCKER_NETWORK=jobblogg-network

# SSL Configuration (for production)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
